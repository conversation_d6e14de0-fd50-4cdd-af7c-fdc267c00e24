# 📅 Agendador de Revisões - Documentação

## Visão Geral

O **RevisionSchedulerModal** é uma janela modal avançada que permite agendar revisões de múltiplas formas, incluindo agendamento rápido, com IA, em lote e importação de dados externos.

## 🎯 Funcionalidades

### 1. Agendamento Rápido
- Formulário simples e direto
- Campos essenciais: título, matéria, data, horário, duração
- Seletor de prioridade visual
- Notas opcionais

### 2. Agendamento com IA
- Análise inteligente do conteúdo estudado
- Sugestões baseadas em:
  - Nível de dificuldade (escala 1-5)
  - Confiança no conteúdo (0-100%)
- Recomendações personalizadas
- Alternativas de datas

### 3. Agendamento em Lote
- Adicione múltiplas revisões de uma vez
- Interface drag & drop intuitiva
- Validação em tempo real
- Contador de revisões

### 4. Importação
- **Google Calendar**: Integração futura
- **Arquivo CSV**: Upload de planilhas
- **Templates Prontos**: 
  - ENEM Medicina
  - Residência Médica
  - Semestre Regular

## 🚀 Como Usar

### Acessar o Agendador
1. Na página `/revisoes`, clique no botão **"+ Nova Revisão"**
2. A janela modal do agendador será aberta

### Modo Rápido
```javascript
1. Preencha o título da revisão
2. Selecione a matéria
3. Escolha data e horário
4. Defina a duração
5. Selecione a prioridade (Baixa/Média/Alta)
6. Adicione notas (opcional)
7. Clique em "Agendar Revisão"
```

### Modo IA
```javascript
1. Descreva o conteúdo estudado em detalhes
2. Ajuste o nível de dificuldade (slider)
3. Informe sua confiança no conteúdo (%)
4. Clique em "Analisar com IA"
5. Revise as sugestões
6. Selecione uma data alternativa se desejar
7. Clique em "Agendar com IA"
```

### Modo Lote
```javascript
1. Preencha os dados da primeira revisão
2. Clique em "+ Adicionar Revisão" para mais itens
3. Complete todos os campos obrigatórios
4. Use o botão de lixeira para remover itens
5. Clique em "Agendar Todas"
```

### Modo Importação
```javascript
// CSV
1. Selecione "Arquivo CSV"
2. Arraste o arquivo ou clique para procurar
3. Visualize o preview dos dados
4. Clique em "Importar Revisões"

// Template
1. Selecione "Template Pronto"
2. Escolha um dos templates disponíveis
3. Clique em "Importar Revisões"
```

## 📊 Estrutura de Dados

### Revisão Agendada
```javascript
{
  id: 'revision-1234567890',
  title: 'Revisão de Anatomia Cardiovascular',
  subject: 'Anatomia',
  date: '2024-01-15',
  time: '14:00',
  duration: 60, // minutos
  priority: 'medium', // low, medium, high
  type: 'manual', // manual, ai-scheduled, batch, imported
  notes: 'Focar em válvulas cardíacas',
  createdAt: '2024-01-10T10:00:00Z',
  metadata: {
    // Dados adicionais conforme o tipo
  }
}
```

### Metadata por Tipo

#### AI Scheduled
```javascript
metadata: {
  difficulty: 3, // 1-5
  confidence: 70, // 0-100
  aiReason: 'Baseado na dificuldade média...'
}
```

#### Batch
```javascript
metadata: {
  batchId: 'batch-1234567890',
  batchSize: 5
}
```

#### Imported
```javascript
metadata: {
  source: 'csv', // csv, calendar, template
  originalFile: 'revisoes_janeiro.csv'
}
```

## 🎨 Interface

### Componentes Visuais
- **Header Gradiente**: Visual moderno com ícone
- **Navigation Pills**: Tabs para diferentes modos
- **Form Controls**: Inputs estilizados com ícones
- **Priority Selector**: Botões coloridos para prioridade
- **AI Analysis Card**: Visualização das sugestões
- **Batch Items**: Lista drag & drop
- **Import Preview**: Tabela de preview dos dados

### Cores e Temas
- **Primário**: #6366f1 (Indigo)
- **Secundário**: #8b5cf6 (Purple)
- **Success**: #22c55e
- **Warning**: #f59e0b
- **Danger**: #ef4444
- **Background**: #0f172a
- **Surface**: #1e293b

## 💾 Persistência

### LocalStorage
```javascript
// Salvar revisão
const revisions = JSON.parse(localStorage.getItem('revisions') || '[]')
revisions.push(newRevision)
localStorage.setItem('revisions', JSON.stringify(revisions))

// Carregar revisões
const revisions = JSON.parse(localStorage.getItem('revisions') || '[]')
```

### Eventos
```javascript
// Revisão agendada
emit('revision-scheduled', revision)

// Múltiplas revisões (batch/import)
emit('revision-scheduled', { 
  count: 5, 
  type: 'batch' 
})
```

## 🔧 Configuração

### Props
O componente não recebe props, é totalmente autônomo.

### Emits
- `close`: Fecha o modal
- `revision-scheduled`: Notifica sobre revisão(ões) agendada(s)

### Slots
Não utiliza slots.

## 📱 Responsividade

### Desktop (>768px)
- Layout em múltiplas colunas
- Todos os recursos disponíveis
- Preview lateral

### Mobile (<768px)
- Layout vertical
- Formulários adaptados
- Navegação simplificada
- Modal fullscreen

## 🚦 Estados

### Loading
- Durante análise de IA
- Durante importação de arquivos

### Validação
- Campos obrigatórios marcados
- Feedback visual em tempo real
- Botões desabilitados quando inválido

### Sucesso
- Alert de confirmação
- Modal fecha automaticamente
- Dados persistidos

## 🔍 Debug

### Console
```javascript
// Verificar revisões salvas
console.log(JSON.parse(localStorage.getItem('revisions')))

// Limpar todas as revisões
localStorage.removeItem('revisions')
```

### Vue DevTools
- Inspecionar estado do componente
- Verificar eventos emitidos
- Analisar computed properties

## 🎯 Casos de Uso

### 1. Estudante Regular
- Usa modo rápido para agendar revisões diárias
- Configura lembretes para provas

### 2. Preparação para Concurso
- Importa template de residência médica
- Ajusta datas conforme cronograma

### 3. Revisão Intensiva
- Usa modo lote para agendar semana completa
- Define prioridades por importância

### 4. Estudo Adaptativo
- Usa IA para sugestões personalizadas
- Ajusta baseado em performance

## 🛠️ Manutenção

### Adicionar Nova Matéria
```javascript
const subjects = ref([
  'Anatomia', 
  'Fisiologia',
  // Adicionar aqui
  'Nova Matéria'
])
```

### Novo Template
```javascript
const templates = ref([
  // ... templates existentes
  {
    id: 'custom',
    name: 'Meu Template',
    description: 'Descrição',
    icon: 'fas fa-star',
    revisions: 20
  }
])
```

### Ajustar Algoritmo IA
```javascript
// Em analyzeWithAI()
let daysToAdd
if (difficulty <= 2 && confidence >= 80) {
  daysToAdd = 3 // Ajustar valores
}
```

## 📈 Métricas

O footer do modal exibe:
- Número de revisões agendadas hoje
- Tempo até próxima revisão
- Link para visualizar agenda completa

---

**Desenvolvido para maximizar a eficiência do estudo com múltiplas opções de agendamento!** 🚀