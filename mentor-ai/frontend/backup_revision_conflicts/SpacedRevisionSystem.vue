<template>
  <div class="spaced-revision-system">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="revision-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Sistema de Revisões Espaçadas</h1>
            <p class="page-subtitle">Método científico de retenção de conhecimento</p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="openNewRevisionModal" class="action-button new-revision">
            <i class="fas fa-plus"></i>
            <span>Registrar Estudo / Nova Revisão</span>
          </button>
          <button @click="showAnalytics = true" class="action-button">
            <i class="fas fa-chart-line"></i>
            <span>Análise</span>
          </button>
          <div style="width: 1px; height: 40px; background: rgba(148, 163, 184, 0.2); margin: 0 0.5rem;"></div>
          <button @click="resetAndCreateSampleData" class="action-button demo-button" title="Criar dados de demonstração">
            <i class="fas fa-magic"></i>
            <span>Demo</span>
          </button>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon theory">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ theoryStudies.length }}</div>
            <div class="stat-label">Estudos Teóricos</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon contact">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ firstContacts.today }}</div>
            <div class="stat-label">Contatos Hoje</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon review">
            <i class="fas fa-redo"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ spacedRevisions.pending }}</div>
            <div class="stat-label">Revisões Pendentes</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon performance">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ averagePerformance }}%</div>
            <div class="stat-label">Desempenho Médio</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Theory Studies Section -->
      <section class="content-section theory-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-book"></i>
            Estudos Teóricos
          </h2>
          <span class="section-subtitle">Registre seus estudos e agende primeiro contato</span>
        </div>

        <div class="theory-list">
          <div v-for="study in recentTheoryStudies" :key="study.id" class="theory-card">
            <div class="theory-info">
              <h3>{{ study.title }}</h3>
              <p class="theory-subject">{{ study.subject }}</p>
              <div class="theory-meta">
                <span class="theory-date">
                  <i class="fas fa-calendar"></i>
                  {{ formatDate(study.date) }}
                </span>
                <span class="theory-difficulty" :class="study.difficulty">
                  <i class="fas fa-signal"></i>
                  {{ study.difficulty }}
                </span>
              </div>
            </div>
            <div class="theory-status">
              <div class="first-contact-date">
                <span class="label">Primeiro Contato:</span>
                <span class="date">{{ formatDate(study.firstContactDate) }}</span>
              </div>
              <div class="days-until">
                {{ getDaysUntilContact(study.firstContactDate) }}
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- First Contact Queue -->
      <section class="content-section contact-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-tasks"></i>
            Fila de Primeiro Contato
          </h2>
          <span class="section-subtitle">Questões agendadas para hoje</span>
        </div>

        <div class="contact-queue">
          <div v-for="contact in todayContacts" :key="contact.id" class="contact-card">
            <div class="contact-header">
              <h3>{{ contact.title }}</h3>
              <span class="contact-type">{{ contact.subject }}</span>
            </div>
            <div class="contact-info">
              <p class="theory-reference">
                Baseado em: {{ contact.theoryTitle }}
              </p>
              <div class="contact-meta">
                <span class="questions-count">
                  <i class="fas fa-list-ol"></i>
                  30 questões
                </span>
                <span class="estimated-time">
                  <i class="fas fa-clock"></i>
                  ~45 min
                </span>
              </div>
            </div>
            <button @click="startQuestions(contact)" class="start-button">
              <i class="fas fa-play"></i>
              Iniciar Questões
            </button>
          </div>
        </div>
      </section>

      <!-- Spaced Revisions Calendar -->
      <section class="content-section calendar-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-calendar-alt"></i>
            Calendário de Revisões
          </h2>
          <span class="section-subtitle">Revisões espaçadas baseadas em desempenho</span>
        </div>

        <div class="revision-calendar">
          <div class="calendar-header">
            <button @click="previousWeek" class="nav-button">
              <i class="fas fa-chevron-left"></i>
            </button>
            <h3>{{ currentWeekLabel }}</h3>
            <button @click="nextWeek" class="nav-button">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <div class="calendar-grid">
            <div v-for="day in weekDays" :key="day.date" class="calendar-day" :class="{ today: isToday(day.date) }">
              <div class="day-header">
                <span class="day-name">{{ day.name }}</span>
                <span class="day-date">{{ day.day }}</span>
              </div>
              <div class="day-revisions">
                <div v-for="revision in getRevisionsForDay(day.date)" :key="revision.id"
                     class="revision-item" :class="getRevisionClass(revision)"
                     @click="handleRevisionClick(revision)">
                  <span class="revision-time">{{ revision.startTime || formatTime(revision.time) }}</span>
                  <span class="revision-title">{{ revision.title }}</span>
                  <span class="revision-type">{{ revision.revisionType || revision.type }}</span>
                  <span class="revision-priority" :class="revision.priority?.toLowerCase()">{{ revision.priority }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Analytics -->
      <section class="content-section analytics-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-chart-bar"></i>
            Análise de Desempenho
          </h2>
          <span class="section-subtitle">Acompanhe sua evolução</span>
        </div>

        <div class="analytics-grid">
          <div class="analytics-card">
            <h3>Taxa de Retenção</h3>
            <div class="chart-container">
              <canvas ref="retentionChart"></canvas>
            </div>
          </div>

          <div class="analytics-card">
            <h3>Desempenho por Matéria</h3>
            <div class="subject-performance">
              <div v-for="subject in subjectPerformance" :key="subject.name" class="subject-item">
                <div class="subject-header">
                  <span class="subject-name">{{ subject.name }}</span>
                  <span class="subject-avg">{{ subject.average }}%</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: subject.average + '%' }"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="analytics-card">
            <h3>Intervalos Ótimos</h3>
            <div class="optimal-intervals">
              <div class="interval-info">
                <i class="fas fa-info-circle"></i>
                <p>Baseado em seu desempenho, seus intervalos ideais são:</p>
              </div>
              <div class="intervals-list">
                <div class="interval-item">
                  <span class="performance-range">50-55%</span>
                  <span class="interval-days">7 dias</span>
                </div>
                <div class="interval-item">
                  <span class="performance-range">55-60%</span>
                  <span class="interval-days">14 dias</span>
                </div>
                <div class="interval-item">
                  <span class="performance-range">60-65%</span>
                  <span class="interval-days">18 dias</span>
                </div>
                <div class="interval-item">
                  <span class="performance-range">65-75%</span>
                  <span class="interval-days">24 dias</span>
                </div>
                <div class="interval-item">
                  <span class="performance-range">75-80%</span>
                  <span class="interval-days">30 dias</span>
                </div>
                <div class="interval-item">
                  <span class="performance-range">&gt;80%</span>
                  <span class="interval-days">35 dias</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Questions Modal -->
    <QuestionsModal 
      v-if="showQuestionsModal"
      :contact="currentContact"
      @close="showQuestionsModal = false"
      @complete="completeQuestions"
    />

    <!-- Analytics Modal -->
    <AnalyticsModal
      v-if="showAnalytics"
      :data="analyticsData"
      @close="showAnalytics = false"
    />

    <!-- New Revision Modal -->
    <NewRevisionModal
      v-if="showNewRevisionModal"
      @close="showNewRevisionModal = false"
      @save="saveNewRevision"
      @saveStudy="saveTheoryStudy"
    />
    
    <!-- Debug info -->
    <div style="position: fixed; bottom: 10px; right: 10px; background: black; color: white; padding: 10px; z-index: 9999;">
      Modal State: {{ showNewRevisionModal }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';
import QuestionsModal from './QuestionsModal.vue';
import AnalyticsModal from './AnalyticsModal.vue';
import NewRevisionModal from './NewRevisionModal.vue';

export default {
  name: 'SpacedRevisionSystem',
  components: {
    QuestionsModal,
    AnalyticsModal,
    NewRevisionModal
  },
  setup() {
    const store = useStore();
    
    // Refs
    const showQuestionsModal = ref(false);
    const showAnalytics = ref(false);
    const showNewRevisionModal = ref(false);
    const currentContact = ref(null);
    const currentWeek = ref(new Date());
    const retentionChart = ref(null);

    // Computed
    const theoryStudies = computed(() => store.state.revisions.theoryStudies || []);
    const recentTheoryStudies = computed(() => theoryStudies.value.slice(0, 5));
    
    const firstContacts = computed(() => {
      const today = new Date().toDateString();
      const contacts = store.state.revisions.firstContacts || [];
      return {
        today: contacts.filter(c => new Date(c.date).toDateString() === today).length,
        total: contacts.length
      };
    });

    const spacedRevisions = computed(() => {
      const revisions = store.state.revisions.spacedRevisions || [];
      const today = new Date();
      today.setHours(23, 59, 59, 999); // Incluir revisões de hoje
      const pending = revisions.filter(r => !r.completed && new Date(r.date) <= today);
      return {
        pending: pending.length,
        total: revisions.length
      };
    });

    const averagePerformance = computed(() => {
      const performances = store.state.revisions.performances || [];
      if (performances.length === 0) return 0;
      const sum = performances.reduce((acc, p) => acc + p.percentage, 0);
      return Math.round(sum / performances.length);
    });

    const todayContacts = computed(() => {
      const today = new Date().toDateString();
      return (store.state.revisions.firstContacts || [])
        .filter(c => new Date(c.date).toDateString() === today);
    });

    const weekDays = computed(() => {
      const days = [];
      const startOfWeek = new Date(currentWeek.value);
      startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());

      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        days.push({
          date: date,
          name: date.toLocaleDateString('pt-BR', { weekday: 'short' }),
          day: date.getDate()
        });
      }
      return days;
    });

    const currentWeekLabel = computed(() => {
      const start = weekDays.value[0].date;
      const end = weekDays.value[6].date;
      return `${start.toLocaleDateString('pt-BR', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('pt-BR', { month: 'short', day: 'numeric' })}`;
    });

    const subjectPerformance = computed(() => {
      const performances = store.state.revisions.performances || [];
      const subjectMap = {};

      performances.forEach(p => {
        if (!subjectMap[p.subject]) {
          subjectMap[p.subject] = { total: 0, count: 0 };
        }
        subjectMap[p.subject].total += p.percentage;
        subjectMap[p.subject].count++;
      });

      return Object.entries(subjectMap).map(([name, data]) => ({
        name,
        average: Math.round(data.total / data.count)
      }));
    });

    const analyticsData = computed(() => ({
      performances: store.state.revisions.performances || [],
      studies: theoryStudies.value,
      revisions: store.state.revisions.spacedRevisions || []
    }));

    // Methods
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR');
    };

    const formatTime = (time) => {
      return new Date(time).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    };

    const getDaysUntilContact = (date) => {
      const today = new Date();
      const contactDate = new Date(date);
      const diffTime = contactDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return 'Atrasado';
      if (diffDays === 0) return 'Hoje';
      if (diffDays === 1) return 'Amanhã';
      return `Em ${diffDays} dias`;
    };

    const isToday = (date) => {
      return date.toDateString() === new Date().toDateString();
    };

    const getRevisionsForDay = (date) => {
      const dateStr = date.toDateString();
      return (store.state.revisions.spacedRevisions || [])
        .filter(r => {
          const revisionDate = new Date(r.date);
          return revisionDate.toDateString() === dateStr;
        })
        .sort((a, b) => {
          // Ordenar por hora de início se disponível
          if (a.startTime && b.startTime) {
            return a.startTime.localeCompare(b.startTime);
          }
          return 0;
        });
    };

    const getRevisionClass = (revision) => {
      if (revision.completed) return 'completed';
      if (new Date(revision.date) < new Date()) return 'overdue';
      return 'pending';
    };

    const previousWeek = () => {
      const newDate = new Date(currentWeek.value);
      newDate.setDate(newDate.getDate() - 7);
      currentWeek.value = newDate;
    };

    const nextWeek = () => {
      const newDate = new Date(currentWeek.value);
      newDate.setDate(newDate.getDate() + 7);
      currentWeek.value = newDate;
    };

    const saveTheoryStudy = (study) => {
      console.log('Saving theory study:', study);
      
      // Calculate first contact date based on difficulty
      const daysToAdd = study.difficulty === 'Fácil' ? 2 : 1;
      const firstContactDate = new Date(study.date);
      firstContactDate.setDate(firstContactDate.getDate() + daysToAdd);

      const theoryStudy = {
        ...study,
        id: Date.now(),
        firstContactDate: firstContactDate
      };

      store.dispatch('revisions/addTheoryStudy', theoryStudy);
      
      // Create first contact
      const firstContact = {
        id: Date.now() + 1,
        theoryId: theoryStudy.id,
        theoryTitle: theoryStudy.title,
        title: `Questões - ${theoryStudy.title}`,
        subject: theoryStudy.subject,
        date: firstContactDate,
        completed: false
      };

      store.dispatch('revisions/addFirstContact', firstContact);
      
      // Close modal
      showNewRevisionModal.value = false;
      
      // Show success message
      alert(`Estudo registrado! Primeiro contato agendado para ${firstContactDate.toLocaleDateString('pt-BR')}`);
      
      console.log('Theory study saved successfully');
    };

    const startQuestions = (contact) => {
      currentContact.value = contact;
      showQuestionsModal.value = true;
    };

    const completeQuestions = (result) => {
      // Calculate next revision based on performance
      const percentage = (result.correct / result.total) * 100;
      let daysToNext;

      if (percentage <= 50) daysToNext = 2;
      else if (percentage <= 55) daysToNext = 7;
      else if (percentage <= 60) daysToNext = 14;
      else if (percentage <= 65) daysToNext = 18;
      else if (percentage <= 75) daysToNext = 24;
      else if (percentage <= 80) daysToNext = 30;
      else daysToNext = 35;

      const nextRevisionDate = new Date();
      nextRevisionDate.setDate(nextRevisionDate.getDate() + daysToNext);

      // Save performance
      const performance = {
        id: Date.now(),
        contactId: currentContact.value.id,
        date: new Date(),
        total: result.total,
        correct: result.correct,
        percentage: Math.round(percentage),
        subject: currentContact.value.subject
      };

      store.dispatch('revisions/addPerformance', performance);

      // Create next revision
      const nextRevision = {
        id: Date.now() + 1,
        theoryId: currentContact.value.theoryId,
        title: currentContact.value.title,
        subject: currentContact.value.subject,
        date: nextRevisionDate,
        lastPerformance: Math.round(percentage),
        completed: false
      };

      store.dispatch('revisions/addSpacedRevision', nextRevision);

      // Mark contact as completed
      store.dispatch('revisions/completeFirstContact', currentContact.value.id);

      showQuestionsModal.value = false;
      currentContact.value = null;
    };

    const saveNewRevision = (revisionData) => {
      console.log('Saving new revision:', revisionData);
      
      // Criar nova revisão com os dados fornecidos
      const newRevision = {
        id: Date.now().toString(),
        title: revisionData.title,
        subject: revisionData.subject,
        date: revisionData.date,
        startTime: revisionData.startTime,
        endTime: revisionData.endTime,
        revisionType: revisionData.revisionType || 'Teórica',
        priority: revisionData.priority || 'Média',
        description: revisionData.description || '',
        completed: false,
        created: new Date().toISOString(),
        isManual: true,
        type: 'manual' // Adicionar type para compatibilidade com getRevisionClass
      };

      console.log('New revision object:', newRevision);

      // Adicionar ao store de revisões espaçadas como uma revisão manual
      store.dispatch('revisions/addSpacedRevision', newRevision);

      // Fechar modal
      showNewRevisionModal.value = false;

      // Mostrar notificação de sucesso
      console.log('Revisão agendada com sucesso!', newRevision);
      console.log('Total revisions after save:', store.state.revisions.spacedRevisions.length);

      // Se houver um sistema de toast disponível
      if (window.$toast) {
        window.$toast.success('Revisão agendada com sucesso!');
      } else {
        alert('Revisão agendada com sucesso!');
        // Forçar atualização da interface
        store.dispatch('revisions/saveToLocalStorage');
      }
    };

    const handleRevisionClick = (revision) => {
      // Por enquanto, apenas marcar como completa se clicar
      if (!revision.completed && window.confirm(`Marcar "${revision.title}" como concluída?`)) {
        store.dispatch('revisions/completeRevision', revision.id);
      }
    };

    // Variável para armazenar a instância do gráfico
    let chartInstance = null;

    const initRetentionChart = () => {
      const ctx = retentionChart.value?.getContext('2d');
      if (!ctx) return;

      // Destruir gráfico existente antes de criar um novo
      if (chartInstance) {
        chartInstance.destroy();
        chartInstance = null;
      }

      const performances = store.state.revisions.performances || [];
      const last30Days = performances.slice(-30);

      chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: last30Days.map(p => new Date(p.date).toLocaleDateString('pt-BR', { day: 'numeric', month: 'short' })),
          datasets: [{
            label: 'Taxa de Acerto (%)',
            data: last30Days.map(p => p.percentage),
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              ticks: {
                callback: (value) => value + '%'
              }
            }
          }
        }
      });
    };

    const createSampleData = () => {
      // Criar algumas revisões de exemplo para demonstração
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      const sampleRevisions = [
        {
          id: Date.now().toString() + '-1',
          title: 'Revisão de Anatomia Cardiovascular',
          subject: 'Anatomia',
          date: today,
          startTime: '14:00',
          endTime: '15:30',
          revisionType: 'Teórica',
          priority: 'Alta',
          description: 'Revisar sistema cardiovascular completo',
          completed: false,
          isManual: true,
          type: 'manual'
        },
        {
          id: Date.now().toString() + '-2',
          title: 'Questões de Farmacologia',
          subject: 'Farmacologia',
          date: tomorrow,
          startTime: '10:00',
          endTime: '11:00',
          revisionType: 'Prática',
          priority: 'Média',
          description: 'Resolver questões sobre antibióticos',
          completed: false,
          isManual: true,
          type: 'manual'
        }
      ];
      
      // Adicionar revisões de exemplo apenas se não houver revisões
      if (store.state.revisions.spacedRevisions.length === 0) {
        sampleRevisions.forEach(revision => {
          store.dispatch('revisions/addSpacedRevision', revision);
        });
      }
    };

    const openNewRevisionModal = () => {
      console.log('=== BOTÃO CLICADO ===');
      console.log('Estado atual do modal:', showNewRevisionModal.value);
      showNewRevisionModal.value = true;
      console.log('Novo estado do modal:', showNewRevisionModal.value);
      
      // Forçar atualização
      setTimeout(() => {
        console.log('Estado após 100ms:', showNewRevisionModal.value);
      }, 100);
    };

    const resetAndCreateSampleData = () => {
      console.log('Resetting and creating sample data...');
      
      // Limpar localStorage
      localStorage.removeItem('spacedRevisionsData');
      
      // Resetar estado do store
      store.commit('revisions/SET_REVISIONS_DATA', {
        theoryStudies: [],
        firstContacts: [],
        spacedRevisions: [],
        performances: []
      });
      
      // Aguardar um momento e criar dados de exemplo
      setTimeout(() => {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dayAfter = new Date(tomorrow);
        dayAfter.setDate(dayAfter.getDate() + 1);
        
        const sampleRevisions = [
          {
            id: Date.now().toString() + '-demo1',
            title: 'Revisão de Anatomia Cardiovascular',
            subject: 'Anatomia',
            date: today,
            startTime: '14:00',
            endTime: '15:30',
            revisionType: 'Teórica',
            priority: 'Alta',
            description: 'Revisar sistema cardiovascular completo',
            completed: false,
            isManual: true,
            type: 'manual'
          },
          {
            id: Date.now().toString() + '-demo2',
            title: 'Questões de Farmacologia',
            subject: 'Farmacologia',
            date: tomorrow,
            startTime: '10:00',
            endTime: '11:00',
            revisionType: 'Prática',
            priority: 'Média',
            description: 'Resolver questões sobre antibióticos',
            completed: false,
            isManual: true,
            type: 'manual'
          },
          {
            id: Date.now().toString() + '-demo3',
            title: 'Flashcards de Microbiologia',
            subject: 'Microbiologia',
            date: dayAfter,
            startTime: '16:00',
            endTime: '17:00',
            revisionType: 'Flashcards',
            priority: 'Baixa',
            description: 'Revisar conceitos de bacteriologia',
            completed: false,
            isManual: true,
            type: 'manual'
          }
        ];
        
        sampleRevisions.forEach((revision, index) => {
          setTimeout(() => {
            store.dispatch('revisions/addSpacedRevision', revision);
          }, index * 100);
        });
        
        alert('Dados de demonstração criados com sucesso!');
      }, 100);
    };

    onMounted(async () => {
      console.log('SpacedRevisionSystem mounted');
      console.log('showNewRevisionModal initial value:', showNewRevisionModal.value);
      
      // Carregar revisões do localStorage
      await store.dispatch('revisions/loadRevisions');
      
      // Criar dados de exemplo se necessário
      setTimeout(() => {
        console.log('Current revisions:', store.state.revisions.spacedRevisions);
        console.log('Modal state after mount:', showNewRevisionModal.value);
        createSampleData();
        initRetentionChart();
      }, 100);
    });

    watch(() => store.state.revisions.performances, () => {
      initRetentionChart();
    });

    // Adicionar watcher para revisões
    watch(() => store.state.revisions.spacedRevisions, (newRevisions) => {
      console.log('Revisions updated:', newRevisions);
    }, { deep: true });

    // Limpar gráfico ao desmontar componente
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.destroy();
        chartInstance = null;
      }
    });

    return {
      // Refs
      showQuestionsModal,
      showAnalytics,
      showNewRevisionModal,
      currentContact,
      currentWeek,
      retentionChart,
      
      // Computed
      theoryStudies,
      recentTheoryStudies,
      firstContacts,
      spacedRevisions,
      averagePerformance,
      todayContacts,
      weekDays,
      currentWeekLabel,
      subjectPerformance,
      analyticsData,
      
      // Methods
      formatDate,
      formatTime,
      getDaysUntilContact,
      isToday,
      getRevisionsForDay,
      getRevisionClass,
      previousWeek,
      nextWeek,
      saveTheoryStudy,
      startQuestions,
      completeQuestions,
      saveNewRevision,
      handleRevisionClick,
      openNewRevisionModal,
      resetAndCreateSampleData
    };
  }
};
</script>

<style scoped>
/* Base Styles */
.spaced-revision-system {
  min-height: 100vh;
  background: #0f1419;
  color: #e4e6eb;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  bottom: -100px;
  right: -100px;
  animation-delay: 7s;
}

.orb-3 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 14s;
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
}

/* Header */
.revision-header {
  position: relative;
  z-index: 1;
  padding: 2rem;
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

.page-info h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #e4e6eb, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-info p {
  margin: 0.25rem 0 0;
  color: #94a3b8;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.action-button {
  padding: 0.75rem 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  color: #e4e6eb;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.action-button:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 1px solid transparent;
}

.action-button.primary:hover {
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.action-button.new-revision {
  background: linear-gradient(135deg, #10b981, #059669);
  border: 1px solid transparent;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.action-button.new-revision:hover {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}

.action-button.new-revision:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button.new-revision:hover:before {
  left: 100%;
}

.action-button.demo-button {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-button.demo-button:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  transform: translateY(-1px);
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s;
}

.stat-card:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.stat-icon.theory {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.stat-icon.contact {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.stat-icon.review {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.stat-icon.performance {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #e4e6eb;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Content Grid */
.content-grid {
  position: relative;
  z-index: 1;
  padding: 2rem;
  display: grid;
  gap: 2rem;
}

.content-section {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #e4e6eb;
}

.section-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  display: block;
  margin-top: 0.25rem;
  margin-left: 2.25rem;
}

/* Theory Section */
.theory-list {
  display: grid;
  gap: 1rem;
}

.theory-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s;
}

.theory-card:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.2);
}

.theory-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: #e4e6eb;
}

.theory-subject {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.theory-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.theory-date,
.theory-difficulty {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #94a3b8;
}

.theory-difficulty.Fácil { color: #10b981; }
.theory-difficulty.Difícil { color: #f59e0b; }

.theory-status {
  text-align: right;
}

.first-contact-date {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.25rem;
}

.first-contact-date .date {
  color: #e4e6eb;
  font-weight: 500;
}

.days-until {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6366f1;
}

/* Contact Queue */
.contact-queue {
  display: grid;
  gap: 1rem;
}

.contact-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.contact-card:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(59, 130, 246, 0.2);
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.contact-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.contact-type {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.theory-reference {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.contact-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.questions-count,
.estimated-time {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.start-button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.start-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Calendar Section */
.revision-calendar {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.calendar-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.nav-button {
  width: 36px;
  height: 36px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.nav-button:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1rem;
  min-height: 120px;
  transition: all 0.2s;
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.day-name {
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
}

.day-date {
  font-size: 0.875rem;
  font-weight: 600;
  color: #e4e6eb;
}

.day-revisions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.revision-item {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0.5rem;
  position: relative;
  overflow: hidden;
}

.revision-item:hover {
  background: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.revision-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: #6366f1;
}

.revision-item:has(.revision-priority.alta)::before {
  background: #ef4444;
}

.revision-item:has(.revision-priority.média)::before {
  background: #fbbf24;
}

.revision-item:has(.revision-priority.baixa)::before {
  background: #22c55e;
}

.revision-item.completed {
  opacity: 0.6;
  text-decoration: line-through;
}

.revision-item.overdue {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.revision-time {
  color: #94a3b8;
  font-size: 0.7rem;
  font-weight: 500;
}

.revision-title {
  color: #e4e6eb;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.revision-type {
  color: #6366f1;
  font-size: 0.7rem;
  font-weight: 500;
}

.revision-priority {
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  text-transform: uppercase;
  align-self: flex-start;
}

.revision-priority.baixa {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.revision-priority.média {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.revision-priority.alta {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.revision-performance {
  color: #6366f1;
  font-weight: 500;
}

/* Analytics Section */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.analytics-card {
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.analytics-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #e4e6eb;
}

.chart-container {
  height: 200px;
}

.subject-performance {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subject-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subject-name {
  color: #e4e6eb;
  font-size: 0.875rem;
  font-weight: 500;
}

.subject-avg {
  color: #6366f1;
  font-size: 0.875rem;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.optimal-intervals {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interval-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  font-size: 0.875rem;
}

.intervals-list {
  display: grid;
  gap: 0.5rem;
}

.interval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  font-size: 0.875rem;
}

.performance-range {
  color: #94a3b8;
  font-weight: 500;
}

.interval-days {
  color: #6366f1;
  font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-actions {
    flex-wrap: wrap;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .calendar-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }
}
</style>