<template>
  <div class="test-container">
    <h1>Teste do Modal de Revisão</h1>
    
    <button @click="showModal = true" class="test-button">
      <PERSON><PERSON><PERSON> (Estado: {{ showModal }})
    </button>
    
    <!-- Modal simples para teste -->
    <div v-if="showModal" class="modal-test">
      <div class="modal-content">
        <h2>Modal está funcionando!</h2>
        <button @click="showModal = false">Fechar</button>
      </div>
    </div>
    
    <!-- Modal real -->
    <NewRevisionModal
      v-if="showNewModal"
      @close="showNewModal = false"
      @save="handleSave"
      @saveStudy="handleSaveStudy"
    />
    
    <button @click="showNewModal = true" class="test-button">
      Abrir NewRevisionModal (Estado: {{ showNewModal }})
    </button>
  </div>
</template>

<script>
import { ref } from 'vue';
import NewRevisionModal from './NewRevisionModal.vue';

export default {
  name: 'TestRevisionModal',
  components: {
    NewRevisionModal
  },
  setup() {
    const showModal = ref(false);
    const showNewModal = ref(false);
    
    const handleSave = (data) => {
      console.log('Save called:', data);
      alert('Revisão manual salva!');
      showNewModal.value = false;
    };
    
    const handleSaveStudy = (data) => {
      console.log('Save study called:', data);
      alert('Estudo salvo!');
      showNewModal.value = false;
    };
    
    return {
      showModal,
      showNewModal,
      handleSave,
      handleSaveStudy
    };
  }
};
</script>

<style scoped>
.test-container {
  padding: 2rem;
  background: #0f1419;
  min-height: 100vh;
  color: white;
}

.test-button {
  padding: 1rem 2rem;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin: 1rem;
}

.modal-test {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: white;
  color: black;
  padding: 2rem;
  border-radius: 8px;
}
</style>