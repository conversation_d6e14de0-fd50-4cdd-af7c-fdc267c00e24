<template>
  <div class="revisoes-view">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="revisoes-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Sistema de Revisões Inteligente</h1>
            <p class="page-subtitle">Metodologia científica de repetição espaçada</p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="showRevisionModal = true" class="action-button primary">
            <i class="fas fa-plus"></i>
            <span>Nova Revisão</span>
          </button>
          <button @click="showQuestionsModal = true" class="action-button">
            <i class="fas fa-question-circle"></i>
            <span>Questões</span>
          </button>
          <button @click="showAnalytics = !showAnalytics" class="action-button">
            <i class="fas fa-chart-line"></i>
            <span>Análise</span>
          </button>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon studies">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalStudies }}</div>
            <div class="stat-label">Estudos Registrados</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon questions">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalQuestions }}</div>
            <div class="stat-label">Questões Resolvidas</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon performance">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ averagePerformance }}%</div>
            <div class="stat-label">Taxa de Acerto</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon upcoming">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ upcomingRevisions }}</div>
            <div class="stat-label">Revisões Pendentes</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="revisoes-content">
      <!-- Analytics Section (Collapsible) -->
      <transition name="slide-fade">
        <div v-if="showAnalytics" class="analytics-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-analytics"></i>
              Análise de Desempenho
            </h2>
          </div>
          <div class="analytics-grid">
            <div class="chart-container">
              <h3>Progresso por Matéria</h3>
              <canvas ref="subjectChart"></canvas>
            </div>
            <div class="chart-container">
              <h3>Curva de Retenção</h3>
              <canvas ref="retentionChart"></canvas>
            </div>
          </div>
        </div>
      </transition>

      <!-- Tabs -->
      <div class="content-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
          <span v-if="tab.count" class="tab-badge">{{ tab.count }}</span>
        </button>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- Próximas Revisões -->
        <div v-if="activeTab === 'upcoming'" class="revisions-list">
          <div v-if="upcomingRevisionsList.length === 0" class="empty-state">
            <i class="fas fa-calendar-check"></i>
            <h3>Nenhuma revisão pendente</h3>
            <p>Registre novos estudos para começar</p>
          </div>
          <transition-group name="list" tag="div" v-else>
            <div 
              v-for="revision in upcomingRevisionsList" 
              :key="revision.id"
              class="revision-card"
              :class="getRevisionClass(revision)"
            >
              <div class="revision-header">
                <div class="revision-info">
                  <h3>{{ revision.title }}</h3>
                  <div class="revision-meta">
                    <span class="subject-tag">
                      <i class="fas fa-tag"></i>
                      {{ revision.subject }}
                    </span>
                    <span class="date-tag">
                      <i class="fas fa-calendar"></i>
                      {{ formatDate(revision.date) }}
                    </span>
                  </div>
                </div>
                <div class="revision-priority" :class="`priority-${revision.priority}`">
                  {{ revision.priority }}
                </div>
              </div>
              <div class="revision-actions">
                <button @click="startRevision(revision)" class="action-btn primary">
                  <i class="fas fa-play"></i>
                  Iniciar
                </button>
                <button @click="postponeRevision(revision)" class="action-btn">
                  <i class="fas fa-clock"></i>
                  Adiar
                </button>
              </div>
            </div>
          </transition-group>
        </div>

        <!-- Histórico -->
        <div v-if="activeTab === 'history'" class="history-list">
          <div v-if="historyList.length === 0" class="empty-state">
            <i class="fas fa-history"></i>
            <h3>Nenhum histórico ainda</h3>
            <p>Complete algumas revisões para ver seu progresso</p>
          </div>
          <div v-else class="history-timeline">
            <div 
              v-for="item in historyList" 
              :key="item.id"
              class="history-item"
            >
              <div class="timeline-marker">
                <i :class="getHistoryIcon(item)"></i>
              </div>
              <div class="history-content">
                <h4>{{ item.title }}</h4>
                <div class="history-details">
                  <span class="performance" :class="getPerformanceClass(item.performance)">
                    {{ item.performance }}% de acerto
                  </span>
                  <span class="date">{{ formatDateTime(item.completedAt) }}</span>
                </div>
                <div v-if="item.recommendations" class="recommendations">
                  <p v-for="(rec, idx) in item.recommendations" :key="idx">
                    <i class="fas fa-lightbulb"></i> {{ rec }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Estatísticas -->
        <div v-if="activeTab === 'stats'" class="stats-dashboard">
          <div class="stats-grid">
            <div class="stat-box">
              <h3>Taxa de Retenção</h3>
              <div class="big-number">{{ retentionRate }}%</div>
              <p>Média dos últimos 30 dias</p>
            </div>
            <div class="stat-box">
              <h3>Streak Atual</h3>
              <div class="big-number">{{ currentStreak }}</div>
              <p>Dias consecutivos estudando</p>
            </div>
            <div class="stat-box">
              <h3>Tempo Total</h3>
              <div class="big-number">{{ totalHours }}h</div>
              <p>Horas de estudo registradas</p>
            </div>
            <div class="stat-box">
              <h3>Melhor Matéria</h3>
              <div class="big-number">{{ bestSubject }}</div>
              <p>{{ bestSubjectPerformance }}% de acerto</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Nova Ferramenta de Revisões -->
    <RevisionToolWindow 
      v-if="showRevisionModal"
      @close="showRevisionModal = false"
      @estudo-registrado="handleEstudoRegistrado"
      @revisao-registrada="handleRevisaoRegistrada"
    />
    
    <QuestionsModal
      v-if="showQuestionsModal"
      :contact="selectedQuestionContact"
      @close="showQuestionsModal = false"
      @complete="handleQuestionsComplete"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import RevisionToolWindow from '@/components/RevisionToolWindow.vue'
import QuestionsModal from '@/components/QuestionsModal.vue'

export default {
  name: 'RevisoesView',
  components: {
    RevisionToolWindow,
    QuestionsModal
  },
  setup() {
    const router = useRouter()
    
    // State
    const showRevisionModal = ref(false)
    const showQuestionsModal = ref(false)
    const showAnalytics = ref(false)
    const activeTab = ref('upcoming')
    const selectedQuestionContact = ref(null)
    
    // Data
    const studies = ref([])
    const performances = ref([])
    const revisions = ref([])
    
    // Tabs
    const tabs = ref([
      { id: 'upcoming', name: 'Próximas Revisões', icon: 'fas fa-calendar-alt', count: 0 },
      { id: 'history', name: 'Histórico', icon: 'fas fa-history', count: 0 },
      { id: 'stats', name: 'Estatísticas', icon: 'fas fa-chart-bar' }
    ])
    
    // Computed
    const totalStudies = computed(() => studies.value.length)
    const totalQuestions = computed(() => performances.value.reduce((sum, p) => sum + p.totalQuestions, 0))
    const averagePerformance = computed(() => {
      if (performances.value.length === 0) return 0
      const avg = performances.value.reduce((sum, p) => sum + p.percentage, 0) / performances.value.length
      return Math.round(avg)
    })
    const upcomingRevisions = computed(() => {
      const now = new Date()
      return revisions.value.filter(r => new Date(r.date) > now && !r.completed).length
    })
    
    const upcomingRevisionsList = computed(() => {
      const now = new Date()
      return revisions.value
        .filter(r => new Date(r.date) > now && !r.completed)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .slice(0, 10)
    })
    
    const historyList = computed(() => {
      return performances.value
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 20)
    })
    
    const retentionRate = computed(() => {
      const recent = performances.value.slice(0, 10)
      if (recent.length === 0) return 0
      const avg = recent.reduce((sum, p) => sum + p.percentage, 0) / recent.length
      return Math.round(avg)
    })
    
    const currentStreak = computed(() => {
      // Calculate consecutive days studied
      return 7 // Placeholder
    })
    
    const totalHours = computed(() => {
      return studies.value.reduce((sum, s) => sum + (s.duration || 0), 0) / 60
    })
    
    const bestSubject = computed(() => {
      if (performances.value.length === 0) return 'N/A'
      const subjectPerformance = {}
      performances.value.forEach(p => {
        if (!subjectPerformance[p.subject]) {
          subjectPerformance[p.subject] = { total: 0, count: 0 }
        }
        subjectPerformance[p.subject].total += p.percentage
        subjectPerformance[p.subject].count++
      })
      
      let best = { subject: 'N/A', avg: 0 }
      Object.entries(subjectPerformance).forEach(([subject, data]) => {
        const avg = data.total / data.count
        if (avg > best.avg) {
          best = { subject, avg: Math.round(avg) }
        }
      })
      
      return best.subject
    })
    
    const bestSubjectPerformance = computed(() => {
      if (performances.value.length === 0) return 0
      const subjectPerformance = {}
      performances.value.forEach(p => {
        if (!subjectPerformance[p.subject]) {
          subjectPerformance[p.subject] = { total: 0, count: 0 }
        }
        subjectPerformance[p.subject].total += p.percentage
        subjectPerformance[p.subject].count++
      })
      
      let best = { subject: 'N/A', avg: 0 }
      Object.entries(subjectPerformance).forEach(([subject, data]) => {
        const avg = data.total / data.count
        if (avg > best.avg) {
          best = { subject, avg: Math.round(avg) }
        }
      })
      
      return best.avg
    })
    
    // Methods
    const loadData = () => {
      studies.value = JSON.parse(localStorage.getItem('studies') || '[]')
      performances.value = JSON.parse(localStorage.getItem('performances') || '[]')
      revisions.value = JSON.parse(localStorage.getItem('revisions') || '[]')
      
      // Update tab counts
      tabs.value[0].count = upcomingRevisions.value
      tabs.value[1].count = performances.value.length
    }
    
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      })
    }
    
    const formatDateTime = (date) => {
      return new Date(date).toLocaleString('pt-BR', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const getRevisionClass = (revision) => {
      const daysUntil = Math.ceil((new Date(revision.date) - new Date()) / (1000 * 60 * 60 * 24))
      if (daysUntil <= 0) return 'overdue'
      if (daysUntil <= 2) return 'urgent'
      if (daysUntil <= 7) return 'soon'
      return ''
    }
    
    const getHistoryIcon = (item) => {
      if (item.performance >= 80) return 'fas fa-star'
      if (item.performance >= 60) return 'fas fa-check-circle'
      return 'fas fa-exclamation-circle'
    }
    
    const getPerformanceClass = (performance) => {
      if (performance >= 80) return 'excellent'
      if (performance >= 60) return 'good'
      return 'needs-improvement'
    }
    
    const startRevision = (revision) => {
      selectedQuestionContact.value = {
        title: revision.title,
        subject: revision.subject
      }
      showQuestionsModal.value = true
    }
    
    const postponeRevision = (revision) => {
      // Implement postpone logic
      console.log('Postponing revision:', revision)
    }
    
    // Handlers
    const handleRevisionSave = (revision) => {
      revisions.value.push(revision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      loadData()
      showRevisionModal.value = false
    }
    
    const handleStudySave = (study) => {
      studies.value.push(study)
      localStorage.setItem('studies', JSON.stringify(studies.value))
      
      // Create first revision
      const firstRevision = {
        id: `revision-${Date.now()}`,
        title: `Questões: ${study.title}`,
        subject: study.subject,
        date: study.scheduling.firstRevisionDate,
        type: 'questions',
        priority: 'medium',
        studyId: study.id
      }
      
      revisions.value.push(firstRevision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      
      loadData()
      showRevisionModal.value = false
    }
    
    const handleQuestionsSave = (questionData) => {
      performances.value.push(questionData)
      localStorage.setItem('performances', JSON.stringify(performances.value))
      
      // Create next revision
      const nextRevision = {
        id: `revision-${Date.now()}`,
        title: `Revisão: ${questionData.title}`,
        subject: questionData.subject,
        date: questionData.scheduling.nextRevisionDate,
        type: 'revision',
        priority: questionData.scheduling.priority,
        previousPerformance: questionData.percentage
      }
      
      revisions.value.push(nextRevision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      
      loadData()
      showRevisionModal.value = false
    }
    
    const handleQuestionsComplete = (result) => {
      performances.value.push(result)
      localStorage.setItem('performances', JSON.stringify(performances.value))
      
      // Create next revision
      const nextRevision = {
        id: `revision-${Date.now()}`,
        title: `Revisão: ${result.scheduling.topic}`,
        subject: result.scheduling.subject,
        date: result.scheduling.nextRevisionDate,
        type: 'revision',
        priority: result.scheduling.priority,
        previousPerformance: result.percentage
      }
      
      revisions.value.push(nextRevision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      
      loadData()
      showQuestionsModal.value = false
    }
    
    // Novos handlers para RevisionToolWindow
    const handleEstudoRegistrado = (estudo) => {
      studies.value.push(estudo)
      localStorage.setItem('studies', JSON.stringify(studies.value))
      
      // Criar primeira revisão com questões baseada na dificuldade
      const firstRevision = {
        id: `revision-${Date.now()}`,
        title: `Primeiro Contato - Questões: ${estudo.titulo}`,
        subject: estudo.materia,
        date: estudo.dataPrimeiroContato,
        type: 'questions',
        priority: estudo.dificuldade === 'Difícil' ? 'high' : 'medium',
        studyId: estudo.id,
        metadata: {
          difficulty: estudo.dificuldade,
          studyDuration: estudo.tempo,
          notes: estudo.notas
        }
      }
      
      revisions.value.push(firstRevision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      
      loadData()
      showRevisionModal.value = false
    }
    
    const handleRevisaoRegistrada = (revisao) => {
      performances.value.push(revisao)
      localStorage.setItem('performances', JSON.stringify(performances.value))
      
      // Criar próxima revisão baseada no desempenho
      const nextRevision = {
        id: `revision-${Date.now()}`,
        title: `Revisão Espaçada: ${revisao.titulo}`,
        subject: revisao.materia,
        date: revisao.proximaRevisaoData,
        type: 'revision',
        priority: revisao.percentual < 60 ? 'high' : revisao.percentual < 75 ? 'medium' : 'low',
        previousPerformance: revisao.percentual,
        metadata: {
          previousRevisionsCount: (performances.value.filter(p => p.estudoId === revisao.estudoId).length),
          totalQuestions: revisao.totalQuestoes,
          correctAnswers: revisao.acertos
        }
      }
      
      revisions.value.push(nextRevision)
      localStorage.setItem('revisions', JSON.stringify(revisions.value))
      
      loadData()
      showRevisionModal.value = false
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      // State
      showRevisionModal,
      showQuestionsModal,
      showAnalytics,
      activeTab,
      selectedQuestionContact,
      tabs,
      
      // Computed
      totalStudies,
      totalQuestions,
      averagePerformance,
      upcomingRevisions,
      upcomingRevisionsList,
      historyList,
      retentionRate,
      currentStreak,
      totalHours,
      bestSubject,
      bestSubjectPerformance,
      
      // Methods
      formatDate,
      formatDateTime,
      getRevisionClass,
      getHistoryIcon,
      getPerformanceClass,
      startRevision,
      postponeRevision,
      handleRevisionSave,
      handleStudySave,
      handleQuestionsSave,
      handleQuestionsComplete,
      handleEstudoRegistrado,
      handleRevisaoRegistrada
    }
  }
}
</script>

<style scoped>
/* Base Layout */
.revisoes-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0e17 0%, #1a1a2e 40%, #16213e 100%);
  color: #fffffe;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #667eea 0%, transparent 70%);
  top: -200px;
  left: -200px;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #764ba2 0%, transparent 70%);
  bottom: -100px;
  right: -100px;
  animation-delay: -5s;
}

.orb-3 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #f093fb 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.05); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Header */
.revisoes-header {
  position: relative;
  z-index: 10;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 2rem 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1rem;
  color: #94a3b8;
  margin: 0.25rem 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 1rem 2rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.action-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.action-button.primary:hover {
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

.action-button.primary:hover::before {
  left: 100%;
}

/* Stats Overview */
.stats-overview {
  max-width: 1400px;
  margin: 2rem auto 0;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-icon.studies {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.stat-icon.questions {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stat-icon.performance {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.stat-icon.upcoming {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #f1f5f9;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Main Content */
.revisoes-content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Analytics Section */
.analytics-section {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #f1f5f9;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.chart-container {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
}

.chart-container h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #e2e8f0;
}

/* Tabs */
.content-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding-bottom: 1rem;
}

.tab-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.tab-button:hover {
  color: #e2e8f0;
}

.tab-button.active {
  color: #667eea;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.tab-badge {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Tab Content */
.tab-content {
  min-height: 400px;
}

/* Revisions List */
.revision-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.revision-card:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.revision-card.overdue {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.05);
}

.revision-card.urgent {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.revision-card.soon {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.revision-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.revision-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.revision-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.subject-tag,
.date-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.revision-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.priority-medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.priority-low {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.revision-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #94a3b8;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #e2e8f0;
}

/* History Timeline */
.history-timeline {
  position: relative;
  padding-left: 2rem;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #667eea, transparent);
}

.history-item {
  position: relative;
  padding-bottom: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -1.25rem;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: #1e293b;
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #667eea;
}

.history-content {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  margin-left: 1rem;
}

.history-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.history-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
}

.performance {
  font-weight: 600;
}

.performance.excellent {
  color: #10b981;
}

.performance.good {
  color: #3b82f6;
}

.performance.needs-improvement {
  color: #f59e0b;
}

.recommendations {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.recommendations p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendations i {
  color: #f59e0b;
}

/* Stats Dashboard */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-box {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.stat-box:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-box h3 {
  font-size: 1rem;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 1rem;
}

.big-number {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-box p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Animations */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(10px);
  opacity: 0;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .content-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tab-button {
    white-space: nowrap;
  }
}
</style>