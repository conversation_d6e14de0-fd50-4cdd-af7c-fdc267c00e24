<template>
  <div class="revision-system">
    <h1>Sistema de Revisões - VERSÃO SIMPLIFICADA</h1>
    
    <!-- <PERSON><PERSON><PERSON> Principal -->
    <button @click="abrirModal" class="botao-principal">
      CLIQUE AQUI PARA ABRIR MODAL (Estado: {{ modalAberto }})
    </button>
    
    <!-- Modal Inline Simples -->
    <div v-if="modalAberto" class="modal-simples">
      <div class="modal-conteudo">
        <h2>MODAL ESTÁ FUNCIONANDO!</h2>
        <button @click="modalAberto = false">Fechar</button>
      </div>
    </div>
    
    <!-- Teste com NewRevisionModal Real -->
    <button @click="abrirModalReal" class="botao-principal">
      Abrir NewRevisionModal Real (Estado: {{ modalRealAberto }})
    </button>
    
    <NewRevisionModal
      v-if="modalRealAberto"
      @close="fecharModalReal"
      @save="salvarRevisao"
      @saveStudy="salvarEstudo"
    />
  </div>
</template>

<script>
import { ref } from 'vue';
import NewRevisionModal from './NewRevisionModal.vue';

export default {
  name: 'SpacedRevisionSystemSimple',
  components: {
    NewRevisionModal
  },
  setup() {
    // Estados
    const modalAberto = ref(false);
    const modalRealAberto = ref(false);
    
    // Métodos
    const abrirModal = () => {
      console.log('=== ABRINDO MODAL SIMPLES ===');
      modalAberto.value = true;
    };
    
    const abrirModalReal = () => {
      console.log('=== ABRINDO MODAL REAL ===');
      modalRealAberto.value = true;
    };
    
    const fecharModalReal = () => {
      console.log('=== FECHANDO MODAL REAL ===');
      modalRealAberto.value = false;
    };
    
    const salvarRevisao = (dados) => {
      console.log('=== SALVANDO REVISÃO ===', dados);
      alert('Revisão salva com sucesso!');
      modalRealAberto.value = false;
    };
    
    const salvarEstudo = (dados) => {
      console.log('=== SALVANDO ESTUDO ===', dados);
      alert('Estudo salvo com sucesso!');
      modalRealAberto.value = false;
    };
    
    return {
      modalAberto,
      modalRealAberto,
      abrirModal,
      abrirModalReal,
      fecharModalReal,
      salvarRevisao,
      salvarEstudo
    };
  }
};
</script>

<style scoped>
.revision-system {
  padding: 2rem;
  background: #0f1419;
  min-height: 100vh;
  color: white;
}

.botao-principal {
  padding: 1rem 2rem;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  margin: 1rem;
  display: block;
}

.botao-principal:hover {
  background: #4f46e5;
}

.modal-simples {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
}

.modal-conteudo {
  background: white;
  color: black;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
}

.modal-conteudo button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>