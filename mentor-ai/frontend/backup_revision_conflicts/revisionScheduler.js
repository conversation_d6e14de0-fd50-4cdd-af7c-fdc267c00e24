import { getFromStorage, saveToStorage } from '@/utils/storage';
import { 
  calculateNextRevisionDate, 
  calculateFirstContactDate 
} from '@/utils/dateUtils';
import { generateMockRevisions } from '@/utils/mockData';

// Helper function to load data from localStorage
const loadRevisionsFromStorage = () => {
  const revisions = getFromStorage('mentor-ai-revisions');
  return revisions || generateMockRevisions();
};

// Helper function to save revisions to localStorage
const saveRevisionsToStorage = (revisions) => {
  saveToStorage('mentor-ai-revisions', revisions);
};

// Helper function to load settings from localStorage
const loadSettingsFromStorage = () => {
  return getFromStorage('mentor-ai-revision-settings', {
    algorithm: 'ebbinghaus',
    forgettingFactor: 1.0,
    minInterval: 6,
    maxInterval: 60,
    chronotype: 'intermediate',
    energyPeakStart: '09:00',
    energyPeakEnd: '12:00',
    avoidSleepInterference: true,
    enableNotifications: true,
    notificationTime: 30,
    soundEnabled: true
  });
};

export default {
  namespaced: true,
  
  state: () => ({
    revisions: loadRevisionsFromStorage(),
    settings: loadSettingsFromStorage(),
    loading: false,
    error: null
  }),
  
  getters: {
    getRevisionsByDate: (state) => (date) => {
      return state.revisions.filter(rev => 
        new Date(rev.date).toDateString() === new Date(date).toDateString()
      );
    },
    
    getUpcomingRevisions: (state) => {
      const today = new Date();
      return state.revisions
        .filter(rev => new Date(rev.date) >= today)
        .sort((a, b) => new Date(a.date) - new Date(b.date));
    },
    
    getSettings: (state) => state.settings,
    
    // Get revisions by subject
    getRevisionsBySubject: (state) => (subject) => {
      return state.revisions.filter(rev => rev.subject === subject);
    },
    
    // Get unique subjects
    getSubjects: (state) => {
      const subjects = state.revisions.map(rev => rev.subject);
      return [...new Set(subjects)];
    },
    
    // Get total count
    getTotalCount: (state) => state.revisions.length
  },
  
  mutations: {
    SET_REVISIONS(state, revisions) {
      state.revisions = revisions;
    },
    
    ADD_REVISION(state, revision) {
      state.revisions.push(revision);
      saveRevisionsToStorage(state.revisions);
    },
    
    UPDATE_REVISION(state, { id, updates }) {
      const index = state.revisions.findIndex(r => r.id === id);
      if (index !== -1) {
        state.revisions[index] = { ...state.revisions[index], ...updates };
        saveRevisionsToStorage(state.revisions);
      }
    },
    
    DELETE_REVISION(state, id) {
      state.revisions = state.revisions.filter(r => r.id !== id);
      saveRevisionsToStorage(state.revisions);
    },
    
    UPDATE_SETTINGS(state, settings) {
      state.settings = { ...state.settings, ...settings };
      saveToStorage('mentor-ai-revision-settings', state.settings);
    },
    
    SET_LOADING(state, isLoading) {
      state.loading = isLoading;
    },
    
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  
  actions: {
    loadRevisions({ commit }) {
      commit('SET_LOADING', true);
      
      try {
        const revisions = loadRevisionsFromStorage();
        commit('SET_REVISIONS', revisions);
        commit('SET_ERROR', null);
      } catch (error) {
        console.error('Failed to load revisions:', error);
        commit('SET_ERROR', 'Falha ao carregar revisões');
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    addRevision({ commit }, revision) {
      commit('SET_LOADING', true);
      
      try {
        const newRevision = {
          ...revision,
          id: Date.now(),
          progress: 0
        };
        
        commit('ADD_REVISION', newRevision);
        commit('SET_ERROR', null);
        return Promise.resolve(newRevision);
      } catch (error) {
        console.error('Failed to add revision:', error);
        commit('SET_ERROR', 'Falha ao adicionar revisão');
        return Promise.reject(error);
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    updateRevision({ commit }, { id, updates }) {
      commit('SET_LOADING', true);
      
      try {
        commit('UPDATE_REVISION', { id, updates });
        commit('SET_ERROR', null);
        return Promise.resolve();
      } catch (error) {
        console.error('Failed to update revision:', error);
        commit('SET_ERROR', 'Falha ao atualizar revisão');
        return Promise.reject(error);
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    deleteRevision({ commit }, id) {
      commit('SET_LOADING', true);
      
      try {
        commit('DELETE_REVISION', id);
        commit('SET_ERROR', null);
        return Promise.resolve();
      } catch (error) {
        console.error('Failed to delete revision:', error);
        commit('SET_ERROR', 'Falha ao excluir revisão');
        return Promise.reject(error);
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    updateSettings({ commit }, settings) {
      try {
        commit('UPDATE_SETTINGS', settings);
        return Promise.resolve();
      } catch (error) {
        console.error('Failed to update settings:', error);
        return Promise.reject(error);
      }
    },
    
    // Calculate a new revision based on study results
    calculateRevision({ commit, state }, { studyDate, subject, difficulty, correctPercentage }) {
      try {
        const firstContactDate = calculateFirstContactDate(studyDate, difficulty);
        const nextRevisionDate = calculateNextRevisionDate(studyDate, correctPercentage);
        
        return Promise.resolve({
          firstContactDate,
          nextRevisionDate
        });
      } catch (error) {
        console.error('Failed to calculate revision:', error);
        return Promise.reject(error);
      }
    }
  }
}; 