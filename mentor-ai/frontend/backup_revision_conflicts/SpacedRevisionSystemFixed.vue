<template>
  <div class="spaced-revision-system">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="revision-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Sistema de Revisões Espaçadas</h1>
            <p class="page-subtitle">Método científico de retenção de conhecimento</p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="handleNewRevision" class="action-button new-revision">
            <i class="fas fa-plus"></i>
            <span>Registrar Estudo / Nova Revisão</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="content-grid">
      <!-- Theory Studies Section -->
      <section class="content-section theory-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-book"></i>
            Estudos Teóricos
          </h2>
          <span class="section-subtitle">Registre seus estudos e agende primeiro contato</span>
        </div>

        <div class="theory-list">
          <div v-if="theoryStudies.length === 0" class="empty-state">
            <i class="fas fa-book-open"></i>
            <p>Nenhum estudo registrado ainda</p>
            <button @click="handleNewRevision" class="btn-primary">
              Registrar Primeiro Estudo
            </button>
          </div>
          
          <div v-else v-for="study in theoryStudies" :key="study.id" class="theory-card">
            <h3>{{ study.title }}</h3>
            <p>{{ study.subject }} - {{ study.difficulty }}</p>
            <small>Primeiro contato em: {{ formatDate(study.firstContactDate) }}</small>
          </div>
        </div>
      </section>

      <!-- Calendar Section -->
      <section class="content-section calendar-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-calendar-alt"></i>
            Calendário de Revisões
          </h2>
          <span class="section-subtitle">Suas revisões agendadas</span>
        </div>

        <div class="calendar-simple">
          <div v-if="spacedRevisions.length === 0" class="empty-state">
            <i class="fas fa-calendar"></i>
            <p>Nenhuma revisão agendada</p>
          </div>
          
          <div v-else class="revision-list">
            <div v-for="revision in spacedRevisions" :key="revision.id" class="revision-item">
              <div class="revision-info">
                <h4>{{ revision.title }}</h4>
                <p>{{ revision.subject }} - {{ formatDate(revision.date) }}</p>
              </div>
              <div class="revision-actions">
                <button @click="markComplete(revision.id)" class="btn-small">
                  <i class="fas fa-check"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Debug Panel -->
    <div class="debug-panel">
      <h4>Debug Info:</h4>
      <p>Modal State: {{ showModal }}</p>
      <p>Theory Studies: {{ theoryStudies.length }}</p>
      <p>Revisions: {{ spacedRevisions.length }}</p>
      <button @click="clearAll" class="btn-danger">Clear All Data</button>
    </div>

    <!-- MODAL AQUI -->
    <teleport to="body">
      <div v-if="showModal" class="modal-backdrop" @click.self="closeModal">
        <div class="modal-container">
          <NewRevisionModal
            @close="closeModal"
            @save="handleSaveRevision"
            @saveStudy="handleSaveStudy"
          />
        </div>
      </div>
    </teleport>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import NewRevisionModal from './NewRevisionModal.vue';

export default {
  name: 'SpacedRevisionSystemFixed',
  components: {
    NewRevisionModal
  },
  setup() {
    const store = useStore();
    
    // Estado do Modal
    const showModal = ref(false);
    
    // Dados
    const theoryStudies = ref([]);
    const spacedRevisions = ref([]);
    
    // Métodos do Modal
    const handleNewRevision = () => {
      console.log('🚀 ABRINDO MODAL');
      showModal.value = true;
    };
    
    const closeModal = () => {
      console.log('❌ FECHANDO MODAL');
      showModal.value = false;
    };
    
    // Métodos de Save
    const handleSaveStudy = (studyData) => {
      console.log('📚 SALVANDO ESTUDO:', studyData);
      
      // Calcular data do primeiro contato
      const daysToAdd = studyData.difficulty === 'Fácil' ? 2 : 1;
      const firstContactDate = new Date(studyData.date);
      firstContactDate.setDate(firstContactDate.getDate() + daysToAdd);
      
      // Adicionar estudo
      const newStudy = {
        ...studyData,
        id: Date.now(),
        firstContactDate
      };
      
      theoryStudies.value.push(newStudy);
      
      // Criar primeiro contato
      const firstContact = {
        id: Date.now() + 1,
        title: `Questões - ${studyData.title}`,
        subject: studyData.subject,
        date: firstContactDate,
        type: 'questions',
        studyId: newStudy.id
      };
      
      spacedRevisions.value.push(firstContact);
      
      // Salvar no localStorage
      saveToLocalStorage();
      
      // Fechar modal
      closeModal();
      
      alert(`✅ Estudo registrado! Primeiro contato agendado para ${firstContactDate.toLocaleDateString('pt-BR')}`);
    };
    
    const handleSaveRevision = (revisionData) => {
      console.log('📅 SALVANDO REVISÃO:', revisionData);
      
      const newRevision = {
        ...revisionData,
        id: Date.now(),
        type: 'manual'
      };
      
      spacedRevisions.value.push(newRevision);
      
      // Salvar no localStorage
      saveToLocalStorage();
      
      // Fechar modal
      closeModal();
      
      alert('✅ Revisão agendada com sucesso!');
    };
    
    // Métodos auxiliares
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR');
    };
    
    const markComplete = (id) => {
      const index = spacedRevisions.value.findIndex(r => r.id === id);
      if (index !== -1) {
        spacedRevisions.value.splice(index, 1);
        saveToLocalStorage();
      }
    };
    
    const clearAll = () => {
      if (confirm('Tem certeza que deseja limpar todos os dados?')) {
        theoryStudies.value = [];
        spacedRevisions.value = [];
        localStorage.removeItem('spacedRevisionData');
      }
    };
    
    // LocalStorage
    const saveToLocalStorage = () => {
      const data = {
        theoryStudies: theoryStudies.value,
        spacedRevisions: spacedRevisions.value
      };
      localStorage.setItem('spacedRevisionData', JSON.stringify(data));
    };
    
    const loadFromLocalStorage = () => {
      const saved = localStorage.getItem('spacedRevisionData');
      if (saved) {
        try {
          const data = JSON.parse(saved);
          theoryStudies.value = data.theoryStudies || [];
          spacedRevisions.value = data.spacedRevisions || [];
        } catch (error) {
          console.error('Erro ao carregar dados:', error);
        }
      }
    };
    
    // Lifecycle
    onMounted(() => {
      console.log('🎯 COMPONENTE MONTADO');
      loadFromLocalStorage();
    });
    
    return {
      // Estado
      showModal,
      theoryStudies,
      spacedRevisions,
      
      // Métodos
      handleNewRevision,
      closeModal,
      handleSaveStudy,
      handleSaveRevision,
      formatDate,
      markComplete,
      clearAll
    };
  }
};
</script>

<style scoped>
/* Base Styles */
.spaced-revision-system {
  min-height: 100vh;
  background: #0f1419;
  color: #e4e6eb;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  top: -200px;
  left: -200px;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  bottom: -100px;
  right: -100px;
}

.orb-3 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
}

/* Header */
.revision-header {
  position: relative;
  z-index: 1;
  padding: 2rem;
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.page-subtitle {
  margin: 0.25rem 0 0;
  color: #94a3b8;
  font-size: 1rem;
}

/* Buttons */
.action-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  font-size: 1rem;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

/* Content */
.content-grid {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.content-section {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  display: block;
  margin-top: 0.25rem;
  margin-left: 2.25rem;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #94a3b8;
}

.empty-state i {
  font-size: 3rem;
  opacity: 0.5;
  margin-bottom: 1rem;
  display: block;
}

.empty-state p {
  margin-bottom: 1.5rem;
}

/* Lists */
.theory-card,
.revision-item {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s;
}

.theory-card:hover,
.revision-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.2);
}

.theory-card h3,
.revision-item h4 {
  margin: 0 0 0.5rem;
  color: #e4e6eb;
}

.theory-card p,
.revision-item p {
  margin: 0;
  color: #94a3b8;
  font-size: 0.875rem;
}

.theory-card small {
  color: #6366f1;
  font-size: 0.8rem;
}

.revision-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Debug Panel */
.debug-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #6366f1;
  border-radius: 8px;
  padding: 1rem;
  color: white;
  font-size: 0.875rem;
  z-index: 9999;
}

.debug-panel h4 {
  margin: 0 0 0.5rem;
  color: #6366f1;
}

.debug-panel p {
  margin: 0.25rem 0;
}

/* Buttons */
.btn-primary {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-small {
  padding: 0.5rem;
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 6px;
  color: #10b981;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-small:hover {
  background: rgba(16, 185, 129, 0.3);
}

.btn-danger {
  padding: 0.5rem 1rem;
  background: #ef4444;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  margin-top: 0.5rem;
}

/* Modal Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
}

.modal-container {
  position: relative;
  z-index: 1000000;
}

/* Responsive */
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }
}
</style>