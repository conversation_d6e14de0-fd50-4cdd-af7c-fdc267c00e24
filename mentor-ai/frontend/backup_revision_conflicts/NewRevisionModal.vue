<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="revision-modal">
      <div class="modal-header">
        <div class="modal-title">
          <i class="fas fa-calendar-plus"></i>
          <h2>Registrar Estudo e Agendar Revisão</h2>
        </div>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Tabs para escolher tipo -->
      <div class="revision-tabs">
        <button 
          type="button"
          @click="activeTab = 'study'"
          :class="{ active: activeTab === 'study' }"
          class="tab-button"
        >
          <i class="fas fa-book"></i>
          Registrar Estudo Teórico
        </button>
        <button 
          type="button"
          @click="activeTab = 'questions'"
          :class="{ active: activeTab === 'questions' }"
          class="tab-button"
        >
          <i class="fas fa-check-circle"></i>
          Registrar Questões Resolvidas
        </button>
        <button 
          type="button"
          @click="activeTab = 'manual'"
          :class="{ active: activeTab === 'manual' }"
          class="tab-button"
        >
          <i class="fas fa-edit"></i>
          Criar Revisão Manual
        </button>
      </div>

      <!-- Formulário de Estudo Teórico -->
      <form v-if="activeTab === 'study'" @submit.prevent="handleStudySubmit" class="modal-body">
        <div class="info-box">
          <i class="fas fa-info-circle"></i>
          <p>Registre seu estudo teórico. O sistema agendará automaticamente o primeiro contato com questões baseado na dificuldade do conteúdo.</p>
        </div>

        <div class="form-group">
          <label>O que você estudou?</label>
          <input 
            v-model="studyData.title" 
            type="text" 
            class="form-control" 
            placeholder="Ex: Sistema Cardiovascular - Anatomia"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="studyData.subject" class="form-control" required>
              <option value="">Selecione...</option>
              <option value="Anatomia">Anatomia</option>
              <option value="Fisiologia">Fisiologia</option>
              <option value="Patologia">Patologia</option>
              <option value="Farmacologia">Farmacologia</option>
              <option value="Microbiologia">Microbiologia</option>
              <option value="Bioquímica">Bioquímica</option>
              <option value="Clínica Médica">Clínica Médica</option>
              <option value="Cirurgia">Cirurgia</option>
              <option value="Pediatria">Pediatria</option>
              <option value="Ginecologia">Ginecologia</option>
            </select>
          </div>

          <div class="form-group">
            <label>Data do Estudo</label>
            <input 
              v-model="studyData.date" 
              type="date" 
              class="form-control"
              :max="today"
              required
            />
          </div>
        </div>

        <div class="form-group">
          <label>Dificuldade do Conteúdo</label>
          <div class="difficulty-selector">
            <button 
              v-for="diff in ['Fácil', 'Difícil']" 
              :key="diff"
              type="button"
              @click="studyData.difficulty = diff"
              :class="{ active: studyData.difficulty === diff }"
              class="difficulty-btn"
            >
              <i :class="getDifficultyIcon(diff)"></i>
              <span>{{ diff }}</span>
              <small>{{ diff === 'Fácil' ? 'Primeiro contato em 2 dias' : 'Primeiro contato em 1 dia' }}</small>
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>Tempo de Estudo</label>
          <input 
            v-model="studyData.duration" 
            type="number" 
            class="form-control" 
            placeholder="Minutos estudados"
            min="1"
            required
          />
        </div>

        <div class="form-group">
          <label>Observações (opcional)</label>
          <textarea 
            v-model="studyData.notes" 
            class="form-control" 
            rows="3"
            placeholder="Pontos importantes, dúvidas, etc..."
          ></textarea>
        </div>

        <div class="schedule-preview">
          <h4><i class="fas fa-calendar-check"></i> Agendamento Automático</h4>
          <p>Primeiro contato com questões será agendado para: <strong>{{ getFirstContactDate() }}</strong></p>
          <p class="schedule-info">Após resolver as questões, o sistema calculará automaticamente os próximos intervalos de revisão baseado no seu desempenho.</p>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i>
            Registrar Estudo
          </button>
        </div>
      </form>

      <!-- Formulário de Questões Resolvidas -->
      <form v-if="activeTab === 'questions'" @submit.prevent="handleQuestionsSubmit" class="modal-body">
        <div class="info-box info-box-warning">
          <i class="fas fa-brain"></i>
          <p>Registre questões já resolvidas. O sistema calculará automaticamente sua próxima revisão baseado no desempenho usando a metodologia de repetição espaçada.</p>
        </div>

        <div class="form-group">
          <label>Conteúdo das Questões</label>
          <input 
            v-model="questionsData.title" 
            type="text" 
            class="form-control" 
            placeholder="Ex: Sistema Cardiovascular - Fisiologia"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="questionsData.subject" class="form-control" required>
              <option value="">Selecione...</option>
              <option value="Anatomia">Anatomia</option>
              <option value="Fisiologia">Fisiologia</option>
              <option value="Patologia">Patologia</option>
              <option value="Farmacologia">Farmacologia</option>
              <option value="Microbiologia">Microbiologia</option>
              <option value="Bioquímica">Bioquímica</option>
              <option value="Clínica Médica">Clínica Médica</option>
              <option value="Cirurgia">Cirurgia</option>
              <option value="Pediatria">Pediatria</option>
              <option value="Ginecologia">Ginecologia</option>
            </select>
          </div>

          <div class="form-group">
            <label>Data de Resolução</label>
            <input 
              v-model="questionsData.date" 
              type="date" 
              class="form-control"
              :max="today"
              required
            />
          </div>
        </div>

        <div class="performance-section">
          <h4><i class="fas fa-chart-bar"></i> Desempenho</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Total de Questões</label>
              <input 
                v-model.number="questionsData.totalQuestions" 
                type="number" 
                class="form-control" 
                placeholder="30"
                min="1"
                @input="updatePercentage"
                required
              />
            </div>

            <div class="form-group">
              <label>Questões Corretas</label>
              <input 
                v-model.number="questionsData.correctAnswers" 
                type="number" 
                class="form-control" 
                placeholder="24"
                min="0"
                :max="questionsData.totalQuestions"
                @input="updatePercentage"
                required
              />
            </div>

            <div class="form-group">
              <label>Tempo Médio/Questão</label>
              <div class="time-input-group">
                <input 
                  v-model.number="questionsData.averageTime" 
                  type="number" 
                  class="form-control" 
                  placeholder="90"
                  min="1"
                />
                <span class="time-unit">segundos</span>
              </div>
            </div>
          </div>

          <div class="performance-display" v-if="questionsData.totalQuestions > 0">
            <div class="performance-metric">
              <span class="metric-label">Acertos</span>
              <span class="metric-value" :class="getPerformanceClass()">
                {{ questionsPercentage }}%
              </span>
            </div>
            <div class="performance-metric">
              <span class="metric-label">Próxima Revisão</span>
              <span class="metric-value">{{ getNextRevisionPreview() }}</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label>Observações (opcional)</label>
          <textarea 
            v-model="questionsData.notes" 
            class="form-control" 
            rows="3"
            placeholder="Tópicos com dificuldade, dúvidas persistentes..."
          ></textarea>
        </div>

        <div class="schedule-preview schedule-preview-smart">
          <h4><i class="fas fa-robot"></i> Análise Inteligente</h4>
          <div v-if="questionsPercentage > 0" class="analysis-preview">
            <p><strong>Desempenho:</strong> {{ getPerformanceMessage() }}</p>
            <p><strong>Intervalo calculado:</strong> {{ getCalculatedInterval() }} dias</p>
            <p><strong>Próxima revisão:</strong> {{ getNextRevisionDate() }}</p>
            <div v-if="getRecommendations().length > 0" class="recommendations-preview">
              <p><strong>Recomendações:</strong></p>
              <ul>
                <li v-for="(rec, index) in getRecommendations()" :key="index">{{ rec }}</li>
              </ul>
            </div>
          </div>
          <p v-else class="analysis-hint">Preencha os dados de desempenho para ver a análise.</p>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary" :disabled="questionsData.totalQuestions === 0">
            <i class="fas fa-save"></i>
            Registrar e Agendar
          </button>
        </div>
      </form>

      <!-- Formulário de Revisão Manual -->
      <form v-if="activeTab === 'manual'" @submit.prevent="handleManualSubmit" class="modal-body">
        <div class="info-box">
          <i class="fas fa-info-circle"></i>
          <p>Crie uma revisão manual para conteúdos que você já conhece ou para revisões personalizadas.</p>
        </div>

        <div class="form-group">
          <label>Título da Revisão</label>
          <input 
            v-model="manualData.title" 
            type="text" 
            class="form-control" 
            placeholder="Ex: Revisão de Anatomia Cardiovascular"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Data</label>
            <input 
              v-model="manualData.date" 
              type="date" 
              class="form-control"
              :min="today"
              required
            />
          </div>

          <div class="form-group">
            <label>Horário</label>
            <div class="time-inputs">
              <input 
                v-model="manualData.startTime" 
                type="time" 
                class="form-control"
                required
              />
              <span class="time-separator">até</span>
              <input 
                v-model="manualData.endTime" 
                type="time" 
                class="form-control"
                required
              />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="manualData.subject" class="form-control" required>
              <option value="">Selecione...</option>
              <option value="Anatomia">Anatomia</option>
              <option value="Fisiologia">Fisiologia</option>
              <option value="Patologia">Patologia</option>
              <option value="Farmacologia">Farmacologia</option>
              <option value="Microbiologia">Microbiologia</option>
              <option value="Bioquímica">Bioquímica</option>
              <option value="Clínica Médica">Clínica Médica</option>
              <option value="Cirurgia">Cirurgia</option>
              <option value="Pediatria">Pediatria</option>
              <option value="Ginecologia">Ginecologia</option>
            </select>
          </div>

          <div class="form-group">
            <label>Tipo de Revisão</label>
            <select v-model="manualData.revisionType" class="form-control" required>
              <option value="Teórica">Teórica</option>
              <option value="Prática">Prática (Questões)</option>
              <option value="Mista">Mista</option>
              <option value="Flashcards">Flashcards</option>
              <option value="Resumo">Resumo</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Prioridade</label>
          <div class="priority-selector">
            <button 
              v-for="priority in ['Baixa', 'Média', 'Alta']" 
              :key="priority"
              type="button"
              @click="manualData.priority = priority"
              :class="{ active: manualData.priority === priority }"
              class="priority-btn"
            >
              <i :class="getPriorityIcon(priority)"></i>
              {{ priority }}
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>Descrição (opcional)</label>
          <textarea 
            v-model="manualData.description" 
            class="form-control" 
            rows="3"
            placeholder="Adicione detalhes sobre o conteúdo a revisar..."
          ></textarea>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i>
            Criar Revisão
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  name: 'NewRevisionModal',
  emits: ['close', 'save', 'saveStudy', 'saveQuestions'],
  setup(props, { emit }) {
    const activeTab = ref('study');
    
    // Dados do estudo teórico
    const studyData = ref({
      title: '',
      subject: '',
      date: new Date().toISOString().split('T')[0],
      difficulty: 'Fácil',
      duration: '',
      notes: ''
    });

    // Dados de questões resolvidas
    const questionsData = ref({
      title: '',
      subject: '',
      date: new Date().toISOString().split('T')[0],
      totalQuestions: 0,
      correctAnswers: 0,
      averageTime: 90,
      notes: ''
    });

    // Dados da revisão manual
    const manualData = ref({
      title: '',
      date: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '10:00',
      subject: '',
      revisionType: 'Teórica',
      priority: 'Média',
      description: ''
    });
    
    // Computed para porcentagem
    const questionsPercentage = computed(() => {
      if (questionsData.value.totalQuestions === 0) return 0;
      return Math.round((questionsData.value.correctAnswers / questionsData.value.totalQuestions) * 100);
    });

    const today = computed(() => new Date().toISOString().split('T')[0]);

    const getDifficultyIcon = (difficulty) => {
      return difficulty === 'Fácil' ? 'fas fa-smile' : 'fas fa-fire';
    };

    const getPriorityIcon = (priority) => {
      const icons = {
        'Baixa': 'fas fa-flag',
        'Média': 'fas fa-flag',
        'Alta': 'fas fa-fire'
      };
      return icons[priority];
    };

    const getFirstContactDate = () => {
      const studyDate = new Date(studyData.value.date);
      const daysToAdd = studyData.value.difficulty === 'Fácil' ? 2 : 1;
      studyDate.setDate(studyDate.getDate() + daysToAdd);
      return studyDate.toLocaleDateString('pt-BR', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    };

    const handleStudySubmit = () => {
      // Calcular data da primeira revisão baseada na dificuldade
      const studyDate = new Date(studyData.value.date);
      const daysToAdd = studyData.value.difficulty === 'Fácil' ? 2 : 1;
      const firstRevisionDate = new Date(studyDate);
      firstRevisionDate.setDate(firstRevisionDate.getDate() + daysToAdd);
      
      const study = {
        ...studyData.value,
        id: Date.now(),
        type: 'theoretical',
        // Dados para o sistema de revisão inteligente
        scheduling: {
          firstRevisionDate,
          revisionType: 'questions', // Primeiro contato sempre com questões
          baseInterval: daysToAdd,
          currentCycle: 0,
          // Metadados para rastreamento
          metadata: {
            studyDuration: parseInt(studyData.value.duration),
            difficulty: studyData.value.difficulty,
            subject: studyData.value.subject,
            createdAt: new Date(),
            lastModified: new Date()
          }
        }
      };
      
      emit('saveStudy', study);
    };

    // Funções para análise inteligente
    const updatePercentage = () => {
      // Força atualização da computed
    };
    
    const getPerformanceClass = () => {
      const percentage = questionsPercentage.value;
      if (percentage >= 80) return 'performance-excellent';
      if (percentage >= 65) return 'performance-good';
      if (percentage >= 50) return 'performance-regular';
      return 'performance-poor';
    };
    
    const getPerformanceMessage = () => {
      const percentage = questionsPercentage.value;
      if (percentage >= 80) return 'Excelente! Domínio consolidado do conteúdo.';
      if (percentage >= 65) return 'Bom desempenho. Continue assim!';
      if (percentage >= 50) return 'Regular. Necessita mais prática.';
      return 'Precisa revisar os conceitos fundamentais.';
    };
    
    const getCalculatedInterval = () => {
      const percentage = questionsPercentage.value;
      const confidence = calculateConfidence();
      
      // Base interval usando metodologia
      let baseInterval;
      if (percentage >= 90) baseInterval = 30;
      else if (percentage >= 85) baseInterval = 21;
      else if (percentage >= 80) baseInterval = 14;
      else if (percentage >= 75) baseInterval = 10;
      else if (percentage >= 70) baseInterval = 7;
      else if (percentage >= 65) baseInterval = 5;
      else if (percentage >= 60) baseInterval = 3;
      else if (percentage >= 55) baseInterval = 2;
      else baseInterval = 1;
      
      // Aplicar modificadores
      let modifier = 1.0;
      if (confidence > 80) modifier *= 1.2;
      else if (confidence < 50) modifier *= 0.8;
      
      // Modificador de dificuldade do assunto
      const difficultyMap = {
        'Anatomia': 0.8,
        'Fisiologia': 0.85,
        'Farmacologia': 0.75,
        'Patologia': 0.8,
        'Clínica Médica': 0.9,
        'Cirurgia': 0.85,
        'Pediatria': 0.9,
        'Ginecologia': 0.9
      };
      
      modifier *= difficultyMap[questionsData.value.subject] || 1.0;
      
      return Math.max(1, Math.round(baseInterval * modifier));
    };
    
    const calculateConfidence = () => {
      if (questionsData.value.totalQuestions === 0) return 0;
      
      const avgTime = questionsData.value.averageTime || 90;
      const timeFactor = avgTime < 30 ? 0.7 : 
                        avgTime > 180 ? 0.9 : 
                        1.0;
      
      const accuracyFactor = questionsData.value.correctAnswers / questionsData.value.totalQuestions;
      
      return Math.round(accuracyFactor * timeFactor * 100);
    };
    
    const getNextRevisionPreview = () => {
      const days = getCalculatedInterval();
      return `em ${days} dia${days > 1 ? 's' : ''}`;
    };
    
    const getNextRevisionDate = () => {
      const days = getCalculatedInterval();
      const date = new Date();
      date.setDate(date.getDate() + days);
      
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    };
    
    const getRecommendations = () => {
      const recommendations = [];
      const percentage = questionsPercentage.value;
      const confidence = calculateConfidence();
      
      if (percentage < 60) {
        recommendations.push('Revisar conceitos fundamentais');
        recommendations.push('Fazer mais exercícios práticos');
      }
      
      if (confidence < 60) {
        recommendations.push('Dedicar mais tempo por questão');
      }
      
      if (questionsData.value.averageTime < 45) {
        recommendations.push('Ler questões com mais atenção');
      }
      
      return recommendations;
    };
    
    const handleQuestionsSubmit = () => {
      const nextRevisionDays = getCalculatedInterval();
      const nextRevisionDate = new Date();
      nextRevisionDate.setDate(nextRevisionDate.getDate() + nextRevisionDays);
      
      const result = {
        ...questionsData.value,
        id: Date.now(),
        type: 'questions',
        percentage: questionsPercentage.value,
        wrongAnswers: questionsData.value.totalQuestions - questionsData.value.correctAnswers,
        
        // Análise detalhada
        analysis: {
          confidence: calculateConfidence(),
          nextRevisionDays,
          recommendations: getRecommendations()
        },
        
        // Dados para agendamento
        scheduling: {
          subject: questionsData.value.subject,
          topic: questionsData.value.title,
          nextRevisionDate,
          priority: questionsPercentage.value < 60 ? 'high' : 
                   questionsPercentage.value < 75 ? 'medium' : 'low',
          
          // Metadados para rastreamento
          metadata: {
            totalQuestions: questionsData.value.totalQuestions,
            correctAnswers: questionsData.value.correctAnswers,
            wrongAnswers: questionsData.value.totalQuestions - questionsData.value.correctAnswers,
            confidence: calculateConfidence(),
            averageTime: questionsData.value.averageTime,
            studySessionId: Date.now()
          }
        }
      };
      
      emit('saveQuestions', result);
    };

    const handleManualSubmit = () => {
      const startDateTime = new Date(`${manualData.value.date}T${manualData.value.startTime}`);
      const endDateTime = new Date(`${manualData.value.date}T${manualData.value.endTime}`);
      
      const revision = {
        id: Date.now(),
        title: manualData.value.title,
        date: startDateTime,
        startTime: manualData.value.startTime,
        endTime: manualData.value.endTime,
        subject: manualData.value.subject,
        revisionType: manualData.value.revisionType,
        priority: manualData.value.priority,
        description: manualData.value.description,
        completed: false,
        isManual: true
      };
      
      emit('save', revision);
    };

    return {
      activeTab,
      studyData,
      questionsData,
      manualData,
      questionsPercentage,
      today,
      getDifficultyIcon,
      getPriorityIcon,
      getFirstContactDate,
      updatePercentage,
      getPerformanceClass,
      getPerformanceMessage,
      getCalculatedInterval,
      getNextRevisionPreview,
      getNextRevisionDate,
      getRecommendations,
      handleStudySubmit,
      handleQuestionsSubmit,
      handleManualSubmit
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  animation: fadeIn 0.3s;
}

.revision-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-title i {
  font-size: 1.5rem;
  color: #6366f1;
}

.modal-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Tabs */
.revision-tabs {
  display: flex;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.3);
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
}

.tab-button:hover {
  background: rgba(99, 102, 241, 0.05);
  color: #e4e6eb;
}

.tab-button.active {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #6366f1;
}

/* Info Box */
.info-box {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.info-box i {
  color: #6366f1;
  font-size: 1.125rem;
  margin-top: 0.125rem;
}

.info-box p {
  margin: 0;
  color: #e4e6eb;
  font-size: 0.875rem;
  line-height: 1.5;
}

.modal-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-separator {
  color: #64748b;
  font-size: 0.875rem;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.difficulty-btn {
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
}

.difficulty-btn i {
  font-size: 1.5rem;
}

.difficulty-btn span {
  font-weight: 600;
  color: #e4e6eb;
}

.difficulty-btn small {
  font-size: 0.75rem;
  color: #64748b;
}

.difficulty-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
}

.difficulty-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.difficulty-btn.active span {
  color: #6366f1;
}

/* Priority Selector */
.priority-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.priority-btn {
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.priority-btn i {
  font-size: 1.25rem;
}

.priority-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.priority-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

/* Schedule Preview */
.schedule-preview {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.schedule-preview h4 {
  margin: 0 0 0.75rem;
  color: #10b981;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.schedule-preview p {
  margin: 0.5rem 0;
  color: #e4e6eb;
  font-size: 0.875rem;
}

.schedule-preview strong {
  color: #10b981;
}

.schedule-info {
  color: #94a3b8 !important;
  font-size: 0.8rem !important;
  margin-top: 0.75rem !important;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.3);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Performance Section */
.performance-section {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.performance-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #6366f1;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-unit {
  color: #94a3b8;
  font-size: 0.875rem;
}

.performance-display {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.performance-metric {
  text-align: center;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 10px;
}

.metric-label {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 700;
}

.metric-value.performance-excellent {
  color: #10b981;
}

.metric-value.performance-good {
  color: #3b82f6;
}

.metric-value.performance-regular {
  color: #f59e0b;
}

.metric-value.performance-poor {
  color: #ef4444;
}

/* Schedule Preview Smart */
.schedule-preview-smart {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(6, 182, 212, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.schedule-preview-smart h4 {
  color: #10b981;
}

.analysis-preview {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 10px;
  padding: 1.25rem;
}

.analysis-preview p {
  margin: 0.5rem 0;
  color: #e4e6eb;
  font-size: 0.875rem;
}

.analysis-preview strong {
  color: #94a3b8;
  font-weight: 600;
}

.recommendations-preview {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.recommendations-preview ul {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0;
}

.recommendations-preview li {
  color: #e4e6eb;
  padding: 0.25rem 0;
  padding-left: 1.25rem;
  position: relative;
  font-size: 0.813rem;
}

.recommendations-preview li:before {
  content: "→";
  position: absolute;
  left: 0;
  color: #f59e0b;
}

.analysis-hint {
  text-align: center;
  color: #94a3b8;
  font-style: italic;
  padding: 2rem;
}

.info-box-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.info-box-warning i {
  color: #f59e0b;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar */
.revision-modal::-webkit-scrollbar {
  width: 8px;
}

.revision-modal::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.revision-modal::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.revision-modal::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>