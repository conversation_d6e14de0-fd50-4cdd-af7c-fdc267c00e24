# 🚀 Guia de Teste Rápido - Sistema de Revisões Espaçadas

## Para testar o sistema implementado:

### 1. Iniciar o servidor
```bash
cd frontend
npm run serve
```

### 2. Acessar a página de revisões
```
http://localhost:8080/revisoes
```

### 3. Testar o fluxo completo

#### 📚 Passo 1: Registrar Estudo Teórico
1. Clique no botão **"+ Nova Revisão"** (botão roxo destacado)
2. Na janela que abrir, você verá 3 abas
3. Na aba **"📚 Estudo Teórico"**, preencha:
   - **Título**: "Sistema Cardiovascular - Anatomia"
   - **Matéria**: "Anatomia"
   - **Data**: Hoje
   - **Tempo**: 120 minutos
   - **Dificuldade**: Selecione **"Difícil"** (primeiro contato em 1 dia)
   - **Notas**: "<PERSON><PERSON><PERSON><PERSON> vas<PERSON>, câ<PERSON><PERSON> cardía<PERSON> e circulação"
4. Clique em **"📌 Registrar Estudo e Agendar Revisão"**

#### ✍️ Passo 2: Simular Revisão Prática
1. Clique novamente em **"+ Nova Revisão"**
2. Vá para a aba **"✍️ Revisão Prática"**
3. Selecione o conteúdo que você acabou de registrar
4. Preencha:
   - **Total de Questões**: 30
   - **Questões Corretas**: 18 (60% de acerto)
5. Observe que o sistema calcula automaticamente:
   - Percentual: 60%
   - Próxima revisão em: 14 dias
   - Recomendação: "Desempenho regular. Reforce o conteúdo."
6. Clique em **"✅ Registrar Desempenho e Agendar Próxima Revisão"**

#### 📊 Passo 3: Verificar Histórico
1. Clique novamente em **"+ Nova Revisão"**
2. Vá para a aba **"📊 Histórico"**
3. Você verá:
   - Total de Estudos: 1
   - Revisões Realizadas: 1
   - Taxa Média de Acerto: 60%
   - Timeline com todos os eventos

### 4. Verificar integração com calendário
1. Volte para a página principal de revisões
2. Observe as estatísticas no topo:
   - Estudos Registrados
   - Questões Resolvidas
   - Taxa de Acerto
   - Revisões Pendentes
3. Na aba "Próximas Revisões", você verá a revisão agendada para 14 dias

### 5. Testar diferentes cenários

#### Cenário A: Conteúdo Fácil
- Registre um estudo com dificuldade **"Fácil"**
- Observe que o primeiro contato é agendado para **2 dias**

#### Cenário B: Alto Desempenho
- Na revisão prática, registre 27/30 acertos (90%)
- Observe que a próxima revisão é agendada para **35 dias**

#### Cenário C: Baixo Desempenho
- Na revisão prática, registre 12/30 acertos (40%)
- Observe que a próxima revisão é agendada para apenas **2 dias**

### 6. Verificar persistência
1. Recarregue a página (F5)
2. Todos os dados devem estar mantidos
3. As revisões agendadas continuam visíveis

## 🎯 Pontos-chave para observar:

1. **Interface em Janela**: O botão "+ Nova Revisão" abre uma janela modal com todo o sistema
2. **Cálculo Automático**: Datas são calculadas automaticamente baseadas na dificuldade/desempenho
3. **Visualização em Tempo Real**: Preview das datas antes de salvar
4. **Recomendações Inteligentes**: Sistema sugere ações baseadas no desempenho
5. **Histórico Completo**: Timeline visual de todos os estudos e revisões

## 🐛 Troubleshooting:

Se algo não funcionar:
1. Abra o Console do navegador (F12)
2. Verifique se há erros
3. Limpe o LocalStorage se necessário:
   ```javascript
   localStorage.clear()
   ```
4. Recarregue a página

---

**Sistema implementado conforme especificações do método de revisões espaçadas!**