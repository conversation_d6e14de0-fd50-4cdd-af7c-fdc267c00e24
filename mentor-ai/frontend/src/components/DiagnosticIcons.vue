<template>
  <div class="diagnostic-container">
    <h1>Diagnóstico de Estilos de Ícones</h1>
    
    <!-- Seção 1: Ícones diretamente -->
    <section>
      <h2>1. Ícon<PERSON> FontAwesome (sem classes extras)</h2>
      <div class="icon-row">
        <font-awesome-icon icon="clock" />
        <font-awesome-icon icon="calendar-week" />
        <font-awesome-icon icon="trophy" />
        <font-awesome-icon icon="chart-bar" />
      </div>
    </section>

    <!-- Seção 2: Com classe revision-detail-icon -->
    <section>
      <h2>2. Com classe revision-detail-icon</h2>
      <div class="icon-row">
        <font-awesome-icon icon="clock" class="revision-detail-icon" />
        <font-awesome-icon icon="calendar-week" class="revision-detail-icon" />
        <font-awesome-icon icon="trophy" class="revision-detail-icon" />
        <font-awesome-icon icon="chart-bar" class="revision-detail-icon" />
      </div>
    </section>

    <!-- Seção 3: Estrutura completa como no RevisionScheduler -->
    <section>
      <h2>3. Estrutura completa (detail-item + revision-detail-icon)</h2>
      <div>
        <span class="detail-item">
          <font-awesome-icon icon="clock" class="revision-detail-icon" />
          0 hoje
        </span>
        <span class="detail-item">
          <font-awesome-icon icon="calendar-week" class="revision-detail-icon" />
          5 esta semana
        </span>
      </div>
    </section>

    <!-- Seção 4: Renderização de SVG direto -->
    <section>
      <h2>4. SVG renderizado pelo Vue</h2>
      <div id="svg-container" v-html="svgContent"></div>
    </section>

    <!-- Seção 5: Análise de estilos computados -->
    <section>
      <h2>5. Análise de Estilos</h2>
      <div id="style-analysis">
        <p>Clique em qualquer ícone acima para ver seus estilos computados</p>
        <pre v-if="computedStyles">{{ computedStyles }}</pre>
      </div>
    </section>

    <!-- Seção 6: Teste com elemento nativo -->
    <section>
      <h2>6. Teste de Background em Elementos Nativos</h2>
      <div class="test-backgrounds">
        <div class="bg-test" style="background: rgba(255, 255, 255, 0.1); padding: 1rem;">
          Background com rgba(255, 255, 255, 0.1)
          <font-awesome-icon icon="clock" />
        </div>
        <div class="bg-test" style="background: #333; padding: 1rem; margin-top: 1rem;">
          Background com #333
          <font-awesome-icon icon="clock" />
        </div>
        <div class="bg-test" style="background: transparent; padding: 1rem; margin-top: 1rem; border: 1px solid #555;">
          Background transparent
          <font-awesome-icon icon="clock" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'DiagnosticIcons',
  setup() {
    const svgContent = ref('')
    const computedStyles = ref(null)

    onMounted(() => {
      // Captura o SVG renderizado
      setTimeout(() => {
        const firstIcon = document.querySelector('.revision-detail-icon svg')
        if (firstIcon) {
          svgContent.value = firstIcon.outerHTML
        }

        // Adiciona event listeners para análise de estilos
        document.querySelectorAll('svg').forEach(svg => {
          svg.addEventListener('click', (e) => {
            e.stopPropagation()
            analyzeElement(svg)
          })
          
          // Também analisa o elemento pai se houver
          if (svg.parentElement) {
            svg.parentElement.addEventListener('click', (e) => {
              e.stopPropagation()
              analyzeElement(svg.parentElement)
            })
          }
        })
      }, 100)
    })

    const analyzeElement = (element) => {
      const styles = window.getComputedStyle(element)
      const relevantStyles = {
        background: styles.background,
        backgroundColor: styles.backgroundColor,
        backgroundImage: styles.backgroundImage,
        padding: styles.padding,
        margin: styles.margin,
        width: styles.width,
        height: styles.height,
        display: styles.display,
        position: styles.position,
        className: element.className,
        tagName: element.tagName,
        parentClassName: element.parentElement?.className || 'none',
        parentTagName: element.parentElement?.tagName || 'none'
      }
      
      computedStyles.value = JSON.stringify(relevantStyles, null, 2)
    }

    return {
      svgContent,
      computedStyles
    }
  }
}
</script>

<style scoped>
.diagnostic-container {
  padding: 2rem;
  background: #0f172a;
  color: white;
  min-height: 100vh;
}

section {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #333;
  border-radius: 8px;
}

h2 {
  color: #10b981;
  margin-bottom: 1rem;
}

.icon-row {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.icon-row > * {
  cursor: pointer;
  padding: 0.5rem;
  border: 1px dashed #555;
}

.icon-row > *:hover {
  border-color: #10b981;
}

#svg-container {
  background: #1e293b;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
}

pre {
  background: #1e293b;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
}

/* IMPORTANTE: Sem estilos adicionais para os ícones */
</style>