<template>
  <div class="ultra-revision-system">
    <!-- Animated Background -->
    <div class="animated-bg">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="grid-pattern"></div>
    </div>

    <!-- Ultra Header -->
    <header class="ultra-header">
      <div class="header-container">
        <div class="header-brand">
          <div class="brand-icon">
            <i class="fas fa-brain"></i>
            <div class="pulse-ring"></div>
          </div>
          <div class="brand-info">
            <h1 class="brand-title">
              Sistema de Revisões Espaçadas
              <span class="badge-new">ULTRA</span>
            </h1>
            <p class="brand-subtitle">
              <i class="fas fa-chart-line"></i>
              Metodologia científica comprovada para retenção de 95% do conteúdo
            </p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="openRevisionModal" class="ultra-btn primary animated">
            <span class="btn-icon">
              <i class="fas fa-plus-circle"></i>
            </span>
            <span class="btn-text">Registrar Estudo / Nova Revisão</span>
            <span class="btn-badge">{{ todayPending }}</span>
          </button>
          
          <button @click="showStats = !showStats" class="ultra-btn secondary">
            <i class="fas fa-chart-bar"></i>
            <span class="btn-text">Estatísticas</span>
          </button>
        </div>
      </div>

      <!-- Quick Stats Bar -->
      <div class="quick-stats-bar">
        <div class="stat-item" v-for="stat in quickStats" :key="stat.label">
          <div class="stat-icon" :style="{ background: stat.color }">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-data">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Dashboard -->
    <main class="ultra-dashboard">
      <!-- Today's Focus Section -->
      <section class="dashboard-section focus-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-fire"></i>
            Foco de Hoje
            <span class="live-indicator">
              <span class="dot"></span>
              Ao vivo
            </span>
          </h2>
          <div class="section-actions">
            <button @click="startFocusMode" class="mini-btn">
              <i class="fas fa-rocket"></i>
              Modo Foco
            </button>
          </div>
        </div>

        <div class="focus-content">
          <div v-if="todayRevisions.length === 0" class="empty-state elegant">
            <div class="empty-icon">
              <i class="fas fa-coffee"></i>
            </div>
            <h3>Você está em dia!</h3>
            <p>Nenhuma revisão pendente para hoje. Que tal registrar um novo estudo?</p>
            <button @click="openRevisionModal" class="ultra-btn primary small">
              <i class="fas fa-plus"></i>
              Registrar Novo Estudo
            </button>
          </div>

          <div v-else class="revision-cards">
            <div v-for="revision in todayRevisions" :key="revision.id" 
                 class="revision-card" 
                 :class="getRevisionCardClass(revision)">
              <div class="card-header">
                <div class="card-subject">{{ revision.subject }}</div>
                <div class="card-priority" :class="revision.priority">
                  <i :class="getPriorityIcon(revision.priority)"></i>
                </div>
              </div>
              
              <h3 class="card-title">{{ revision.title }}</h3>
              
              <div class="card-meta">
                <span class="meta-item">
                  <i class="fas fa-clock"></i>
                  {{ revision.startTime }}
                </span>
                <span class="meta-item">
                  <i class="fas fa-brain"></i>
                  {{ revision.revisionType }}
                </span>
              </div>

              <div class="card-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: revision.progress + '%' }"></div>
                </div>
                <span class="progress-text">{{ revision.progress }}% concluído</span>
              </div>

              <div class="card-actions">
                <button @click="startRevision(revision)" class="action-btn primary">
                  <i class="fas fa-play"></i>
                  Iniciar
                </button>
                <button @click="postponeRevision(revision)" class="action-btn secondary">
                  <i class="fas fa-clock"></i>
                  Adiar
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Study Timeline -->
      <section class="dashboard-section timeline-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-stream"></i>
            Linha do Tempo de Estudos
          </h2>
        </div>

        <div class="study-timeline">
          <div v-for="(day, index) in timeline" :key="index" class="timeline-day">
            <div class="day-header">
              <div class="day-date">{{ formatTimelineDate(day.date) }}</div>
              <div class="day-count">{{ day.items.length }} atividades</div>
            </div>
            
            <div class="timeline-items">
              <div v-for="item in day.items" :key="item.id" 
                   class="timeline-item" 
                   :class="item.type">
                <div class="item-connector"></div>
                <div class="item-dot">
                  <i :class="getTimelineIcon(item.type)"></i>
                </div>
                <div class="item-content">
                  <div class="item-time">{{ item.time }}</div>
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-subject">{{ item.subject }}</div>
                  <div v-if="item.performance" class="item-performance">
                    <span class="performance-badge" :class="getPerformanceClass(item.performance)">
                      {{ item.performance }}% de acerto
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Analytics -->
      <section class="dashboard-section analytics-section" v-if="showStats">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-analytics"></i>
            Análise de Desempenho
          </h2>
        </div>

        <div class="analytics-grid">
          <!-- Retention Curve -->
          <div class="analytics-card large">
            <h3>Curva de Retenção</h3>
            <div class="chart-container">
              <canvas ref="retentionChart"></canvas>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color" style="background: #6366f1"></span>
                <span>Taxa de Retenção</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #10b981"></span>
                <span>Meta (85%)</span>
              </div>
            </div>
          </div>

          <!-- Subject Performance -->
          <div class="analytics-card">
            <h3>Desempenho por Disciplina</h3>
            <div class="subject-stats">
              <div v-for="subject in subjectStats" :key="subject.name" 
                   class="subject-stat">
                <div class="subject-header">
                  <span class="subject-name">{{ subject.name }}</span>
                  <span class="subject-score" :style="{ color: getScoreColor(subject.average) }">
                    {{ subject.average }}%
                  </span>
                </div>
                <div class="subject-bar">
                  <div class="bar-fill" 
                       :style="{ 
                         width: subject.average + '%',
                         background: getScoreColor(subject.average)
                       }"></div>
                </div>
                <div class="subject-meta">
                  <span>{{ subject.revisions }} revisões</span>
                  <span>{{ subject.streak }} dias seguidos</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Study Heatmap -->
          <div class="analytics-card">
            <h3>Mapa de Calor - Últimos 30 dias</h3>
            <div class="heatmap">
              <div v-for="week in heatmapWeeks" :key="week.id" class="heatmap-week">
                <div v-for="day in week.days" :key="day.date" 
                     class="heatmap-day" 
                     :class="getHeatmapClass(day.count)"
                     :title="`${day.date}: ${day.count} atividades`">
                </div>
              </div>
            </div>
            <div class="heatmap-legend">
              <span>Menos</span>
              <div class="legend-boxes">
                <div class="legend-box" v-for="i in 5" :key="i" :class="'level-' + i"></div>
              </div>
              <span>Mais</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Upcoming Revisions Calendar -->
      <section class="dashboard-section calendar-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-calendar-alt"></i>
            Próximas Revisões
          </h2>
          <div class="calendar-nav">
            <button @click="previousWeek" class="nav-btn">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="current-week">{{ currentWeekLabel }}</span>
            <button @click="nextWeek" class="nav-btn">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>

        <div class="ultra-calendar">
          <div class="calendar-header">
            <div v-for="day in weekDays" :key="day" class="calendar-day-header">
              {{ day }}
            </div>
          </div>
          <div class="calendar-body">
            <div v-for="day in calendarDays" :key="day.date" 
                 class="calendar-day" 
                 :class="{ today: isToday(day.date), weekend: isWeekend(day.date) }">
              <div class="day-number">{{ day.day }}</div>
              <div class="day-revisions">
                <div v-for="rev in day.revisions" :key="rev.id" 
                     class="day-revision"
                     :class="rev.priority">
                  <span class="rev-time">{{ rev.time }}</span>
                  <span class="rev-title">{{ rev.title }}</span>
                </div>
              </div>
              <div v-if="day.revisions.length > 3" class="more-indicator">
                +{{ day.revisions.length - 3 }} mais
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Ultra Modal -->
    <teleport to="body">
      <transition name="modal-fade">
        <div v-if="showModal" class="ultra-modal-backdrop" @click.self="closeModal">
          <div class="ultra-modal-container">
            <NewRevisionModalUltra
              @close="closeModal"
              @save="handleSaveRevision"
              @saveStudy="handleSaveStudy"
            />
          </div>
        </div>
      </transition>
    </teleport>

    <!-- Notification Toast -->
    <transition name="toast">
      <div v-if="notification.show" class="notification-toast" :class="notification.type">
        <i :class="notification.icon"></i>
        <span>{{ notification.message }}</span>
      </div>
    </transition>

    <!-- Question Block System Modal -->
    <teleport to="body">
      <transition name="modal-fade">
        <div v-if="showQuestionBlock" class="ultra-modal-backdrop" @click.self="closeQuestionBlock">
          <div class="ultra-modal-container">
            <QuestionBlockSystem
              :subject="currentRevisionForQuestions?.subject"
              :topic="currentRevisionForQuestions?.title"
              :revisionId="currentRevisionForQuestions?.id"
              @close="closeQuestionBlock"
              @complete="handleQuestionBlockComplete"
            />
          </div>
        </div>
      </transition>
    </teleport>

    <!-- Debug Panel -->
    <div class="debug-panel-ultra">
      <h4>Debug Ultra</h4>
      <p>Modal State: {{ showModal }}</p>
      <p>Studies: {{ theoryStudies.length }}</p>
      <p>Revisions: {{ spacedRevisions.length }}</p>
      <button @click="showModal = !showModal" class="debug-btn">
        Toggle Modal Directly
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';
import NewRevisionModalUltra from './NewRevisionModalUltra.vue';
import QuestionBlockSystem from './QuestionBlockSystem.vue';

export default {
  name: 'SpacedRevisionSystemUltra',
  components: {
    NewRevisionModalUltra,
    QuestionBlockSystem
  },
  setup() {
    const store = useStore();
    
    // State
    const showModal = ref(false);
    const showStats = ref(false);
    const showQuestionBlock = ref(false);
    const currentRevisionForQuestions = ref(null);
    const currentWeek = ref(new Date());
    const notification = ref({
      show: false,
      type: 'success',
      message: '',
      icon: 'fas fa-check-circle'
    });
    
    // Data from store
    const theoryStudies = computed(() => store.getters['revisions/getTheoryStudies']);
    const spacedRevisions = computed(() => store.getters['revisions/getSpacedRevisions']);
    const performances = computed(() => store.getters['revisions/getPerformances']);
    
    // Chart instance
    let chartInstance = null;
    
    // Quick Stats
    const quickStats = computed(() => [
      {
        icon: 'fas fa-fire',
        label: 'Sequência',
        value: getStudyStreak(),
        color: 'linear-gradient(135deg, #f59e0b, #ef4444)'
      },
      {
        icon: 'fas fa-brain',
        label: 'Estudos',
        value: theoryStudies.value.length,
        color: 'linear-gradient(135deg, #6366f1, #8b5cf6)'
      },
      {
        icon: 'fas fa-check-circle',
        label: 'Concluídas',
        value: getCompletedCount(),
        color: 'linear-gradient(135deg, #10b981, #059669)'
      },
      {
        icon: 'fas fa-percentage',
        label: 'Taxa Média',
        value: getAveragePerformance() + '%',
        color: 'linear-gradient(135deg, #3b82f6, #06b6d4)'
      }
    ]);
    
    // Today's revisions
    const todayRevisions = computed(() => {
      const today = new Date().toDateString();
      return spacedRevisions.value
        .filter(r => new Date(r.date).toDateString() === today && !r.completed)
        .map(r => ({
          ...r,
          progress: r.progress || 0
        }));
    });
    
    const todayPending = computed(() => todayRevisions.value.length);
    
    // Timeline
    const timeline = computed(() => {
      const days = [];
      const allItems = [
        ...theoryStudies.value.map(s => ({
          ...s,
          type: 'study',
          time: formatTime(s.date)
        })),
        ...spacedRevisions.value.map(r => ({
          ...r,
          type: r.completed ? 'completed' : 'revision',
          time: r.startTime || formatTime(r.date)
        })),
        ...performances.value.map(p => ({
          ...p,
          type: 'performance',
          time: formatTime(p.date),
          performance: p.percentage
        }))
      ].sort((a, b) => new Date(b.date) - new Date(a.date));
      
      // Group by day
      const grouped = {};
      allItems.forEach(item => {
        const dateKey = new Date(item.date).toDateString();
        if (!grouped[dateKey]) {
          grouped[dateKey] = {
            date: new Date(item.date),
            items: []
          };
        }
        grouped[dateKey].items.push(item);
      });
      
      return Object.values(grouped).slice(0, 7);
    });
    
    // Subject stats
    const subjectStats = computed(() => {
      const stats = {};
      
      performances.value.forEach(p => {
        if (!stats[p.subject]) {
          stats[p.subject] = {
            name: p.subject,
            total: 0,
            count: 0,
            revisions: 0,
            lastDate: null
          };
        }
        stats[p.subject].total += p.percentage;
        stats[p.subject].count++;
        stats[p.subject].revisions++;
        stats[p.subject].lastDate = p.date;
      });
      
      return Object.values(stats).map(s => ({
        ...s,
        average: Math.round(s.total / s.count),
        streak: calculateStreak(s.lastDate)
      }));
    });
    
    // Calendar
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    
    const currentWeekLabel = computed(() => {
      const start = new Date(currentWeek.value);
      start.setDate(start.getDate() - start.getDay());
      const end = new Date(start);
      end.setDate(end.getDate() + 6);
      
      return `${start.getDate()}/${start.getMonth() + 1} - ${end.getDate()}/${end.getMonth() + 1}`;
    });
    
    const calendarDays = computed(() => {
      const days = [];
      const start = new Date(currentWeek.value);
      start.setDate(start.getDate() - start.getDay());
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(start);
        date.setDate(start.getDate() + i);
        
        const dayRevisions = spacedRevisions.value
          .filter(r => new Date(r.date).toDateString() === date.toDateString())
          .map(r => ({
            id: r.id,
            time: r.startTime || '09:00',
            title: r.title,
            priority: r.priority?.toLowerCase() || 'medium'
          }))
          .sort((a, b) => a.time.localeCompare(b.time));
        
        days.push({
          date: date,
          day: date.getDate(),
          revisions: dayRevisions
        });
      }
      
      return days;
    });
    
    // Heatmap
    const heatmapWeeks = computed(() => {
      const weeks = [];
      const today = new Date();
      
      for (let w = 4; w >= 0; w--) {
        const week = { id: w, days: [] };
        
        for (let d = 0; d < 7; d++) {
          const date = new Date(today);
          date.setDate(today.getDate() - (w * 7) - (6 - d));
          
          const count = countActivitiesOnDate(date);
          week.days.push({
            date: date.toLocaleDateString(),
            count
          });
        }
        
        weeks.push(week);
      }
      
      return weeks;
    });
    
    // Methods
    const openRevisionModal = () => {
      console.log('🚀 Abrindo modal Ultra!');
      showModal.value = true;
      console.log('Modal state:', showModal.value);
    };
    
    const closeModal = () => {
      showModal.value = false;
    };
    
    const handleSaveStudy = (studyData) => {
      const daysToAdd = studyData.difficulty === 'Fácil' ? 2 : 1;
      const firstContactDate = new Date(studyData.date);
      firstContactDate.setDate(firstContactDate.getDate() + daysToAdd);
      
      const newStudy = {
        ...studyData,
        id: Date.now(),
        date: new Date(studyData.date),
        firstContactDate
      };
      
      // Save to store
      store.dispatch('revisions/addTheoryStudy', newStudy);
      
      const firstContact = {
        id: Date.now() + 1,
        title: `Questões - ${studyData.title}`,
        subject: studyData.subject,
        date: firstContactDate,
        revisionType: 'Prática',
        priority: 'Alta',
        startTime: '14:00',
        studyId: newStudy.id
      };
      
      // Save to store
      store.dispatch('revisions/addFirstContact', firstContact);
      
      closeModal();
      
      showNotification(
        'success',
        `Estudo registrado! Primeiro contato em ${firstContactDate.toLocaleDateString('pt-BR')}`,
        'fas fa-check-circle'
      );
    };
    
    const handleSaveRevision = (revisionData) => {
      const newRevision = {
        ...revisionData,
        id: Date.now(),
        date: new Date(`${revisionData.date} ${revisionData.time || '09:00'}`),
        completed: false,
        progress: 0
      };
      
      // Save to store
      store.dispatch('revisions/addSpacedRevision', newRevision);
      
      closeModal();
      
      showNotification('success', 'Revisão agendada com sucesso!', 'fas fa-calendar-check');
    };
    
    const startRevision = (revision) => {
      showNotification('info', 'Iniciando revisão...', 'fas fa-play');
      
      // Se for uma revisão de questões, abrir o sistema de blocos
      if (revision.revisionType === 'Prática' || revision.revisionType === 'Questões') {
        showQuestionBlock.value = true;
        currentRevisionForQuestions.value = revision;
      } else {
        // Para outros tipos de revisão
        showNotification('warning', 'Sistema de revisão em desenvolvimento', 'fas fa-tools');
      }
    };
    
    const postponeRevision = (revision) => {
      const index = spacedRevisions.value.findIndex(r => r.id === revision.id);
      if (index !== -1) {
        const newDate = new Date(revision.date);
        newDate.setDate(newDate.getDate() + 1);
        spacedRevisions.value[index].date = newDate;
        saveToLocalStorage();
        showNotification('warning', 'Revisão adiada para amanhã', 'fas fa-clock');
      }
    };
    
    const startFocusMode = () => {
      showNotification('info', 'Modo foco ativado!', 'fas fa-rocket');
      // TODO: Implement focus mode
    };
    
    const previousWeek = () => {
      const newDate = new Date(currentWeek.value);
      newDate.setDate(newDate.getDate() - 7);
      currentWeek.value = newDate;
    };
    
    const nextWeek = () => {
      const newDate = new Date(currentWeek.value);
      newDate.setDate(newDate.getDate() + 7);
      currentWeek.value = newDate;
    };
    
    // Helper functions
    const getStudyStreak = () => {
      const studies = store.getters['revisions/getTheoryStudies'];
      if (studies.length === 0) return 0;
      
      // Simple streak calculation
      let streak = 0;
      const today = new Date();
      const dates = studies.map(s => new Date(s.date).toDateString());
      
      for (let i = 0; i < 30; i++) {
        const checkDate = new Date(today);
        checkDate.setDate(checkDate.getDate() - i);
        if (dates.includes(checkDate.toDateString())) {
          streak++;
        } else if (i > 0) {
          break;
        }
      }
      
      return streak;
    };
    
    const getCompletedCount = () => {
      const revisions = store.getters['revisions/getSpacedRevisions'];
      return revisions.filter(r => r.completed).length;
    };
    
    const getAveragePerformance = () => {
      return store.getters['revisions/getAveragePerformance'];
    };
    
    const formatTime = (date) => {
      return new Date(date).toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    };
    
    const formatTimelineDate = (date) => {
      const d = new Date(date);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (d.toDateString() === today.toDateString()) return 'Hoje';
      if (d.toDateString() === yesterday.toDateString()) return 'Ontem';
      
      return d.toLocaleDateString('pt-BR', { 
        weekday: 'short', 
        day: 'numeric', 
        month: 'short' 
      });
    };
    
    const isToday = (date) => {
      return date.toDateString() === new Date().toDateString();
    };
    
    const isWeekend = (date) => {
      const day = date.getDay();
      return day === 0 || day === 6;
    };
    
    const getRevisionCardClass = (revision) => {
      return {
        'priority-high': revision.priority === 'Alta',
        'priority-medium': revision.priority === 'Média',
        'priority-low': revision.priority === 'Baixa'
      };
    };
    
    const getPriorityIcon = (priority) => {
      const icons = {
        'Alta': 'fas fa-fire',
        'Média': 'fas fa-flag',
        'Baixa': 'fas fa-leaf'
      };
      return icons[priority] || 'fas fa-flag';
    };
    
    const getTimelineIcon = (type) => {
      const icons = {
        'study': 'fas fa-book',
        'revision': 'fas fa-redo',
        'completed': 'fas fa-check-circle',
        'performance': 'fas fa-chart-line'
      };
      return icons[type] || 'fas fa-circle';
    };
    
    const getPerformanceClass = (percentage) => {
      if (percentage >= 80) return 'excellent';
      if (percentage >= 60) return 'good';
      if (percentage >= 40) return 'average';
      return 'poor';
    };
    
    const getScoreColor = (score) => {
      if (score >= 80) return '#10b981';
      if (score >= 60) return '#3b82f6';
      if (score >= 40) return '#f59e0b';
      return '#ef4444';
    };
    
    const getHeatmapClass = (count) => {
      if (count === 0) return 'level-0';
      if (count <= 2) return 'level-1';
      if (count <= 4) return 'level-2';
      if (count <= 6) return 'level-3';
      if (count <= 8) return 'level-4';
      return 'level-5';
    };
    
    const calculateStreak = (lastDate) => {
      if (!lastDate) return 0;
      const today = new Date();
      const last = new Date(lastDate);
      const diffDays = Math.floor((today - last) / (1000 * 60 * 60 * 24));
      return diffDays <= 1 ? 7 : 0; // Simplified
    };
    
    const countActivitiesOnDate = (date) => {
      const dateStr = date.toDateString();
      let count = 0;
      
      theoryStudies.value.forEach(s => {
        if (new Date(s.date).toDateString() === dateStr) count++;
      });
      
      spacedRevisions.value.forEach(r => {
        if (new Date(r.date).toDateString() === dateStr) count++;
      });
      
      return count;
    };
    
    const showNotification = (type, message, icon) => {
      notification.value = {
        show: true,
        type,
        message,
        icon
      };
      
      setTimeout(() => {
        notification.value.show = false;
      }, 3000);
    };
    
    const closeQuestionBlock = () => {
      showQuestionBlock.value = false;
      currentRevisionForQuestions.value = null;
    };
    
    const handleQuestionBlockComplete = (result) => {
      console.log('📊 Question Block completed:', result);
      
      // Atualizar a revisão como concluída
      const revision = currentRevisionForQuestions.value;
      if (revision) {
        // Marcar como concluída no store
        store.dispatch('revisions/completeRevision', {
          id: revision.id,
          performance: result.percentage
        });
        
        // Salvar o desempenho
        store.dispatch('revisions/addPerformance', {
          id: Date.now(),
          subject: revision.subject,
          topic: revision.title,
          date: new Date(),
          percentage: result.percentage,
          totalQuestions: result.totalQuestions,
          correctAnswers: result.correctAnswers,
          timeSpent: result.totalTime,
          revisionId: revision.id
        });
        
        // Mostrar notificação de sucesso
        if (result.percentage >= 80) {
          showNotification('success', 
            `Excelente! ${result.percentage}% de acerto. Próxima revisão em ${result.nextRevisionDays} dias.`, 
            'fas fa-trophy'
          );
        } else if (result.percentage >= 60) {
          showNotification('info', 
            `Bom trabalho! ${result.percentage}% de acerto. Continue praticando!`, 
            'fas fa-thumbs-up'
          );
        } else {
          showNotification('warning', 
            `${result.percentage}% de acerto. Recomenda-se revisar o conteúdo teórico.`, 
            'fas fa-exclamation-triangle'
          );
        }
      }
      
      closeQuestionBlock();
    };
    
    // Load data from store
    
    // Chart
    const initChart = () => {
      const ctx = document.querySelector('.chart-container canvas')?.getContext('2d');
      if (!ctx) return;
      
      if (chartInstance) {
        chartInstance.destroy();
      }
      
      const last30Days = performances.value.slice(-30);
      
      chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: last30Days.map(p => new Date(p.date).toLocaleDateString('pt-BR', { 
            day: 'numeric', 
            month: 'short' 
          })),
          datasets: [
            {
              label: 'Taxa de Retenção',
              data: last30Days.map(p => p.percentage),
              borderColor: '#6366f1',
              backgroundColor: 'rgba(99, 102, 241, 0.1)',
              tension: 0.4,
              fill: true
            },
            {
              label: 'Meta',
              data: last30Days.map(() => 85),
              borderColor: '#10b981',
              borderDash: [5, 5],
              pointRadius: 0
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              ticks: {
                callback: (value) => value + '%'
              }
            }
          }
        }
      });
    };
    
    // Lifecycle
    onMounted(() => {
      // Load data from store
      store.dispatch('revisions/loadRevisions');
      if (showStats.value) {
        setTimeout(initChart, 100);
      }
    });
    
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.destroy();
      }
    });
    
    return {
      // State
      showModal,
      showStats,
      showQuestionBlock,
      currentRevisionForQuestions,
      currentWeek,
      notification,
      
      // Data
      theoryStudies,
      spacedRevisions,
      performances,
      
      // Computed
      quickStats,
      todayRevisions,
      todayPending,
      timeline,
      subjectStats,
      weekDays,
      currentWeekLabel,
      calendarDays,
      heatmapWeeks,
      
      // Methods
      openRevisionModal,
      closeModal,
      handleSaveStudy,
      handleSaveRevision,
      startRevision,
      postponeRevision,
      startFocusMode,
      previousWeek,
      nextWeek,
      closeQuestionBlock,
      handleQuestionBlockComplete,
      
      // Helpers
      formatTimelineDate,
      isToday,
      isWeekend,
      getRevisionCardClass,
      getPriorityIcon,
      getTimelineIcon,
      getPerformanceClass,
      getScoreColor,
      getHeatmapClass
    };
  }
};
</script>

<style scoped>
/* ULTRA Design System */
:root {
  --ultra-bg: #0a0e1a;
  --ultra-surface: #141925;
  --ultra-surface-light: #1e2433;
  --ultra-border: rgba(148, 163, 184, 0.1);
  --ultra-text: #e4e6eb;
  --ultra-text-secondary: #94a3b8;
  --ultra-primary: #6366f1;
  --ultra-primary-light: #818cf8;
  --ultra-success: #10b981;
  --ultra-warning: #f59e0b;
  --ultra-danger: #ef4444;
  --ultra-gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Base Layout */
.ultra-revision-system {
  min-height: 100vh;
  background: var(--ultra-bg);
  color: var(--ultra-text);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(120px);
  opacity: 0.15;
  animation: float 30s infinite;
}

.shape-1 {
  width: 800px;
  height: 800px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  top: -300px;
  left: -300px;
  animation-duration: 25s;
}

.shape-2 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  bottom: -200px;
  right: -200px;
  animation-duration: 35s;
  animation-delay: -10s;
}

.shape-3 {
  width: 700px;
  height: 700px;
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-duration: 40s;
  animation-delay: -20s;
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 60px 60px;
}

@keyframes float {
  0%, 100% { 
    transform: translate(0, 0) scale(1) rotate(0deg); 
  }
  25% { 
    transform: translate(50px, -50px) scale(1.1) rotate(90deg); 
  }
  50% { 
    transform: translate(-30px, 30px) scale(0.9) rotate(180deg); 
  }
  75% { 
    transform: translate(40px, 40px) scale(1.05) rotate(270deg); 
  }
}

/* Ultra Header */
.ultra-header {
  position: relative;
  z-index: 10;
  background: rgba(20, 25, 37, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ultra-border);
}

.header-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.brand-icon {
  position: relative;
  width: 72px;
  height: 72px;
  background: var(--ultra-gradient);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
}

.pulse-ring {
  position: absolute;
  inset: -4px;
  border: 2px solid rgba(99, 102, 241, 0.4);
  border-radius: 28px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #fff, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge-new {
  font-size: 0.6rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #ef4444, #f59e0b);
  color: white;
  border-radius: 6px;
  font-weight: 700;
  animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 10px rgba(245, 158, 11, 0.5); }
  50% { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8); }
}

.brand-subtitle {
  margin: 0.5rem 0 0;
  color: var(--ultra-text-secondary);
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Ultra Buttons */
.ultra-btn {
  position: relative;
  padding: 1rem 2rem;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.ultra-btn.primary {
  background: var(--ultra-gradient);
  color: white;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

.ultra-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
}

.ultra-btn.primary.animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.ultra-btn.primary.animated:hover::before {
  left: 100%;
}

.ultra-btn.secondary {
  background: rgba(148, 163, 184, 0.1);
  color: var(--ultra-text);
  border: 1px solid var(--ultra-border);
}

.ultra-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  border-color: rgba(148, 163, 184, 0.3);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
}

.btn-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--ultra-danger);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 24px;
  text-align: center;
}

/* Quick Stats Bar */
.quick-stats-bar {
  padding: 1.5rem 2rem;
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  max-width: 1600px;
  margin: 0 auto;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--ultra-text);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

/* Dashboard */
.ultra-dashboard {
  position: relative;
  z-index: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-section {
  background: rgba(20, 25, 37, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid var(--ultra-border);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--ultra-success);
  font-weight: 400;
}

.live-indicator .dot {
  width: 8px;
  height: 8px;
  background: var(--ultra-success);
  border-radius: 50%;
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.section-actions {
  display: flex;
  gap: 1rem;
}

.mini-btn {
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  color: var(--ultra-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.mini-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

/* Focus Section */
.empty-state.elegant {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(30, 36, 51, 0.3);
  border-radius: 16px;
  border: 1px solid var(--ultra-border);
}

.empty-icon {
  font-size: 4rem;
  color: var(--ultra-text-secondary);
  opacity: 0.5;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: var(--ultra-text);
}

.empty-state p {
  color: var(--ultra-text-secondary);
  margin-bottom: 2rem;
}

.ultra-btn.small {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

/* Revision Cards */
.revision-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 1.5rem;
}

.revision-card {
  background: rgba(30, 36, 51, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.revision-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--ultra-gradient);
  opacity: 0;
  transition: opacity 0.3s;
}

.revision-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.2);
}

.revision-card:hover::before {
  opacity: 1;
}

.revision-card.priority-high {
  border-color: rgba(239, 68, 68, 0.3);
}

.revision-card.priority-high::before {
  background: linear-gradient(135deg, #ef4444, #f59e0b);
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-subject {
  background: rgba(99, 102, 241, 0.1);
  color: var(--ultra-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.card-priority {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-priority.alta {
  background: rgba(239, 68, 68, 0.1);
  color: var(--ultra-danger);
}

.card-priority.média {
  background: rgba(245, 158, 11, 0.1);
  color: var(--ultra-warning);
}

.card-priority.baixa {
  background: rgba(16, 185, 129, 0.1);
  color: var(--ultra-success);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ultra-text);
}

.card-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--ultra-text-secondary);
  font-size: 0.875rem;
}

.card-progress {
  margin-bottom: 1.5rem;
}

.progress-bar {
  height: 6px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--ultra-gradient);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
}

.card-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.action-btn.primary {
  background: var(--ultra-gradient);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.action-btn.secondary {
  background: rgba(148, 163, 184, 0.1);
  color: var(--ultra-text);
  border: 1px solid var(--ultra-border);
}

.action-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

/* Timeline */
.study-timeline {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 1rem;
}

.timeline-day {
  margin-bottom: 2rem;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.day-date {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ultra-text);
}

.day-count {
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

.timeline-items {
  position: relative;
  padding-left: 2rem;
}

.timeline-item {
  position: relative;
  padding: 1rem;
  background: rgba(30, 36, 51, 0.5);
  border-radius: 12px;
  margin-bottom: 1rem;
  border: 1px solid var(--ultra-border);
  transition: all 0.2s;
}

.timeline-item:hover {
  background: rgba(30, 36, 51, 0.7);
  transform: translateX(4px);
}

.item-connector {
  position: absolute;
  left: -1.5rem;
  top: 0;
  bottom: -1rem;
  width: 2px;
  background: var(--ultra-border);
}

.timeline-item:last-child .item-connector {
  display: none;
}

.item-dot {
  position: absolute;
  left: -2rem;
  top: 1.5rem;
  width: 32px;
  height: 32px;
  background: var(--ultra-surface);
  border: 2px solid var(--ultra-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.timeline-item.study .item-dot {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
  color: var(--ultra-primary);
}

.timeline-item.completed .item-dot {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--ultra-success);
  color: var(--ultra-success);
}

.timeline-item.performance .item-dot {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #3b82f6;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-time {
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
  font-weight: 500;
}

.item-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ultra-text);
}

.item-subject {
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

.performance-badge {
  display: inline-flex;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.performance-badge.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: var(--ultra-success);
}

.performance-badge.good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.performance-badge.average {
  background: rgba(245, 158, 11, 0.1);
  color: var(--ultra-warning);
}

.performance-badge.poor {
  background: rgba(239, 68, 68, 0.1);
  color: var(--ultra-danger);
}

/* Analytics */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.analytics-card {
  background: rgba(30, 36, 51, 0.3);
  border: 1px solid var(--ultra-border);
  border-radius: 16px;
  padding: 1.5rem;
}

.analytics-card.large {
  grid-column: span 2;
}

.analytics-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: var(--ultra-text);
}

.chart-container {
  height: 300px;
  margin-bottom: 1rem;
}

.chart-legend {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Subject Stats */
.subject-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.subject-stat {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subject-name {
  font-weight: 500;
  color: var(--ultra-text);
}

.subject-score {
  font-weight: 700;
  font-size: 1.125rem;
}

.subject-bar {
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.subject-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
}

/* Heatmap */
.heatmap {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.heatmap-week {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.heatmap-day {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.heatmap-day:hover {
  transform: scale(1.2);
}

.heatmap-day.level-0 {
  background: rgba(148, 163, 184, 0.1);
}

.heatmap-day.level-1 {
  background: rgba(99, 102, 241, 0.2);
}

.heatmap-day.level-2 {
  background: rgba(99, 102, 241, 0.4);
}

.heatmap-day.level-3 {
  background: rgba(99, 102, 241, 0.6);
}

.heatmap-day.level-4 {
  background: rgba(99, 102, 241, 0.8);
}

.heatmap-day.level-5 {
  background: var(--ultra-primary);
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
}

.legend-boxes {
  display: flex;
  gap: 0.25rem;
}

.legend-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Calendar */
.calendar-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--ultra-border);
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  color: var(--ultra-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.nav-btn:hover {
  background: rgba(148, 163, 184, 0.2);
  transform: scale(1.1);
}

.current-week {
  font-weight: 500;
  color: var(--ultra-text);
}

.ultra-calendar {
  background: rgba(30, 36, 51, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid var(--ultra-border);
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.calendar-day-header {
  text-align: center;
  font-weight: 600;
  color: var(--ultra-text-secondary);
  font-size: 0.875rem;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  background: rgba(20, 25, 37, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  padding: 0.75rem;
  min-height: 120px;
  transition: all 0.2s;
}

.calendar-day:hover {
  background: rgba(30, 36, 51, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
}

.calendar-day.weekend {
  background: rgba(148, 163, 184, 0.05);
}

.day-number {
  font-weight: 600;
  color: var(--ultra-text);
  margin-bottom: 0.5rem;
}

.day-revisions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.day-revision {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 4px;
  color: var(--ultra-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.day-revision.alta {
  background: rgba(239, 68, 68, 0.2);
  color: #ff6b6b;
}

.day-revision.média {
  background: rgba(245, 158, 11, 0.2);
  color: #ffa94d;
}

.more-indicator {
  font-size: 0.7rem;
  color: var(--ultra-text-secondary);
  margin-top: 0.25rem;
  text-align: center;
}

/* Modal */
.ultra-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999999;
  padding: 2rem;
}

.ultra-modal-container {
  position: relative;
  z-index: 10000000;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: auto;
}

/* Notification Toast */
.notification-toast {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  padding: 1rem 1.5rem;
  background: var(--ultra-surface);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  z-index: 10000;
}

.notification-toast.success {
  border-color: var(--ultra-success);
  color: var(--ultra-success);
}

.notification-toast.warning {
  border-color: var(--ultra-warning);
  color: var(--ultra-warning);
}

.notification-toast.info {
  border-color: var(--ultra-primary);
  color: var(--ultra-primary);
}

/* Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.toast-enter-active {
  animation: slideInRight 0.3s ease;
}

.toast-leave-active {
  animation: slideOutRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .analytics-card.large {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    flex-direction: column;
  }
  
  .ultra-btn {
    width: 100%;
    justify-content: center;
  }
  
  .quick-stats-bar {
    flex-wrap: wrap;
  }
  
  .revision-cards {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .calendar-body {
    grid-template-columns: repeat(7, 1fr);
    font-size: 0.8rem;
  }
  
  .calendar-day {
    min-height: 80px;
    padding: 0.5rem;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Debug Panel */
.debug-panel-ultra {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid #6366f1;
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 0.875rem;
  z-index: 999999;
  font-family: monospace;
}

.debug-panel-ultra h4 {
  margin: 0 0 0.5rem;
  color: #6366f1;
  font-size: 1rem;
}

.debug-panel-ultra p {
  margin: 0.25rem 0;
}

.debug-btn {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: #6366f1;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
}

.debug-btn:hover {
  background: #818cf8;
}
</style>