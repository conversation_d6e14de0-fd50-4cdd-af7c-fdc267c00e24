<template>
  <div class="revision-tool-overlay" @click.self="$emit('close')">
    <div class="revision-tool-window">
      <!-- Header -->
      <div class="tool-header">
        <h2>🧠 Sistema de Revisões Espaçadas</h2>
        <button class="close-btn" @click="$emit('close')">✕</button>
      </div>

      <!-- Tabs -->
      <div class="tool-tabs">
        <button 
          class="tab-btn" 
          :class="{ active: activeTab === 'teorico' }"
          @click="activeTab = 'teorico'"
        >
          📚 Estudo Teórico
        </button>
        <button 
          class="tab-btn" 
          :class="{ active: activeTab === 'pratico' }"
          @click="activeTab = 'pratico'"
        >
          ✍️ Revisão Prática
        </button>
        <button 
          class="tab-btn" 
          :class="{ active: activeTab === 'historico' }"
          @click="activeTab = 'historico'"
        >
          📊 Histórico
        </button>
      </div>

      <!-- Content -->
      <div class="tool-content">
        <!-- Tab: Estudo Teórico -->
        <div v-if="activeTab === 'teorico'" class="tab-content">
          <form @submit.prevent="registrarEstudoTeorico" class="teorico-form">
            <h3>Registrar Novo Estudo Teórico</h3>
            
            <div class="form-group">
              <label>Título do Conteúdo *</label>
              <input 
                v-model="estudoTeorico.titulo" 
                type="text" 
                placeholder="Ex: Sistema Cardiovascular - Anatomia"
                required
              >
            </div>

            <div class="form-group">
              <label>Matéria *</label>
              <select v-model="estudoTeorico.materia" required>
                <option value="">Selecione...</option>
                <option value="Anatomia">Anatomia</option>
                <option value="Fisiologia">Fisiologia</option>
                <option value="Patologia">Patologia</option>
                <option value="Farmacologia">Farmacologia</option>
                <option value="Bioquímica">Bioquímica</option>
                <option value="Microbiologia">Microbiologia</option>
                <option value="Imunologia">Imunologia</option>
                <option value="Outras">Outras</option>
              </select>
            </div>

            <div class="form-group">
              <label>Data do Estudo *</label>
              <input 
                v-model="estudoTeorico.data" 
                type="date" 
                :max="hoje"
                required
              >
            </div>

            <div class="form-group">
              <label>Tempo de Estudo (minutos) *</label>
              <input 
                v-model.number="estudoTeorico.tempo" 
                type="number" 
                min="10" 
                placeholder="60"
                required
              >
            </div>

            <div class="form-group">
              <label>Avaliação de Dificuldade *</label>
              <div class="difficulty-selector">
                <label class="difficulty-option">
                  <input 
                    type="radio" 
                    value="Fácil" 
                    v-model="estudoTeorico.dificuldade"
                    required
                  >
                  <span class="difficulty-card facil">
                    😊 Fácil
                    <small>Primeiro contato em 2 dias</small>
                  </span>
                </label>
                <label class="difficulty-option">
                  <input 
                    type="radio" 
                    value="Difícil" 
                    v-model="estudoTeorico.dificuldade"
                    required
                  >
                  <span class="difficulty-card dificil">
                    😰 Difícil
                    <small>Primeiro contato em 1 dia</small>
                  </span>
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>Notas / Observações</label>
              <textarea 
                v-model="estudoTeorico.notas" 
                rows="3" 
                placeholder="Anotações sobre o conteúdo estudado..."
              ></textarea>
            </div>

            <div class="preview-section" v-if="estudoTeorico.dificuldade">
              <h4>📅 Agendamento Automático</h4>
              <div class="preview-card">
                <p><strong>Primeiro Contato com Questões:</strong></p>
                <p class="date-preview">{{ dataPrimeiroContato }}</p>
                <p class="info-text">
                  Baseado na dificuldade <strong>{{ estudoTeorico.dificuldade }}</strong>, 
                  seu primeiro contato com questões será agendado para 
                  <strong>{{ estudoTeorico.dificuldade === 'Fácil' ? '2 dias' : '1 dia' }}</strong> 
                  após o estudo teórico.
                </p>
              </div>
            </div>

            <button type="submit" class="submit-btn">
              📌 Registrar Estudo e Agendar Revisão
            </button>
          </form>
        </div>

        <!-- Tab: Revisão Prática -->
        <div v-if="activeTab === 'pratico'" class="tab-content">
          <form @submit.prevent="registrarRevisaoPratica" class="pratico-form">
            <h3>Registrar Desempenho em Questões</h3>

            <div class="form-group">
              <label>Conteúdo Revisado *</label>
              <select v-model="revisaoPratica.conteudoId" required>
                <option value="">Selecione o conteúdo...</option>
                <option 
                  v-for="estudo in estudosParaRevisao" 
                  :key="estudo.id" 
                  :value="estudo.id"
                >
                  {{ estudo.titulo }} - {{ formatDate(estudo.dataPrimeiroContato) }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>Total de Questões *</label>
              <input 
                v-model.number="revisaoPratica.totalQuestoes" 
                type="number" 
                min="20" 
                max="50" 
                placeholder="30"
                required
              >
              <small>Recomendado: 20-30 questões (ideal: 30)</small>
            </div>

            <div class="form-group">
              <label>Questões Corretas *</label>
              <input 
                v-model.number="revisaoPratica.acertos" 
                type="number" 
                :min="0" 
                :max="revisaoPratica.totalQuestoes || 30"
                required
              >
            </div>

            <div class="performance-display" v-if="percentualCalculado !== null">
              <div class="performance-meter">
                <div 
                  class="performance-fill" 
                  :style="{ width: percentualCalculado + '%' }"
                  :class="performanceClass"
                ></div>
                <span class="performance-text">{{ percentualCalculado }}%</span>
              </div>
            </div>

            <div class="preview-section" v-if="proximaRevisaoCalculada">
              <h4>📊 Análise de Desempenho</h4>
              <div class="preview-card">
                <p><strong>Próxima Revisão:</strong></p>
                <p class="date-preview">{{ proximaRevisaoCalculada.data }}</p>
                <p class="info-text">
                  Com <strong>{{ percentualCalculado }}%</strong> de acerto, 
                  sua próxima revisão será em <strong>{{ proximaRevisaoCalculada.dias }} dias</strong>.
                </p>
                <div class="recommendation" :class="performanceClass">
                  <strong>Recomendação:</strong> {{ getRecomendacao() }}
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn" :disabled="!proximaRevisaoCalculada">
              ✅ Registrar Desempenho e Agendar Próxima Revisão
            </button>
          </form>
        </div>

        <!-- Tab: Histórico -->
        <div v-if="activeTab === 'historico'" class="tab-content">
          <div class="historico-section">
            <h3>📈 Histórico de Estudos e Revisões</h3>
            
            <div class="stats-cards">
              <div class="stat-card">
                <h4>Total de Estudos</h4>
                <p class="stat-number">{{ totalEstudos }}</p>
              </div>
              <div class="stat-card">
                <h4>Revisões Realizadas</h4>
                <p class="stat-number">{{ totalRevisoes }}</p>
              </div>
              <div class="stat-card">
                <h4>Taxa Média de Acerto</h4>
                <p class="stat-number">{{ taxaMediaAcerto }}%</p>
              </div>
            </div>

            <div class="timeline">
              <div 
                v-for="item in historicoCompleto" 
                :key="item.id" 
                class="timeline-item"
                :class="item.tipo"
              >
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                  <h5>{{ item.titulo }}</h5>
                  <p class="timeline-date">{{ formatDate(item.data) }}</p>
                  <div v-if="item.tipo === 'teorico'" class="timeline-details">
                    <span class="badge">{{ item.dificuldade }}</span>
                    <span class="badge">{{ item.tempo }} min</span>
                  </div>
                  <div v-else class="timeline-details">
                    <span class="badge" :class="getPerformanceClass(item.percentual)">
                      {{ item.acertos }}/{{ item.totalQuestoes }} ({{ item.percentual }}%)
                    </span>
                    <span class="badge">Próxima em {{ item.proximaRevisaoDias }} dias</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const emit = defineEmits(['close', 'estudo-registrado', 'revisao-registrada'])

// Estado
const activeTab = ref('teorico')
const hoje = new Date().toISOString().split('T')[0]

// Formulário Estudo Teórico
const estudoTeorico = ref({
  titulo: '',
  materia: '',
  data: hoje,
  tempo: 60,
  dificuldade: '',
  notas: ''
})

// Formulário Revisão Prática
const revisaoPratica = ref({
  conteudoId: '',
  totalQuestoes: 30,
  acertos: 0
})

// Dados armazenados
const estudos = ref([])
const revisoes = ref([])

// Computed
const dataPrimeiroContato = computed(() => {
  if (!estudoTeorico.value.data || !estudoTeorico.value.dificuldade) return ''
  
  const data = new Date(estudoTeorico.value.data)
  const diasParaAdicionar = estudoTeorico.value.dificuldade === 'Fácil' ? 2 : 1
  data.setDate(data.getDate() + diasParaAdicionar)
  
  return data.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
})

const estudosParaRevisao = computed(() => {
  return estudos.value.filter(estudo => {
    const dataRevisao = new Date(estudo.dataPrimeiroContato)
    return dataRevisao <= new Date()
  })
})

const percentualCalculado = computed(() => {
  if (!revisaoPratica.value.totalQuestoes) return null
  return Math.round((revisaoPratica.value.acertos / revisaoPratica.value.totalQuestoes) * 100)
})

const proximaRevisaoCalculada = computed(() => {
  const percentual = percentualCalculado.value
  if (percentual === null) return null
  
  let dias
  if (percentual <= 50) dias = 2
  else if (percentual <= 55) dias = 7
  else if (percentual <= 60) dias = 14
  else if (percentual <= 65) dias = 18
  else if (percentual <= 75) dias = 24
  else if (percentual <= 80) dias = 30
  else dias = 35
  
  const data = new Date()
  data.setDate(data.getDate() + dias)
  
  return {
    dias,
    data: data.toLocaleDateString('pt-BR', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }
})

const performanceClass = computed(() => {
  const perc = percentualCalculado.value
  if (perc === null) return ''
  if (perc >= 80) return 'excellent'
  if (perc >= 65) return 'good'
  if (perc >= 50) return 'medium'
  return 'low'
})

const totalEstudos = computed(() => estudos.value.length)
const totalRevisoes = computed(() => revisoes.value.length)

const taxaMediaAcerto = computed(() => {
  if (revisoes.value.length === 0) return 0
  const soma = revisoes.value.reduce((acc, rev) => acc + rev.percentual, 0)
  return Math.round(soma / revisoes.value.length)
})

const historicoCompleto = computed(() => {
  const todos = [
    ...estudos.value.map(e => ({ ...e, tipo: 'teorico' })),
    ...revisoes.value.map(r => ({ ...r, tipo: 'pratico' }))
  ]
  return todos.sort((a, b) => new Date(b.data) - new Date(a.data))
})

// Métodos
const registrarEstudoTeorico = () => {
  const diasAdicionar = estudoTeorico.value.dificuldade === 'Fácil' ? 2 : 1
  const dataPrimeiroContato = new Date(estudoTeorico.value.data)
  dataPrimeiroContato.setDate(dataPrimeiroContato.getDate() + diasAdicionar)
  
  const novoEstudo = {
    id: Date.now(),
    ...estudoTeorico.value,
    dataPrimeiroContato: dataPrimeiroContato.toISOString(),
    dataRegistro: new Date().toISOString()
  }
  
  estudos.value.push(novoEstudo)
  localStorage.setItem('estudos_teoricos', JSON.stringify(estudos.value))
  
  emit('estudo-registrado', novoEstudo)
  
  // Reset form
  estudoTeorico.value = {
    titulo: '',
    materia: '',
    data: hoje,
    tempo: 60,
    dificuldade: '',
    notas: ''
  }
  
  alert('✅ Estudo teórico registrado e primeira revisão agendada!')
}

const registrarRevisaoPratica = () => {
  const estudo = estudos.value.find(e => e.id === revisaoPratica.value.conteudoId)
  if (!estudo) return
  
  const novaRevisao = {
    id: Date.now(),
    estudoId: revisaoPratica.value.conteudoId,
    titulo: estudo.titulo,
    materia: estudo.materia,
    data: new Date().toISOString(),
    totalQuestoes: revisaoPratica.value.totalQuestoes,
    acertos: revisaoPratica.value.acertos,
    percentual: percentualCalculado.value,
    proximaRevisaoDias: proximaRevisaoCalculada.value.dias,
    proximaRevisaoData: new Date(Date.now() + proximaRevisaoCalculada.value.dias * 24 * 60 * 60 * 1000).toISOString()
  }
  
  revisoes.value.push(novaRevisao)
  localStorage.setItem('revisoes_praticas', JSON.stringify(revisoes.value))
  
  emit('revisao-registrada', novaRevisao)
  
  // Reset form
  revisaoPratica.value = {
    conteudoId: '',
    totalQuestoes: 30,
    acertos: 0
  }
  
  alert('✅ Desempenho registrado e próxima revisão agendada!')
}

const getRecomendacao = () => {
  const perc = percentualCalculado.value
  if (perc >= 80) return 'Excelente desempenho! Continue assim.'
  if (perc >= 65) return 'Bom desempenho! Revise os pontos que errou.'
  if (perc >= 50) return 'Desempenho regular. Reforce o conteúdo.'
  return 'Necessita mais estudo. Revise o conteúdo teórico.'
}

const getPerformanceClass = (percentual) => {
  if (percentual >= 80) return 'excellent'
  if (percentual >= 65) return 'good'
  if (percentual >= 50) return 'medium'
  return 'low'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('pt-BR')
}

// Lifecycle
onMounted(() => {
  const estudosSalvos = localStorage.getItem('estudos_teoricos')
  const revisoesSalvas = localStorage.getItem('revisoes_praticas')
  
  if (estudosSalvos) estudos.value = JSON.parse(estudosSalvos)
  if (revisoesSalvas) revisoes.value = JSON.parse(revisoesSalvas)
})
</script>

<style scoped>
.revision-tool-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.revision-tool-window {
  background: #0f172a;
  border-radius: 20px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  border: 1px solid #1e293b;
}

.tool-header {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  padding: 24px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #1e293b;
}

.tool-header h2 {
  color: #f1f5f9;
  font-size: 24px;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
}

.tool-tabs {
  display: flex;
  background: #1e293b;
  border-bottom: 1px solid #334155;
}

.tab-btn {
  flex: 1;
  padding: 16px 24px;
  background: none;
  border: none;
  color: #64748b;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #cbd5e1;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.tool-content {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

.tab-content h3 {
  color: #f1f5f9;
  font-size: 20px;
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  color: #cbd5e1;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 10px;
  color: #f1f5f9;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: #0f172a;
}

.form-group small {
  display: block;
  color: #64748b;
  font-size: 13px;
  margin-top: 4px;
}

.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.difficulty-option {
  position: relative;
  cursor: pointer;
}

.difficulty-option input {
  position: absolute;
  opacity: 0;
}

.difficulty-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  background: #1e293b;
  border: 2px solid #334155;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.difficulty-card small {
  display: block;
  color: #64748b;
  font-size: 12px;
  margin-top: 8px;
}

.difficulty-option input:checked + .difficulty-card {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.difficulty-card.facil {
  font-size: 18px;
  color: #10b981;
}

.difficulty-card.dificil {
  font-size: 18px;
  color: #f59e0b;
}

.preview-section {
  margin-top: 32px;
  padding: 24px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
}

.preview-section h4 {
  color: #3b82f6;
  font-size: 18px;
  margin-bottom: 16px;
}

.preview-card {
  background: #1e293b;
  padding: 20px;
  border-radius: 10px;
}

.date-preview {
  font-size: 20px;
  color: #f1f5f9;
  font-weight: 600;
  margin: 8px 0;
}

.info-text {
  color: #94a3b8;
  line-height: 1.6;
}

.performance-display {
  margin: 24px 0;
}

.performance-meter {
  position: relative;
  height: 40px;
  background: #1e293b;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid #334155;
}

.performance-fill {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  transition: width 0.5s ease;
}

.performance-fill.excellent {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.performance-fill.good {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
}

.performance-fill.medium {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.performance-fill.low {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.performance-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #f1f5f9;
  font-weight: 600;
  font-size: 18px;
}

.recommendation {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.recommendation.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.recommendation.good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.recommendation.medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.recommendation.low {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.submit-btn {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 24px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: #1e293b;
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #334155;
}

.stat-card h4 {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-number {
  color: #3b82f6;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

.timeline {
  position: relative;
  padding-left: 40px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #334155;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
}

.timeline-marker {
  position: absolute;
  left: -34px;
  top: 8px;
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border-radius: 50%;
  border: 3px solid #0f172a;
}

.timeline-item.teorico .timeline-marker {
  background: #10b981;
}

.timeline-content {
  background: #1e293b;
  padding: 16px 20px;
  border-radius: 10px;
  border: 1px solid #334155;
}

.timeline-content h5 {
  color: #f1f5f9;
  font-size: 16px;
  margin: 0 0 8px 0;
}

.timeline-date {
  color: #64748b;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.timeline-details {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  padding: 4px 12px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

.badge.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.badge.good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.badge.medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.badge.low {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

@media (max-width: 768px) {
  .revision-tool-window {
    width: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .tool-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    border-bottom: none;
    border-left: 3px solid transparent;
  }
  
  .tab-btn.active {
    border-left-color: #3b82f6;
    border-bottom-color: transparent;
  }
}
</style>