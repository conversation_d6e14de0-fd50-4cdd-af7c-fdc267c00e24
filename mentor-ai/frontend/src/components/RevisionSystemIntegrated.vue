<template>
  <div class="revision-system-overlay" @click.self="$emit('close')">
    <div class="revision-system-window">
      <!-- Header <PERSON> -->
      <div class="system-header">
        <div class="header-content">
          <div class="header-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="header-text">
            <h2>Sistema Inteligente de Revisões</h2>
            <p>Método científico com análise de performance</p>
          </div>
        </div>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Navigation Tabs -->
      <div class="system-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-btn', { active: activeTab === tab.id }]"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.name }}</span>
          <span v-if="tab.badge" class="tab-badge">{{ tab.badge }}</span>
        </button>
      </div>

      <!-- Content Area -->
      <div class="system-content">
        <!-- Tab 1: Novo Estudo -->
        <div v-if="activeTab === 'study'" class="tab-content">
          <div class="study-section">
            <h3>
              <i class="fas fa-book-open"></i>
              Registrar Novo Estudo
            </h3>

            <form @submit.prevent="registrarEstudoCompleto" class="study-form">
              <!-- Informações Básicas -->
              <div class="form-section">
                <h4>Informações do Estudo</h4>
                
                <div class="form-group">
                  <label>
                    <i class="fas fa-heading"></i>
                    Título do Conteúdo
                  </label>
                  <input 
                    v-model="novoEstudo.titulo" 
                    type="text" 
                    placeholder="Ex: Sistema Cardiovascular - Anatomia e Fisiologia"
                    required
                  >
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label>
                      <i class="fas fa-graduation-cap"></i>
                      Matéria
                    </label>
                    <select v-model="novoEstudo.materia" required>
                      <option value="">Selecione...</option>
                      <option v-for="mat in materias" :key="mat">{{ mat }}</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label>
                      <i class="fas fa-calendar"></i>
                      Data do Estudo
                    </label>
                    <input 
                      v-model="novoEstudo.data" 
                      type="date" 
                      :max="hoje"
                      required
                    >
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label>
                      <i class="fas fa-clock"></i>
                      Duração (minutos)
                    </label>
                    <input 
                      v-model.number="novoEstudo.duracao" 
                      type="number" 
                      min="10" 
                      max="480"
                      required
                    >
                  </div>

                  <div class="form-group">
                    <label>
                      <i class="fas fa-layer-group"></i>
                      Tipo de Estudo
                    </label>
                    <select v-model="novoEstudo.tipo" required>
                      <option value="leitura">Leitura</option>
                      <option value="video">Vídeo-aula</option>
                      <option value="pratica">Prática</option>
                      <option value="resumo">Resumo</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Avaliação Detalhada -->
              <div class="form-section">
                <h4>Avaliação do Conteúdo</h4>
                
                <div class="evaluation-grid">
                  <div class="eval-item">
                    <label>Complexidade</label>
                    <div class="rating-selector">
                      <button 
                        v-for="n in 5" 
                        :key="`complex-${n}`"
                        type="button"
                        @click="novoEstudo.complexidade = n"
                        :class="['rating-btn', { active: novoEstudo.complexidade >= n }]"
                      >
                        <i class="fas fa-star"></i>
                      </button>
                    </div>
                    <span class="rating-label">{{ getComplexityLabel(novoEstudo.complexidade) }}</span>
                  </div>

                  <div class="eval-item">
                    <label>Compreensão</label>
                    <div class="percentage-input">
                      <input 
                        v-model.number="novoEstudo.compreensao" 
                        type="range" 
                        min="0" 
                        max="100"
                        step="5"
                      >
                      <span class="percentage-value">{{ novoEstudo.compreensao }}%</span>
                    </div>
                  </div>

                  <div class="eval-item">
                    <label>Importância</label>
                    <div class="importance-selector">
                      <button 
                        v-for="level in importanceLevels" 
                        :key="level.value"
                        type="button"
                        @click="novoEstudo.importancia = level.value"
                        :class="['importance-btn', level.class, { active: novoEstudo.importancia === level.value }]"
                      >
                        {{ level.label }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Análise Preditiva -->
              <div class="predictive-analysis" v-if="analisePreditiva">
                <h4>
                  <i class="fas fa-chart-line"></i>
                  Análise Preditiva de Revisões
                </h4>
                
                <div class="prediction-cards">
                  <div class="pred-card">
                    <div class="pred-icon">
                      <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="pred-info">
                      <span class="pred-label">Primeiro Contato</span>
                      <span class="pred-value">{{ analisePreditiva.primeiroContato }}</span>
                    </div>
                  </div>

                  <div class="pred-card">
                    <div class="pred-icon">
                      <i class="fas fa-redo"></i>
                    </div>
                    <div class="pred-info">
                      <span class="pred-label">Revisões Estimadas</span>
                      <span class="pred-value">{{ analisePreditiva.totalRevisoes }}</span>
                    </div>
                  </div>

                  <div class="pred-card">
                    <div class="pred-icon">
                      <i class="fas fa-trophy"></i>
                    </div>
                    <div class="pred-info">
                      <span class="pred-label">Domínio Esperado</span>
                      <span class="pred-value">{{ analisePreditiva.dominio }}</span>
                    </div>
                  </div>
                </div>

                <div class="revision-timeline">
                  <h5>Cronograma Sugerido</h5>
                  <div class="timeline-preview">
                    <div 
                      v-for="(rev, idx) in analisePreditiva.cronograma" 
                      :key="idx"
                      class="timeline-event"
                    >
                      <div class="event-marker" :class="rev.tipo"></div>
                      <div class="event-info">
                        <span class="event-date">{{ rev.data }}</span>
                        <span class="event-type">{{ rev.descricao }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Notas e Tags -->
              <div class="form-section">
                <h4>Informações Adicionais</h4>
                
                <div class="form-group">
                  <label>
                    <i class="fas fa-sticky-note"></i>
                    Notas do Estudo
                  </label>
                  <textarea 
                    v-model="novoEstudo.notas" 
                    rows="3" 
                    placeholder="Pontos importantes, dúvidas, observações..."
                  ></textarea>
                </div>

                <div class="form-group">
                  <label>
                    <i class="fas fa-tags"></i>
                    Tags (separadas por vírgula)
                  </label>
                  <input 
                    v-model="novoEstudo.tags" 
                    type="text" 
                    placeholder="anatomia, cardiovascular, fisiologia"
                  >
                </div>
              </div>

              <div class="form-actions">
                <button type="button" @click="limparFormulario" class="btn-secondary">
                  <i class="fas fa-eraser"></i>
                  Limpar
                </button>
                <button type="submit" class="btn-primary">
                  <i class="fas fa-save"></i>
                  Registrar e Gerar Revisões
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Tab 2: Registro de Questões -->
        <div v-if="activeTab === 'questions'" class="tab-content">
          <div class="questions-section">
            <h3>
              <i class="fas fa-check-circle"></i>
              Registrar Performance em Questões
            </h3>

            <!-- Seleção de Conteúdo -->
            <div class="content-selector">
              <h4>Selecione o Conteúdo Revisado</h4>
              <div class="content-grid">
                <div 
                  v-for="estudo in estudosRecentes" 
                  :key="estudo.id"
                  @click="questoesForm.estudoId = estudo.id"
                  :class="['content-card', { selected: questoesForm.estudoId === estudo.id }]"
                >
                  <div class="content-header">
                    <h5>{{ estudo.titulo }}</h5>
                    <span class="content-date">{{ formatDate(estudo.data) }}</span>
                  </div>
                  <div class="content-meta">
                    <span class="meta-tag">{{ estudo.materia }}</span>
                    <span class="meta-tag">{{ estudo.revisaoNumero }}ª revisão</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Formulário de Performance -->
            <form @submit.prevent="registrarPerformance" class="performance-form" v-if="questoesForm.estudoId">
              <div class="form-section">
                <h4>Detalhes da Sessão</h4>
                
                <div class="form-row">
                  <div class="form-group">
                    <label>
                      <i class="fas fa-list-ol"></i>
                      Total de Questões
                    </label>
                    <input 
                      v-model.number="questoesForm.total" 
                      type="number" 
                      min="1" 
                      max="100"
                      required
                    >
                  </div>

                  <div class="form-group">
                    <label>
                      <i class="fas fa-check"></i>
                      Questões Corretas
                    </label>
                    <input 
                      v-model.number="questoesForm.acertos" 
                      type="number" 
                      min="0" 
                      :max="questoesForm.total"
                      required
                    >
                  </div>

                  <div class="form-group">
                    <label>
                      <i class="fas fa-clock"></i>
                      Tempo (minutos)
                    </label>
                    <input 
                      v-model.number="questoesForm.tempo" 
                      type="number" 
                      min="1"
                      required
                    >
                  </div>
                </div>

                <!-- Performance Visual -->
                <div class="performance-display">
                  <div class="performance-chart">
                    <div class="chart-circle">
                      <svg viewBox="0 0 36 36" class="circular-chart">
                        <path class="circle-bg"
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                        <path class="circle"
                          :stroke-dasharray="`${percentualQuestoes}, 100`"
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                        <text x="18" y="20.35" class="percentage">{{ percentualQuestoes }}%</text>
                      </svg>
                    </div>
                    <div class="performance-stats">
                      <div class="stat">
                        <span class="stat-label">Taxa de Acerto</span>
                        <span class="stat-value" :class="getPerformanceClass(percentualQuestoes)">
                          {{ percentualQuestoes }}%
                        </span>
                      </div>
                      <div class="stat">
                        <span class="stat-label">Tempo Médio</span>
                        <span class="stat-value">
                          {{ tempoMedioPorQuestao }} seg/questão
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Análise por Tipo -->
                <div class="question-types">
                  <h5>Análise por Tipo de Questão</h5>
                  <div class="type-grid">
                    <div 
                      v-for="tipo in tiposQuestoes" 
                      :key="tipo.id"
                      class="type-input"
                    >
                      <label>{{ tipo.nome }}</label>
                      <div class="type-inputs">
                        <input 
                          v-model.number="questoesForm.tipos[tipo.id].total" 
                          type="number" 
                          min="0"
                          placeholder="Total"
                        >
                        <span>/</span>
                        <input 
                          v-model.number="questoesForm.tipos[tipo.id].acertos" 
                          type="number" 
                          min="0"
                          placeholder="Acertos"
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dificuldades Encontradas -->
                <div class="difficulties">
                  <h5>Tópicos com Dificuldade</h5>
                  <div class="topics-selector">
                    <button 
                      v-for="topico in topicosDisponiveis" 
                      :key="topico"
                      type="button"
                      @click="toggleTopicoDificil(topico)"
                      :class="['topic-btn', { active: questoesForm.topicosDificeis.includes(topico) }]"
                    >
                      {{ topico }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- Análise e Próxima Revisão -->
              <div class="next-revision-analysis" v-if="analiseProximaRevisao">
                <h4>
                  <i class="fas fa-forward"></i>
                  Análise e Próxima Revisão
                </h4>

                <div class="analysis-cards">
                  <div class="analysis-card" :class="analiseProximaRevisao.nivel">
                    <div class="card-header">
                      <i :class="analiseProximaRevisao.icon"></i>
                      <span>{{ analiseProximaRevisao.titulo }}</span>
                    </div>
                    <p>{{ analiseProximaRevisao.mensagem }}</p>
                  </div>

                  <div class="next-revision-card">
                    <h5>Próxima Revisão</h5>
                    <div class="revision-date">
                      <i class="fas fa-calendar-alt"></i>
                      <span>{{ analiseProximaRevisao.proximaData }}</span>
                    </div>
                    <div class="revision-recommendation">
                      <i class="fas fa-lightbulb"></i>
                      <p>{{ analiseProximaRevisao.recomendacao }}</p>
                    </div>
                  </div>
                </div>

                <!-- Gráfico de Progresso -->
                <div class="progress-chart">
                  <h5>Curva de Aprendizado</h5>
                  <canvas ref="progressChart"></canvas>
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn-primary">
                  <i class="fas fa-chart-line"></i>
                  Registrar Performance
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Tab 3: Análise Geral -->
        <div v-if="activeTab === 'analysis'" class="tab-content">
          <div class="analysis-section">
            <h3>
              <i class="fas fa-analytics"></i>
              Análise Completa do Sistema
            </h3>

            <!-- Resumo Geral -->
            <div class="summary-grid">
              <div class="summary-card">
                <div class="card-icon">
                  <i class="fas fa-brain"></i>
                </div>
                <div class="card-content">
                  <h4>Taxa de Retenção</h4>
                  <div class="big-number">{{ taxaRetencaoGeral }}%</div>
                  <p>Média ponderada de todos os conteúdos</p>
                </div>
              </div>

              <div class="summary-card">
                <div class="card-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-content">
                  <h4>Evolução</h4>
                  <div class="big-number">+{{ evolucaoPercentual }}%</div>
                  <p>Melhoria nos últimos 30 dias</p>
                </div>
              </div>

              <div class="summary-card">
                <div class="card-icon">
                  <i class="fas fa-calendar-check"></i>
                </div>
                <div class="card-content">
                  <h4>Consistência</h4>
                  <div class="big-number">{{ diasConsecutivos }}</div>
                  <p>Dias consecutivos de estudo</p>
                </div>
              </div>
            </div>

            <!-- Mapa de Calor -->
            <div class="heatmap-section">
              <h4>Mapa de Atividades</h4>
              <div class="heatmap-container">
                <div class="heatmap-months">
                  <span v-for="mes in mesesHeatmap" :key="mes">{{ mes }}</span>
                </div>
                <div class="heatmap-grid">
                  <div class="heatmap-weekdays">
                    <span v-for="dia in diasSemana" :key="dia">{{ dia }}</span>
                  </div>
                  <div class="heatmap-cells">
                    <div 
                      v-for="dia in diasHeatmap" 
                      :key="dia.data"
                      class="heatmap-cell"
                      :style="{ backgroundColor: getHeatmapColor(dia.atividade) }"
                      :title="`${dia.data}: ${dia.atividade} atividades`"
                    ></div>
                  </div>
                </div>
                <div class="heatmap-legend">
                  <span>Menos</span>
                  <div class="legend-cells">
                    <div v-for="n in 5" :key="n" class="legend-cell" :style="{ backgroundColor: getHeatmapColor(n-1) }"></div>
                  </div>
                  <span>Mais</span>
                </div>
              </div>
            </div>

            <!-- Top Conteúdos -->
            <div class="top-contents">
              <h4>Conteúdos que Precisam de Atenção</h4>
              <div class="content-list">
                <div 
                  v-for="conteudo in conteudosAtencao" 
                  :key="conteudo.id"
                  class="attention-item"
                >
                  <div class="item-header">
                    <h5>{{ conteudo.titulo }}</h5>
                    <span class="performance-badge" :class="conteudo.nivel">
                      {{ conteudo.performance }}%
                    </span>
                  </div>
                  <div class="item-meta">
                    <span><i class="fas fa-calendar"></i> Última revisão: {{ conteudo.ultimaRevisao }}</span>
                    <span><i class="fas fa-redo"></i> {{ conteudo.totalRevisoes }} revisões</span>
                  </div>
                  <div class="item-action">
                    <button @click="agendarRevisaoUrgente(conteudo)" class="urgent-btn">
                      <i class="fas fa-exclamation-triangle"></i>
                      Agendar Revisão Urgente
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Insights e Recomendações -->
            <div class="insights-section">
              <h4>Insights Personalizados</h4>
              <div class="insights-grid">
                <div 
                  v-for="insight in insightsPersonalizados" 
                  :key="insight.id"
                  class="insight-card"
                  :class="insight.tipo"
                >
                  <div class="insight-icon">
                    <i :class="insight.icon"></i>
                  </div>
                  <div class="insight-content">
                    <h5>{{ insight.titulo }}</h5>
                    <p>{{ insight.descricao }}</p>
                    <button 
                      v-if="insight.acao"
                      @click="executarAcaoInsight(insight.acao)"
                      class="insight-action"
                    >
                      {{ insight.acaoTexto }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer com Resumo -->
      <div class="system-footer">
        <div class="footer-stats">
          <div class="stat">
            <i class="fas fa-book"></i>
            <span>{{ totalEstudos }} estudos</span>
          </div>
          <div class="stat">
            <i class="fas fa-check-circle"></i>
            <span>{{ totalQuestoes }} questões</span>
          </div>
          <div class="stat">
            <i class="fas fa-calendar"></i>
            <span>{{ proximasRevisoes }} revisões pendentes</span>
          </div>
        </div>
        <div class="footer-actions">
          <button @click="exportarDados" class="btn-ghost">
            <i class="fas fa-download"></i>
            Exportar
          </button>
          <button @click="verCalendario" class="btn-secondary">
            <i class="fas fa-calendar-alt"></i>
            Ver Calendário
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import Chart from 'chart.js/auto'

const emit = defineEmits(['close', 'estudo-registrado', 'revisao-agendada', 'performance-registrada'])

// Estado
const activeTab = ref('study')
const hoje = new Date().toISOString().split('T')[0]
const progressChart = ref(null)
let chartInstance = null

// Tabs
const tabs = [
  { id: 'study', name: 'Novo Estudo', icon: 'fas fa-book-open' },
  { id: 'questions', name: 'Registrar Questões', icon: 'fas fa-check-circle', badge: null },
  { id: 'analysis', name: 'Análise Geral', icon: 'fas fa-analytics' }
]

// Dados estáticos
const materias = [
  'Anatomia', 'Fisiologia', 'Patologia', 'Farmacologia',
  'Bioquímica', 'Microbiologia', 'Imunologia', 'Clínica Médica',
  'Cirurgia', 'Pediatria', 'Ginecologia', 'Psiquiatria'
]

const importanceLevels = [
  { value: 'baixa', label: 'Baixa', class: 'low' },
  { value: 'media', label: 'Média', class: 'medium' },
  { value: 'alta', label: 'Alta', class: 'high' },
  { value: 'critica', label: 'Crítica', class: 'critical' }
]

const tiposQuestoes = [
  { id: 'multipla', nome: 'Múltipla Escolha' },
  { id: 'verdadeiro', nome: 'V ou F' },
  { id: 'associacao', nome: 'Associação' },
  { id: 'dissertativa', nome: 'Dissertativa' }
]

// Formulário Novo Estudo
const novoEstudo = ref({
  titulo: '',
  materia: '',
  data: hoje,
  duracao: 60,
  tipo: 'leitura',
  complexidade: 3,
  compreensao: 70,
  importancia: 'media',
  notas: '',
  tags: ''
})

// Formulário Questões
const questoesForm = ref({
  estudoId: null,
  total: 30,
  acertos: 0,
  tempo: 60,
  tipos: {
    multipla: { total: 0, acertos: 0 },
    verdadeiro: { total: 0, acertos: 0 },
    associacao: { total: 0, acertos: 0 },
    dissertativa: { total: 0, acertos: 0 }
  },
  topicosDificeis: []
})

// Dados do sistema
const estudos = ref([])
const performances = ref([])
const revisoes = ref([])

// Computed Properties
const analisePreditiva = computed(() => {
  if (!novoEstudo.value.complexidade || !novoEstudo.value.compreensao) return null

  const complexidade = novoEstudo.value.complexidade
  const compreensao = novoEstudo.value.compreensao
  const importancia = novoEstudo.value.importancia

  // Calcular dias para primeiro contato baseado em múltiplos fatores
  let diasPrimeiroContato = 1
  if (complexidade <= 2 && compreensao >= 80) diasPrimeiroContato = 3
  else if (complexidade <= 3 && compreensao >= 60) diasPrimeiroContato = 2
  else if (complexidade >= 4 || compreensao < 40) diasPrimeiroContato = 1

  // Ajustar baseado na importância
  if (importancia === 'critica') diasPrimeiroContato = Math.max(1, diasPrimeiroContato - 1)
  
  const primeiroDia = new Date()
  primeiroDia.setDate(primeiroDia.getDate() + diasPrimeiroContato)

  // Estimar número de revisões necessárias
  const revisoesEstimadas = Math.ceil((100 - compreensao) / 15) + Math.floor(complexidade / 2)

  // Calcular tempo estimado para domínio
  const diasParaDominio = revisoesEstimadas * 7 + diasPrimeiroContato

  return {
    primeiroContato: primeiroDia.toLocaleDateString('pt-BR'),
    totalRevisoes: revisoesEstimadas,
    dominio: `~${Math.ceil(diasParaDominio / 30)} meses`,
    cronograma: gerarCronogramaSugerido(diasPrimeiroContato, revisoesEstimadas)
  }
})

const estudosRecentes = computed(() => {
  return estudos.value
    .filter(e => {
      const diasDesde = Math.floor((new Date() - new Date(e.data)) / (1000 * 60 * 60 * 24))
      return diasDesde <= 30
    })
    .map(e => ({
      ...e,
      revisaoNumero: performances.value.filter(p => p.estudoId === e.id).length + 1
    }))
    .slice(0, 6)
})

const percentualQuestoes = computed(() => {
  if (!questoesForm.value.total) return 0
  return Math.round((questoesForm.value.acertos / questoesForm.value.total) * 100)
})

const tempoMedioPorQuestao = computed(() => {
  if (!questoesForm.value.total || !questoesForm.value.tempo) return 0
  return Math.round((questoesForm.value.tempo * 60) / questoesForm.value.total)
})

const topicosDisponiveis = computed(() => {
  if (!questoesForm.value.estudoId) return []
  const estudo = estudos.value.find(e => e.id === questoesForm.value.estudoId)
  if (!estudo) return []
  
  // Extrair tópicos das tags e matéria
  const topicos = estudo.tags ? estudo.tags.split(',').map(t => t.trim()) : []
  topicos.push(estudo.materia)
  return [...new Set(topicos)]
})

const analiseProximaRevisao = computed(() => {
  const perc = percentualQuestoes.value
  if (perc === null || perc === 0) return null

  let nivel, icon, titulo, mensagem, dias, recomendacao

  if (perc >= 90) {
    nivel = 'excellent'
    icon = 'fas fa-trophy'
    titulo = 'Excelente Desempenho!'
    mensagem = 'Você demonstrou domínio do conteúdo. Continue assim!'
    dias = 30
    recomendacao = 'Revise apenas os pontos específicos que errou.'
  } else if (perc >= 80) {
    nivel = 'good'
    icon = 'fas fa-star'
    titulo = 'Muito Bom!'
    mensagem = 'Ótimo desempenho, com pequenos ajustes a fazer.'
    dias = 21
    recomendacao = 'Foque nos tipos de questão com menor acerto.'
  } else if (perc >= 70) {
    nivel = 'regular'
    icon = 'fas fa-check'
    titulo = 'Bom Progresso'
    mensagem = 'Você está no caminho certo, continue praticando.'
    dias = 14
    recomendacao = 'Revise os tópicos marcados como difíceis.'
  } else if (perc >= 60) {
    nivel = 'attention'
    icon = 'fas fa-exclamation'
    titulo = 'Atenção Necessária'
    mensagem = 'Alguns conceitos precisam ser reforçados.'
    dias = 7
    recomendacao = 'Dedique mais tempo aos fundamentos antes das questões.'
  } else {
    nivel = 'critical'
    icon = 'fas fa-exclamation-triangle'
    titulo = 'Revisão Urgente'
    mensagem = 'É necessário revisar o conteúdo teórico novamente.'
    dias = 2
    recomendacao = 'Volte ao material de estudo antes de fazer mais questões.'
  }

  const proximaData = new Date()
  proximaData.setDate(proximaData.getDate() + dias)

  return {
    nivel,
    icon,
    titulo,
    mensagem,
    dias,
    proximaData: proximaData.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    }),
    recomendacao
  }
})

// Computed para análise geral
const taxaRetencaoGeral = computed(() => {
  if (performances.value.length === 0) return 0
  
  // Calcular média ponderada por recência
  const now = new Date()
  let somaTotal = 0
  let pesoTotal = 0
  
  performances.value.forEach(p => {
    const diasDesde = Math.floor((now - new Date(p.data)) / (1000 * 60 * 60 * 24))
    const peso = Math.max(0.1, 1 - (diasDesde / 100)) // Peso diminui com o tempo
    somaTotal += (p.percentual || p.acertos / p.total * 100) * peso
    pesoTotal += peso
  })
  
  return pesoTotal > 0 ? Math.round(somaTotal / pesoTotal) : 0
})

const evolucaoPercentual = computed(() => {
  const periodo30Dias = new Date()
  periodo30Dias.setDate(periodo30Dias.getDate() - 30)
  
  const performancesRecentes = performances.value.filter(p => new Date(p.data) >= periodo30Dias)
  if (performancesRecentes.length < 2) return 0
  
  // Comparar primeira metade com segunda metade
  const metade = Math.floor(performancesRecentes.length / 2)
  const primeiraMetade = performancesRecentes.slice(0, metade)
  const segundaMetade = performancesRecentes.slice(metade)
  
  const mediaPrimeira = primeiraMetade.reduce((sum, p) => sum + (p.percentual || p.acertos / p.total * 100), 0) / primeiraMetade.length
  const mediaSegunda = segundaMetade.reduce((sum, p) => sum + (p.percentual || p.acertos / p.total * 100), 0) / segundaMetade.length
  
  return Math.round(mediaSegunda - mediaPrimeira)
})

const diasConsecutivos = computed(() => {
  const atividades = [...estudos.value, ...performances.value]
    .map(a => new Date(a.data).toDateString())
    .sort((a, b) => new Date(b) - new Date(a))
  
  if (atividades.length === 0) return 0
  
  let consecutivos = 1
  const hoje = new Date()
  let diaAtual = new Date(hoje)
  diaAtual.setDate(diaAtual.getDate() - 1)
  
  while (atividades.includes(diaAtual.toDateString())) {
    consecutivos++
    diaAtual.setDate(diaAtual.getDate() - 1)
  }
  
  return consecutivos
})

const conteudosAtencao = computed(() => {
  // Agrupar performances por estudo
  const performancesPorEstudo = {}
  
  performances.value.forEach(p => {
    if (!performancesPorEstudo[p.estudoId]) {
      performancesPorEstudo[p.estudoId] = []
    }
    performancesPorEstudo[p.estudoId].push(p)
  })
  
  // Calcular média e identificar problemáticos
  const conteudos = []
  
  Object.entries(performancesPorEstudo).forEach(([estudoId, perfs]) => {
    const estudo = estudos.value.find(e => e.id === estudoId)
    if (!estudo) return
    
    const mediaPerformance = perfs.reduce((sum, p) => sum + (p.percentual || p.acertos / p.total * 100), 0) / perfs.length
    const ultimaPerformance = perfs[perfs.length - 1]
    const diasDesdeUltima = Math.floor((new Date() - new Date(ultimaPerformance.data)) / (1000 * 60 * 60 * 24))
    
    if (mediaPerformance < 70 || diasDesdeUltima > 14) {
      conteudos.push({
        id: estudoId,
        titulo: estudo.titulo,
        performance: Math.round(mediaPerformance),
        nivel: mediaPerformance < 50 ? 'critical' : mediaPerformance < 70 ? 'attention' : 'regular',
        ultimaRevisao: `há ${diasDesdeUltima} dias`,
        totalRevisoes: perfs.length
      })
    }
  })
  
  return conteudos.sort((a, b) => a.performance - b.performance).slice(0, 5)
})

const totalEstudos = computed(() => estudos.value.length)
const totalQuestoes = computed(() => performances.value.reduce((sum, p) => sum + p.total, 0))
const proximasRevisoes = computed(() => revisoes.value.filter(r => new Date(r.data) > new Date() && !r.completada).length)

// Dados para heatmap
const mesesHeatmap = computed(() => {
  const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
  const mesAtual = new Date().getMonth()
  return [...meses.slice(mesAtual - 2), ...meses.slice(0, mesAtual + 1)]
})

const diasSemana = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']

const diasHeatmap = computed(() => {
  // Gerar últimos 90 dias
  const dias = []
  const hoje = new Date()
  
  for (let i = 89; i >= 0; i--) {
    const dia = new Date(hoje)
    dia.setDate(dia.getDate() - i)
    
    // Contar atividades neste dia
    const dataStr = dia.toISOString().split('T')[0]
    const atividades = [
      ...estudos.value.filter(e => e.data === dataStr),
      ...performances.value.filter(p => p.data === dataStr)
    ].length
    
    dias.push({
      data: dia.toLocaleDateString('pt-BR'),
      atividade: atividades
    })
  }
  
  return dias
})

const insightsPersonalizados = computed(() => {
  const insights = []
  
  // Insight 1: Melhor horário de estudo
  const horariosEstudo = estudos.value.map(e => new Date(e.data).getHours())
  const horarioMaisFrequente = mode(horariosEstudo)
  if (horarioMaisFrequente !== null) {
    insights.push({
      id: 'horario',
      tipo: 'info',
      icon: 'fas fa-clock',
      titulo: 'Melhor Horário',
      descricao: `Você tem melhor desempenho estudando às ${horarioMaisFrequente}h. Mantenha esse padrão!`,
      acao: null
    })
  }
  
  // Insight 2: Matéria com melhor desempenho
  const performancesPorMateria = {}
  performances.value.forEach(p => {
    const estudo = estudos.value.find(e => e.id === p.estudoId)
    if (!estudo) return
    
    if (!performancesPorMateria[estudo.materia]) {
      performancesPorMateria[estudo.materia] = []
    }
    performancesPorMateria[estudo.materia].push(p.percentual || p.acertos / p.total * 100)
  })
  
  let melhorMateria = null
  let melhorMedia = 0
  
  Object.entries(performancesPorMateria).forEach(([materia, perfs]) => {
    const media = perfs.reduce((sum, p) => sum + p, 0) / perfs.length
    if (media > melhorMedia) {
      melhorMedia = media
      melhorMateria = materia
    }
  })
  
  if (melhorMateria) {
    insights.push({
      id: 'melhor-materia',
      tipo: 'success',
      icon: 'fas fa-trophy',
      titulo: 'Ponto Forte',
      descricao: `${melhorMateria} é sua matéria mais forte com ${Math.round(melhorMedia)}% de acerto médio.`,
      acao: 'explorar-avancado',
      acaoTexto: 'Explorar Conteúdo Avançado'
    })
  }
  
  // Insight 3: Sugestão de pausa
  if (diasConsecutivos.value > 10) {
    insights.push({
      id: 'pausa',
      tipo: 'warning',
      icon: 'fas fa-pause-circle',
      titulo: 'Evite Burnout',
      descricao: `Você está estudando há ${diasConsecutivos.value} dias seguidos. Considere um dia de descanso.`,
      acao: null
    })
  }
  
  return insights
})

// Métodos
const getComplexityLabel = (value) => {
  const labels = ['', 'Muito Fácil', 'Fácil', 'Médio', 'Difícil', 'Muito Difícil']
  return labels[value] || ''
}

const getPerformanceClass = (percentage) => {
  if (percentage >= 80) return 'excellent'
  if (percentage >= 60) return 'good'
  if (percentage >= 40) return 'regular'
  return 'poor'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('pt-BR', {
    day: 'numeric',
    month: 'short'
  })
}

const getHeatmapColor = (atividade) => {
  if (atividade === 0) return '#1a1a2e'
  if (atividade === 1) return '#16213e'
  if (atividade === 2) return '#0f3460'
  if (atividade === 3) return '#533483'
  return '#e94560'
}

const toggleTopicoDificil = (topico) => {
  const index = questoesForm.value.topicosDificeis.indexOf(topico)
  if (index > -1) {
    questoesForm.value.topicosDificeis.splice(index, 1)
  } else {
    questoesForm.value.topicosDificeis.push(topico)
  }
}

const gerarCronogramaSugerido = (diasPrimeiro, totalRevisoes) => {
  const cronograma = []
  let dataAtual = new Date()
  dataAtual.setDate(dataAtual.getDate() + diasPrimeiro)
  
  // Primeiro contato
  cronograma.push({
    data: dataAtual.toLocaleDateString('pt-BR'),
    tipo: 'primeiro',
    descricao: 'Primeiro contato - Questões'
  })
  
  // Revisões subsequentes
  const intervalos = [2, 7, 14, 21, 30, 45, 60]
  for (let i = 0; i < Math.min(totalRevisoes - 1, intervalos.length); i++) {
    dataAtual = new Date(dataAtual)
    dataAtual.setDate(dataAtual.getDate() + intervalos[i])
    cronograma.push({
      data: dataAtual.toLocaleDateString('pt-BR'),
      tipo: 'revisao',
      descricao: `${i + 2}ª Revisão`
    })
  }
  
  return cronograma
}

const mode = (arr) => {
  if (arr.length === 0) return null
  const frequency = {}
  let maxFreq = 0
  let mode = null
  
  arr.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1
    if (frequency[val] > maxFreq) {
      maxFreq = frequency[val]
      mode = val
    }
  })
  
  return mode
}

const limparFormulario = () => {
  novoEstudo.value = {
    titulo: '',
    materia: '',
    data: hoje,
    duracao: 60,
    tipo: 'leitura',
    complexidade: 3,
    compreensao: 70,
    importancia: 'media',
    notas: '',
    tags: ''
  }
}

const registrarEstudoCompleto = () => {
  const estudo = {
    id: Date.now(),
    ...novoEstudo.value,
    dataRegistro: new Date().toISOString()
  }
  
  // Salvar estudo
  estudos.value.push(estudo)
  localStorage.setItem('estudos_sistema', JSON.stringify(estudos.value))
  
  // Gerar revisões baseadas na análise preditiva
  if (analisePreditiva.value) {
    const revisoes = []
    
    analisePreditiva.value.cronograma.forEach((evento, index) => {
      revisoes.push({
        id: `rev-${estudo.id}-${index}`,
        estudoId: estudo.id,
        titulo: `${evento.descricao}: ${estudo.titulo}`,
        materia: estudo.materia,
        data: new Date(evento.data.split('/').reverse().join('-')).toISOString(),
        tipo: evento.tipo,
        ordem: index + 1,
        completada: false,
        importancia: estudo.importancia
      })
    })
    
    // Salvar revisões
    const revisoesAtuais = JSON.parse(localStorage.getItem('revisoes_sistema') || '[]')
    localStorage.setItem('revisoes_sistema', JSON.stringify([...revisoesAtuais, ...revisoes]))
    
    emit('revisao-agendada', { estudo, revisoes })
  }
  
  emit('estudo-registrado', estudo)
  
  limparFormulario()
  alert('✅ Estudo registrado e revisões agendadas com sucesso!')
}

const registrarPerformance = () => {
  const estudo = estudos.value.find(e => e.id === questoesForm.value.estudoId)
  if (!estudo) return
  
  const performance = {
    id: Date.now(),
    estudoId: questoesForm.value.estudoId,
    titulo: estudo.titulo,
    materia: estudo.materia,
    data: new Date().toISOString().split('T')[0],
    total: questoesForm.value.total,
    acertos: questoesForm.value.acertos,
    percentual: percentualQuestoes.value,
    tempo: questoesForm.value.tempo,
    tempoMedio: tempoMedioPorQuestao.value,
    tipos: { ...questoesForm.value.tipos },
    topicosDificeis: [...questoesForm.value.topicosDificeis]
  }
  
  // Salvar performance
  performances.value.push(performance)
  localStorage.setItem('performances_sistema', JSON.stringify(performances.value))
  
  // Gerar próxima revisão baseada no desempenho
  if (analiseProximaRevisao.value) {
    const proximaRevisao = {
      id: `rev-${Date.now()}`,
      estudoId: questoesForm.value.estudoId,
      titulo: `Revisão: ${estudo.titulo}`,
      materia: estudo.materia,
      data: new Date(Date.now() + analiseProximaRevisao.value.dias * 24 * 60 * 60 * 1000).toISOString(),
      tipo: 'revisao',
      completada: false,
      importancia: estudo.importancia,
      performanceAnterior: percentualQuestoes.value
    }
    
    const revisoesAtuais = JSON.parse(localStorage.getItem('revisoes_sistema') || '[]')
    localStorage.setItem('revisoes_sistema', JSON.stringify([...revisoesAtuais, proximaRevisao]))
    
    emit('revisao-agendada', { performance, proximaRevisao })
  }
  
  emit('performance-registrada', performance)
  
  // Reset form
  questoesForm.value = {
    estudoId: null,
    total: 30,
    acertos: 0,
    tempo: 60,
    tipos: {
      multipla: { total: 0, acertos: 0 },
      verdadeiro: { total: 0, acertos: 0 },
      associacao: { total: 0, acertos: 0 },
      dissertativa: { total: 0, acertos: 0 }
    },
    topicosDificeis: []
  }
  
  // Atualizar gráfico
  updateProgressChart()
  
  alert('✅ Performance registrada e próxima revisão agendada!')
}

const agendarRevisaoUrgente = (conteudo) => {
  const revisaoUrgente = {
    id: `rev-urgent-${Date.now()}`,
    estudoId: conteudo.id,
    titulo: `URGENTE: ${conteudo.titulo}`,
    materia: estudos.value.find(e => e.id === conteudo.id)?.materia || 'Geral',
    data: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Amanhã
    tipo: 'urgente',
    completada: false,
    importancia: 'critica'
  }
  
  const revisoesAtuais = JSON.parse(localStorage.getItem('revisoes_sistema') || '[]')
  localStorage.setItem('revisoes_sistema', JSON.stringify([...revisoesAtuais, revisaoUrgente]))
  
  emit('revisao-agendada', { revisaoUrgente })
  alert('🚨 Revisão urgente agendada para amanhã!')
}

const executarAcaoInsight = (acao) => {
  switch (acao) {
    case 'explorar-avancado':
      // Redirecionar para conteúdo avançado
      emit('close')
      break
    default:
      console.log('Ação insight:', acao)
  }
}

const exportarDados = () => {
  const dados = {
    estudos: estudos.value,
    performances: performances.value,
    revisoes: revisoes.value,
    exportadoEm: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(dados, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `revisoes_backup_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const verCalendario = () => {
  emit('close')
  // Navegar para calendário
}

const updateProgressChart = () => {
  if (!progressChart.value) return
  
  const ctx = progressChart.value.getContext('2d')
  
  // Destruir gráfico anterior se existir
  if (chartInstance) {
    chartInstance.destroy()
  }
  
  // Dados para o gráfico
  const performancesOrdenadas = performances.value
    .filter(p => p.estudoId === questoesForm.value.estudoId)
    .sort((a, b) => new Date(a.data) - new Date(b.data))
  
  const labels = performancesOrdenadas.map((p, i) => `Revisão ${i + 1}`)
  const data = performancesOrdenadas.map(p => p.percentual || (p.acertos / p.total * 100))
  
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels,
      datasets: [{
        label: 'Taxa de Acerto (%)',
        data,
        borderColor: '#667eea',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        tension: 0.4,
        pointRadius: 6,
        pointHoverRadius: 8
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: value => value + '%'
          }
        }
      }
    }
  })
}

// Lifecycle
onMounted(() => {
  // Carregar dados salvos
  estudos.value = JSON.parse(localStorage.getItem('estudos_sistema') || '[]')
  performances.value = JSON.parse(localStorage.getItem('performances_sistema') || '[]')
  revisoes.value = JSON.parse(localStorage.getItem('revisoes_sistema') || '[]')
  
  // Atualizar badge de questões pendentes
  const revisoesHoje = revisoes.value.filter(r => {
    const dataRevisao = new Date(r.data).toDateString()
    return dataRevisao === new Date().toDateString() && !r.completada
  }).length
  
  if (revisoesHoje > 0) {
    tabs[1].badge = revisoesHoje
  }
})

// Watch para atualizar gráfico
watch(() => questoesForm.value.estudoId, () => {
  if (questoesForm.value.estudoId && activeTab.value === 'questions') {
    nextTick(() => updateProgressChart())
  }
})
</script>

<style scoped>
/* Container Principal */
.revision-system-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  backdrop-filter: blur(8px);
}

.revision-system-window {
  background: #0f172a;
  border-radius: 24px;
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  overflow: hidden;
}

/* Header */
.system-header {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  padding: 24px 32px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.header-text h2 {
  font-size: 24px;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0;
}

.header-text p {
  font-size: 14px;
  color: #94a3b8;
  margin: 4px 0 0;
}

.close-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  transition: all 0.3s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
}

/* Tabs */
.system-tabs {
  display: flex;
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 0;
}

.tab-btn {
  flex: 1;
  padding: 16px 24px;
  background: none;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-bottom: 3px solid transparent;
  position: relative;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

.tab-btn.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-bottom-color: #667eea;
}

.tab-badge {
  background: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

/* Content Area */
.system-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.tab-content h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 22px;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 24px;
}

/* Form Sections */
.form-section {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.form-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0 0 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #cbd5e1;
  margin-bottom: 8px;
}

.form-group label i {
  color: #667eea;
  font-size: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #f1f5f9;
  font-size: 15px;
  transition: all 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(15, 23, 42, 0.8);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* Evaluation Grid */
.evaluation-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.eval-item {
  text-align: center;
}

.eval-item label {
  display: block;
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 12px;
}

.rating-selector {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.rating-btn {
  background: none;
  border: none;
  color: #475569;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s;
  padding: 4px;
}

.rating-btn.active {
  color: #fbbf24;
}

.rating-label {
  font-size: 13px;
  color: #e2e8f0;
  font-weight: 500;
}

.percentage-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.percentage-input input[type="range"] {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: rgba(148, 163, 184, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.percentage-input input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
}

.percentage-value {
  min-width: 50px;
  font-weight: 600;
  color: #667eea;
}

.importance-selector {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.importance-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.importance-btn.active {
  border-color: currentColor;
  background: rgba(255, 255, 255, 0.1);
}

.importance-btn.low { color: #22c55e; }
.importance-btn.medium { color: #3b82f6; }
.importance-btn.high { color: #f59e0b; }
.importance-btn.critical { color: #ef4444; }

/* Predictive Analysis */
.predictive-analysis {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.predictive-analysis h4 {
  color: #a5b4fc;
  margin-bottom: 20px;
}

.prediction-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.pred-card {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.pred-icon {
  width: 48px;
  height: 48px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #a5b4fc;
}

.pred-info {
  flex: 1;
}

.pred-label {
  display: block;
  font-size: 13px;
  color: #94a3b8;
  margin-bottom: 4px;
}

.pred-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
}

.revision-timeline h5 {
  font-size: 15px;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.timeline-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-event {
  display: flex;
  align-items: center;
  gap: 12px;
}

.event-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #667eea;
  position: relative;
}

.event-marker.primeiro {
  background: #10b981;
}

.event-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.event-date {
  font-weight: 500;
  color: #e2e8f0;
}

.event-type {
  color: #94a3b8;
  font-size: 14px;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.btn-primary,
.btn-secondary,
.btn-ghost {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.btn-ghost {
  background: transparent;
  color: #94a3b8;
}

.btn-ghost:hover {
  color: #e2e8f0;
}

/* Questions Section */
.content-selector {
  margin-bottom: 32px;
}

.content-selector h4 {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 16px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.content-card {
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.content-card:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.content-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 12px;
}

.content-header h5 {
  font-size: 15px;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0;
}

.content-date {
  font-size: 13px;
  color: #94a3b8;
}

.content-meta {
  display: flex;
  gap: 8px;
}

.meta-tag {
  padding: 4px 8px;
  background: rgba(102, 126, 234, 0.2);
  color: #a5b4fc;
  border-radius: 6px;
  font-size: 12px;
}

/* Performance Display */
.performance-display {
  margin: 24px 0;
}

.performance-chart {
  display: flex;
  align-items: center;
  gap: 32px;
}

.chart-circle {
  width: 120px;
  height: 120px;
}

.circular-chart {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  max-height: 100%;
}

.circle-bg {
  fill: none;
  stroke: rgba(148, 163, 184, 0.2);
  stroke-width: 3;
}

.circle {
  fill: none;
  stroke: #667eea;
  stroke-width: 3;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: center;
  transition: stroke-dasharray 0.5s;
}

.percentage {
  fill: #f1f5f9;
  font-size: 0.5em;
  text-anchor: middle;
  font-weight: 700;
}

.performance-stats {
  flex: 1;
}

.stat {
  margin-bottom: 16px;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

.stat-value.excellent { color: #10b981; }
.stat-value.good { color: #3b82f6; }
.stat-value.regular { color: #f59e0b; }
.stat-value.poor { color: #ef4444; }

/* Question Types */
.question-types {
  margin-top: 32px;
}

.question-types h5 {
  font-size: 15px;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.type-input label {
  display: block;
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 8px;
}

.type-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-inputs input {
  width: 60px;
  padding: 8px;
  text-align: center;
}

.type-inputs span {
  color: #64748b;
}

/* Difficulties */
.difficulties {
  margin-top: 32px;
}

.difficulties h5 {
  font-size: 15px;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.topics-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topic-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #94a3b8;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.topic-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.topic-btn.active {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

/* Next Revision Analysis */
.next-revision-analysis {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  padding: 24px;
  margin-top: 32px;
}

.next-revision-analysis h4 {
  color: #a5b4fc;
  margin-bottom: 20px;
}

.analysis-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.analysis-card {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.analysis-card.excellent { border-color: rgba(16, 185, 129, 0.3); }
.analysis-card.good { border-color: rgba(59, 130, 246, 0.3); }
.analysis-card.regular { border-color: rgba(245, 158, 11, 0.3); }
.analysis-card.attention { border-color: rgba(245, 158, 11, 0.5); }
.analysis-card.critical { border-color: rgba(239, 68, 68, 0.5); }

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #f1f5f9;
}

.analysis-card p {
  color: #cbd5e1;
  line-height: 1.6;
}

.next-revision-card {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.next-revision-card h5 {
  font-size: 16px;
  font-weight: 600;
  color: #a5b4fc;
  margin-bottom: 16px;
}

.revision-date {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 16px;
}

.revision-recommendation {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.revision-recommendation p {
  margin: 0;
  color: #cbd5e1;
  font-size: 14px;
  line-height: 1.5;
}

.progress-chart {
  margin-top: 24px;
}

.progress-chart h5 {
  font-size: 15px;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.progress-chart canvas {
  width: 100%;
  height: 200px;
}

/* Analysis Section */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.summary-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  gap: 20px;
  transition: all 0.3s;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

.card-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  flex-shrink: 0;
}

.card-content h4 {
  font-size: 16px;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 8px;
}

.big-number {
  font-size: 36px;
  font-weight: 700;
  color: #f1f5f9;
  line-height: 1;
  margin-bottom: 8px;
}

.card-content p {
  font-size: 13px;
  color: #64748b;
  margin: 0;
}

/* Heatmap */
.heatmap-section {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
}

.heatmap-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 20px;
}

.heatmap-container {
  position: relative;
}

.heatmap-months {
  display: flex;
  justify-content: space-around;
  margin-bottom: 8px;
  padding-left: 30px;
  font-size: 12px;
  color: #94a3b8;
}

.heatmap-grid {
  display: flex;
  gap: 8px;
}

.heatmap-weekdays {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-right: 8px;
  font-size: 12px;
  color: #94a3b8;
  width: 20px;
}

.heatmap-weekdays span {
  height: 13px;
  display: flex;
  align-items: center;
}

.heatmap-cells {
  display: grid;
  grid-template-columns: repeat(13, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 2px;
  flex: 1;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  border: 1px solid #667eea;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  font-size: 12px;
  color: #94a3b8;
}

.legend-cells {
  display: flex;
  gap: 2px;
}

.legend-cell {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Top Contents */
.top-contents {
  margin-bottom: 32px;
}

.top-contents h4 {
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 20px;
}

.content-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.attention-item {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s;
}

.attention-item:hover {
  border-color: rgba(239, 68, 68, 0.3);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-header h5 {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0;
}

.performance-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.performance-badge.critical {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.performance-badge.attention {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.performance-badge.regular {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.item-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #94a3b8;
}

.item-meta span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.urgent-btn {
  padding: 8px 16px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #ef4444;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.urgent-btn:hover {
  background: #ef4444;
  color: white;
}

/* Insights */
.insights-section {
  margin-top: 32px;
}

.insights-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 20px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.insight-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  gap: 16px;
  transition: all 0.3s;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.insight-card.info { border-color: rgba(59, 130, 246, 0.3); }
.insight-card.success { border-color: rgba(16, 185, 129, 0.3); }
.insight-card.warning { border-color: rgba(245, 158, 11, 0.3); }

.insight-icon {
  width: 48px;
  height: 48px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #a5b4fc;
  flex-shrink: 0;
}

.insight-content {
  flex: 1;
}

.insight-content h5 {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 8px;
}

.insight-content p {
  font-size: 14px;
  color: #cbd5e1;
  line-height: 1.5;
  margin: 0 0 12px;
}

.insight-action {
  padding: 6px 12px;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  color: #a5b4fc;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.insight-action:hover {
  background: rgba(102, 126, 234, 0.3);
  color: #c7d2fe;
}

/* Footer */
.system-footer {
  background: rgba(30, 41, 59, 0.5);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  padding: 20px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-stats {
  display: flex;
  gap: 24px;
}

.footer-stats .stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #94a3b8;
}

.footer-stats .stat i {
  color: #667eea;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

/* Responsive */
@media (max-width: 1024px) {
  .revision-system-window {
    width: 100%;
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .evaluation-grid,
  .prediction-cards,
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .analysis-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .system-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .tab-btn {
    white-space: nowrap;
    min-width: 150px;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .type-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .footer-stats {
    display: none;
  }
}
</style>