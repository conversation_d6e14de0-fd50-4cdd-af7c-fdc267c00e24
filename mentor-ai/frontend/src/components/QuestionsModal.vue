<template>
  <div class="modal-overlay">
    <div class="questions-modal">
      <div class="modal-header">
        <div class="header-info">
          <h2>
            <i class="fas fa-question-circle"></i>
            {{ contact?.title || '<PERSON><PERSON><PERSON>ões' }}
          </h2>
          <p class="subject-tag">{{ contact?.subject }}</p>
        </div>
        <button @click="handleClose" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        <span class="progress-text">{{ currentQuestion }}/{{ totalQuestions }}</span>
      </div>

      <div class="modal-body">
        <div v-if="!sessionStarted" class="session-intro">
          <div class="intro-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <h3>Pronto para começar?</h3>
          <p>Esta sessão contém {{ totalQuestions }} questões sobre {{ contact?.subject }}.</p>
          <p class="time-estimate">
            <i class="fas fa-clock"></i>
            Tempo estimado: ~{{ estimatedTime }} minutos
          </p>
          <button @click="startSession" class="start-btn">
            <i class="fas fa-play"></i>
            Iniciar Questões
          </button>
        </div>

        <div v-else-if="!sessionCompleted" class="question-container">
          <div class="question-header">
            <span class="question-number">Questão {{ currentQuestion }}</span>
            <div class="timer">
              <i class="fas fa-stopwatch"></i>
              {{ formattedTime }}
            </div>
          </div>

          <div class="question-content">
            <p class="question-text">{{ currentQuestionData.text }}</p>
            
            <div class="options-list">
              <label 
                v-for="(option, index) in currentQuestionData.options" 
                :key="index"
                class="option-item"
                :class="getOptionClass(index)"
              >
                <input 
                  type="radio" 
                  :name="`question-${currentQuestion}`"
                  :value="index"
                  v-model="selectedAnswer"
                  :disabled="answered"
                />
                <span class="option-letter">{{ getOptionLetter(index) }}</span>
                <span class="option-text">{{ option }}</span>
                <i v-if="answered && index === currentQuestionData.correct" class="fas fa-check-circle correct-icon"></i>
                <i v-if="answered && index === selectedAnswer && index !== currentQuestionData.correct" class="fas fa-times-circle wrong-icon"></i>
              </label>
            </div>

            <div v-if="answered && currentQuestionData.explanation" class="explanation">
              <h4>
                <i class="fas fa-lightbulb"></i>
                Explicação
              </h4>
              <p>{{ currentQuestionData.explanation }}</p>
            </div>
          </div>

          <div class="question-actions">
            <button v-if="!answered" @click="submitAnswer" :disabled="selectedAnswer === null" class="submit-btn">
              <i class="fas fa-check"></i>
              Confirmar Resposta
            </button>
            <button v-else @click="nextQuestion" class="next-btn">
              <i class="fas fa-arrow-right"></i>
              {{ currentQuestion < totalQuestions ? 'Próxima Questão' : 'Finalizar' }}
            </button>
          </div>
        </div>

        <div v-else class="session-complete">
          <div class="complete-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <h3>Sessão Concluída!</h3>
          
          <div class="results-summary">
            <div class="result-stat">
              <span class="stat-label">Acertos</span>
              <span class="stat-value success">{{ correctAnswers }}</span>
            </div>
            <div class="result-stat">
              <span class="stat-label">Erros</span>
              <span class="stat-value error">{{ wrongAnswers }}</span>
            </div>
            <div class="result-stat">
              <span class="stat-label">Percentual</span>
              <span class="stat-value percentage">{{ performancePercentage }}%</span>
            </div>
          </div>

          <div class="performance-message">
            <i :class="performanceIcon"></i>
            <p>{{ performanceMessage }}</p>
          </div>

          <div class="next-revision-info">
            <h4>
              <i class="fas fa-brain"></i>
              Análise Inteligente de Revisão
            </h4>
            
            <div class="analysis-grid">
              <div class="analysis-item">
                <span class="analysis-label">Desempenho</span>
                <span class="analysis-value">{{ performancePercentage }}%</span>
              </div>
              <div class="analysis-item">
                <span class="analysis-label">Confiança</span>
                <span class="analysis-value">{{ calculateNextRevision.analysis.confidence }}%</span>
              </div>
              <div class="analysis-item">
                <span class="analysis-label">Tempo Médio</span>
                <span class="analysis-value">{{ Math.round(calculateNextRevision.analysis.averageTime) }}s</span>
              </div>
              <div class="analysis-item">
                <span class="analysis-label">Intervalo</span>
                <span class="analysis-value">{{ calculateNextRevision.days }} dias</span>
              </div>
            </div>
            
            <div class="recommendations" v-if="calculateNextRevision.recommendation.length > 0">
              <h5>
                <i class="fas fa-lightbulb"></i>
                Recomendações Personalizadas
              </h5>
              <ul>
                <li v-for="(rec, index) in calculateNextRevision.recommendation" :key="index">
                  {{ rec }}
                </li>
              </ul>
            </div>
            
            <div class="next-date">
              <i class="fas fa-calendar-check"></i>
              <div>
                <span class="date-label">Próxima revisão agendada para:</span>
                <span class="date-value">{{ nextRevisionDate }}</span>
              </div>
            </div>
            
            <div class="spaced-repetition-info">
              <i class="fas fa-info-circle"></i>
              <p>Sistema baseado na curva de esquecimento de Ebbinghaus, adaptado para estudos médicos com análise de confiança e tempo de resposta.</p>
            </div>
          </div>

          <button @click="finishSession" class="finish-btn">
            <i class="fas fa-check-circle"></i>
            Concluir e Agendar Revisão
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';

export default {
  name: 'QuestionsModal',
  props: {
    contact: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'complete'],
  setup(props, { emit }) {
    // State
    const sessionStarted = ref(false);
    const sessionCompleted = ref(false);
    const currentQuestion = ref(1);
    const totalQuestions = ref(30);
    const selectedAnswer = ref(null);
    const answered = ref(false);
    const correctAnswers = ref(0);
    const wrongAnswers = ref(0);
    const timeElapsed = ref(0);
    const timer = ref(null);

    // Mock question data - In real app, this would come from an API
    const questions = ref(generateMockQuestions());
    
    const currentQuestionData = computed(() => questions.value[currentQuestion.value - 1]);
    
    const progressPercentage = computed(() => {
      if (!sessionStarted.value) return 0;
      return Math.round((currentQuestion.value / totalQuestions.value) * 100);
    });

    const estimatedTime = computed(() => Math.round(totalQuestions.value * 1.5));

    const formattedTime = computed(() => {
      const minutes = Math.floor(timeElapsed.value / 60);
      const seconds = timeElapsed.value % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    });

    const performancePercentage = computed(() => {
      if (correctAnswers.value + wrongAnswers.value === 0) return 0;
      return Math.round((correctAnswers.value / totalQuestions.value) * 100);
    });

    const performanceIcon = computed(() => {
      const percentage = performancePercentage.value;
      if (percentage >= 80) return 'fas fa-star';
      if (percentage >= 65) return 'fas fa-thumbs-up';
      if (percentage >= 50) return 'fas fa-check';
      return 'fas fa-redo';
    });

    const performanceMessage = computed(() => {
      const percentage = performancePercentage.value;
      if (percentage >= 80) return 'Excelente desempenho! Você domina bem este conteúdo.';
      if (percentage >= 65) return 'Bom trabalho! Você está no caminho certo.';
      if (percentage >= 50) return 'Resultado satisfatório. Continue praticando para melhorar.';
      return 'Precisa de mais estudo neste tópico. Não desista!';
    });

    // Cálculo inteligente de revisão baseado na metodologia de espaçamento
    const calculateNextRevision = computed(() => {
      const percentage = performancePercentage.value;
      const totalQuestions = correctAnswers.value + wrongAnswers.value;
      const averageTimePerQuestion = timeElapsed.value / totalQuestions;
      
      // Análise detalhada do desempenho
      const performanceAnalysis = {
        percentage,
        totalQuestions,
        correctAnswers: correctAnswers.value,
        wrongAnswers: wrongAnswers.value,
        averageTime: averageTimePerQuestion,
        confidence: calculateConfidenceLevel()
      };
      
      // Cálculo do intervalo baseado em múltiplos fatores
      const baseInterval = calculateBaseInterval(percentage);
      const adjustedInterval = applyPerformanceModifiers(baseInterval, performanceAnalysis);
      
      return {
        days: adjustedInterval,
        analysis: performanceAnalysis,
        recommendation: getRecommendation(performanceAnalysis)
      };
    });
    
    // Função para calcular o nível de confiança baseado em padrões de resposta
    function calculateConfidenceLevel() {
      const totalAnswered = correctAnswers.value + wrongAnswers.value;
      if (totalAnswered === 0) return 0;
      
      // Considera tempo médio por questão
      const avgTime = timeElapsed.value / totalAnswered;
      const optimalTime = 90; // 90 segundos por questão
      
      // Penaliza respostas muito rápidas (possível chute)
      const timeFactor = avgTime < 30 ? 0.7 : 
                        avgTime > 180 ? 0.9 : 
                        1.0;
      
      const accuracyFactor = correctAnswers.value / totalAnswered;
      
      return Math.round(accuracyFactor * timeFactor * 100);
    }
    
    // Calcula intervalo base usando curva de esquecimento
    function calculateBaseInterval(percentage) {
      // Intervalos baseados na curva de esquecimento de Ebbinghaus
      // Ajustados para medicina onde a retenção é crítica
      
      if (percentage >= 90) return 30;      // Excelente - 1 mês
      if (percentage >= 85) return 21;      // Muito bom - 3 semanas
      if (percentage >= 80) return 14;      // Bom - 2 semanas
      if (percentage >= 75) return 10;      // Satisfatório+ - 10 dias
      if (percentage >= 70) return 7;       // Satisfatório - 1 semana
      if (percentage >= 65) return 5;       // Regular+ - 5 dias
      if (percentage >= 60) return 3;       // Regular - 3 dias
      if (percentage >= 55) return 2;       // Insuficiente+ - 2 dias
      return 1;                             // Insuficiente - 1 dia
    }
    
    // Aplica modificadores baseados em análise detalhada
    function applyPerformanceModifiers(baseInterval, analysis) {
      let modifier = 1.0;
      
      // Modificador de confiança
      if (analysis.confidence > 80) {
        modifier *= 1.2; // Aumenta intervalo se alta confiança
      } else if (analysis.confidence < 50) {
        modifier *= 0.8; // Reduz intervalo se baixa confiança
      }
      
      // Modificador de consistência (se houver muitos erros concentrados)
      if (analysis.wrongAnswers > 5 && analysis.percentage < 70) {
        modifier *= 0.7; // Reduz intervalo significativamente
      }
      
      // Modificador de tempo
      if (analysis.averageTime < 30) {
        modifier *= 0.9; // Respostas muito rápidas podem indicar chutes
      }
      
      // Modificador de dificuldade (baseado no assunto)
      const subjectDifficulty = getSubjectDifficulty(props.contact?.subject);
      modifier *= subjectDifficulty;
      
      return Math.max(1, Math.round(baseInterval * modifier));
    }
    
    // Retorna fator de dificuldade baseado no assunto
    function getSubjectDifficulty(subject) {
      const difficultyMap = {
        'Anatomia': 0.8,
        'Fisiologia': 0.85,
        'Farmacologia': 0.75,
        'Patologia': 0.8,
        'Clínica Médica': 0.9,
        'Cirurgia': 0.85,
        'Pediatria': 0.9,
        'Ginecologia': 0.9
      };
      
      return difficultyMap[subject] || 1.0;
    }
    
    // Gera recomendação personalizada
    function getRecommendation(analysis) {
      const recommendations = [];
      
      if (analysis.percentage < 60) {
        recommendations.push('Revisar conceitos fundamentais');
        recommendations.push('Fazer mais exercícios práticos');
      }
      
      if (analysis.confidence < 60) {
        recommendations.push('Dedicar mais tempo por questão');
        recommendations.push('Revisar explicações detalhadamente');
      }
      
      if (analysis.wrongAnswers > 10) {
        recommendations.push('Identificar padrões nos erros');
        recommendations.push('Focar nos tópicos com mais dificuldade');
      }
      
      if (analysis.averageTime < 45) {
        recommendations.push('Ler as questões com mais atenção');
      }
      
      return recommendations;
    }
    
    const nextRevisionDate = computed(() => {
      const revision = calculateNextRevision.value;
      const date = new Date();
      date.setDate(date.getDate() + revision.days);
      
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    });

    // Methods
    function generateMockQuestions() {
      const mockQuestions = [];
      for (let i = 1; i <= 30; i++) {
        mockQuestions.push({
          text: `Esta é uma questão exemplo ${i} sobre ${props.contact?.subject || 'o conteúdo'}. Qual das alternativas abaixo está correta?`,
          options: [
            'Primeira alternativa com texto explicativo detalhado',
            'Segunda alternativa com informações relevantes',
            'Terceira alternativa apresentando conceitos importantes',
            'Quarta alternativa com dados complementares',
            'Quinta alternativa incluindo exemplos práticos'
          ],
          correct: Math.floor(Math.random() * 5),
          explanation: 'Esta é a explicação detalhada da resposta correta, incluindo os conceitos importantes e por que as outras alternativas estão incorretas.'
        });
      }
      return mockQuestions;
    }

    function getOptionLetter(index) {
      return String.fromCharCode(65 + index);
    }

    function getOptionClass(index) {
      if (!answered.value) return '';
      if (index === currentQuestionData.value.correct) return 'correct';
      if (index === selectedAnswer.value && index !== currentQuestionData.value.correct) return 'wrong';
      return 'disabled';
    }

    function startSession() {
      sessionStarted.value = true;
      startTimer();
    }

    function startTimer() {
      timer.value = setInterval(() => {
        timeElapsed.value++;
      }, 1000);
    }

    function stopTimer() {
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
    }

    function submitAnswer() {
      if (selectedAnswer.value === null) return;
      
      answered.value = true;
      
      if (selectedAnswer.value === currentQuestionData.value.correct) {
        correctAnswers.value++;
      } else {
        wrongAnswers.value++;
      }
    }

    function nextQuestion() {
      if (currentQuestion.value < totalQuestions.value) {
        currentQuestion.value++;
        selectedAnswer.value = null;
        answered.value = false;
      } else {
        completeSession();
      }
    }

    function completeSession() {
      sessionCompleted.value = true;
      stopTimer();
    }

    function finishSession() {
      const revision = calculateNextRevision.value;
      
      const result = {
        // Dados básicos da sessão
        total: totalQuestions.value,
        correct: correctAnswers.value,
        wrong: wrongAnswers.value,
        percentage: performancePercentage.value,
        timeElapsed: timeElapsed.value,
        
        // Análise detalhada
        analysis: {
          confidence: revision.analysis.confidence,
          averageTimePerQuestion: revision.analysis.averageTime,
          nextRevisionDays: revision.days,
          recommendations: revision.recommendation
        },
        
        // Dados para agendamento
        scheduling: {
          subject: props.contact?.subject,
          topic: props.contact?.title,
          nextRevisionDate: new Date(Date.now() + revision.days * 24 * 60 * 60 * 1000),
          priority: performancePercentage.value < 60 ? 'high' : 
                   performancePercentage.value < 75 ? 'medium' : 'low',
          difficulty: getSubjectDifficulty(props.contact?.subject),
          
          // Metadados para rastreamento
          metadata: {
            totalQuestions: totalQuestions.value,
            correctAnswers: correctAnswers.value,
            wrongAnswers: wrongAnswers.value,
            confidence: revision.analysis.confidence,
            averageTime: Math.round(revision.analysis.averageTime),
            studySessionId: Date.now() // ID único para a sessão
          }
        }
      };
      
      emit('complete', result);
    }

    function handleClose() {
      if (sessionStarted.value && !sessionCompleted.value) {
        if (confirm('Tem certeza que deseja sair? Seu progresso será perdido.')) {
          stopTimer();
          emit('close');
        }
      } else {
        emit('close');
      }
    }

    onUnmounted(() => {
      stopTimer();
    });

    return {
      // State
      sessionStarted,
      sessionCompleted,
      currentQuestion,
      totalQuestions,
      selectedAnswer,
      answered,
      correctAnswers,
      wrongAnswers,
      currentQuestionData,
      
      // Computed
      progressPercentage,
      estimatedTime,
      formattedTime,
      performancePercentage,
      performanceIcon,
      performanceMessage,
      calculateNextRevision,
      nextRevisionDate,
      
      // Methods
      getOptionLetter,
      getOptionClass,
      startSession,
      submitAnswer,
      nextQuestion,
      finishSession,
      handleClose
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.questions-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.header-info h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-info h2 i {
  color: #3b82f6;
}

.subject-tag {
  margin: 0.25rem 0 0 2.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.progress-bar {
  height: 4px;
  background: rgba(148, 163, 184, 0.1);
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: width 0.3s ease;
  position: relative;
}

.progress-text {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
  background: #1e293b;
  padding: 0 0.5rem;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* Session Intro */
.session-intro {
  text-align: center;
  padding: 3rem 1rem;
}

.intro-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2rem;
  color: white;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.session-intro h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1rem;
}

.session-intro p {
  color: #94a3b8;
  margin: 0.5rem 0;
}

.time-estimate {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0 2rem;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  color: #3b82f6;
  font-weight: 500;
}

.start-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s;
}

.start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

/* Question Container */
.question-container {
  max-width: 700px;
  margin: 0 auto;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.question-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  color: #e4e6eb;
  font-weight: 500;
}

.timer i {
  color: #3b82f6;
}

.question-text {
  font-size: 1.125rem;
  line-height: 1.6;
  color: #e4e6eb;
  margin-bottom: 2rem;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.option-item:hover:not(.disabled) {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
}

.option-item input {
  display: none;
}

.option-letter {
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #94a3b8;
  margin-right: 1rem;
  transition: all 0.2s;
}

.option-item:hover:not(.disabled) .option-letter {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.option-item input:checked ~ .option-letter {
  background: #3b82f6;
  color: white;
}

.option-text {
  flex: 1;
  color: #e4e6eb;
  font-size: 0.875rem;
}

.option-item.correct {
  background: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
}

.option-item.correct .option-letter {
  background: #10b981;
  color: white;
}

.option-item.wrong {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.option-item.wrong .option-letter {
  background: #ef4444;
  color: white;
}

.option-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.correct-icon,
.wrong-icon {
  position: absolute;
  right: 1rem;
  font-size: 1.25rem;
}

.correct-icon {
  color: #10b981;
}

.wrong-icon {
  color: #ef4444;
}

.explanation {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.explanation h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #6366f1;
  margin: 0 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.explanation p {
  color: #94a3b8;
  line-height: 1.6;
  margin: 0;
}

.question-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.submit-btn,
.next-btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.submit-btn {
  background: linear-gradient(135deg, #10b981, #06b6d4);
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.next-btn {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.next-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Session Complete */
.session-complete {
  text-align: center;
  padding: 2rem;
}

.complete-icon {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 3rem;
  color: white;
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
}

.session-complete h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #e4e6eb;
  margin: 0 0 2rem;
}

.results-summary {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 2rem;
}

.result-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  text-transform: uppercase;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
}

.stat-value.success {
  color: #10b981;
}

.stat-value.error {
  color: #ef4444;
}

.stat-value.percentage {
  color: #6366f1;
}

.performance-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  margin: 2rem 0;
}

.performance-message i {
  font-size: 1.5rem;
  color: #6366f1;
}

.performance-message p {
  color: #e4e6eb;
  font-size: 1.125rem;
  margin: 0;
}

.next-revision-info {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
}

.next-revision-info h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.next-revision-info h4 i {
  color: #6366f1;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.analysis-item {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 10px;
}

.analysis-label {
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
}

.analysis-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #6366f1;
}

.recommendations {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.1);
  border-radius: 10px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.recommendations h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #f59e0b;
  margin: 0 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendations ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.recommendations li {
  color: #e4e6eb;
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
}

.recommendations li:before {
  content: "→";
  position: absolute;
  left: 0;
  color: #f59e0b;
  font-weight: 600;
}

.next-date {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  margin-bottom: 1rem;
}

.next-date i {
  font-size: 1.5rem;
  color: #10b981;
}

.next-date div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.date-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #10b981;
}

.spaced-repetition-info {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
}

.spaced-repetition-info i {
  color: #6366f1;
  margin-top: 0.125rem;
}

.spaced-repetition-info p {
  color: #94a3b8;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.finish-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981, #06b6d4);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s;
}

.finish-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

/* Scrollbar */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Animations */
@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
</style>