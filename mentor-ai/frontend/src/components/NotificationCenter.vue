<template>
  <div class="notification-center">
    <button @click="toggleNotifications" class="notification-button" :class="{ 'has-notifications': unreadCount > 0 }">
      <i class="fas fa-bell"></i>
      <span v-if="unreadCount > 0" class="notification-badge">{{ unreadCount }}</span>
    </button>
    
    <transition name="dropdown">
      <div v-if="showNotifications" class="notification-dropdown" @click.stop>
        <div class="dropdown-header">
          <h3>Notificações</h3>
          <button v-if="notifications.length > 0" @click="markAllAsRead" class="mark-read-btn">
            Marcar todas como lidas
          </button>
        </div>
        
        <div class="notifications-list">
          <div v-if="notifications.length === 0" class="empty-state">
            <i class="fas fa-bell-slash"></i>
            <p>Nenhuma notificação</p>
          </div>
          
          <div v-else>
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="notification-item"
              :class="{ unread: !notification.read }"
              @click="markAsRead(notification.id)"
            >
              <div class="notification-icon" :class="notification.type">
                <i :class="getNotificationIcon(notification.type)"></i>
              </div>
              <div class="notification-content">
                <p class="notification-message">{{ notification.message }}</p>
                <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'NotificationCenter',
  setup() {
    const store = useStore();
    const showNotifications = ref(false);
    
    // Mock notifications for now
    const notifications = ref([]);
    
    const unreadCount = computed(() => 
      notifications.value.filter(n => !n.read).length
    );
    
    const toggleNotifications = () => {
      showNotifications.value = !showNotifications.value;
    };
    
    const markAsRead = (id) => {
      const notification = notifications.value.find(n => n.id === id);
      if (notification) {
        notification.read = true;
      }
    };
    
    const markAllAsRead = () => {
      notifications.value.forEach(n => n.read = true);
    };
    
    const getNotificationIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle',
        warning: 'fas fa-exclamation-triangle',
        error: 'fas fa-times-circle',
        info: 'fas fa-info-circle',
        revision: 'fas fa-clock',
        achievement: 'fas fa-trophy'
      };
      return icons[type] || 'fas fa-bell';
    };
    
    const formatTime = (timestamp) => {
      const now = new Date();
      const date = new Date(timestamp);
      const diff = Math.floor((now - date) / 1000);
      
      if (diff < 60) return 'agora';
      if (diff < 3600) return `${Math.floor(diff / 60)}m atrás`;
      if (diff < 86400) return `${Math.floor(diff / 3600)}h atrás`;
      return `${Math.floor(diff / 86400)}d atrás`;
    };
    
    const handleClickOutside = (event) => {
      const notificationEl = document.querySelector('.notification-center');
      if (notificationEl && !notificationEl.contains(event.target)) {
        showNotifications.value = false;
      }
    };
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });
    
    return {
      showNotifications,
      notifications,
      unreadCount,
      toggleNotifications,
      markAsRead,
      markAllAsRead,
      getNotificationIcon,
      formatTime
    };
  }
};
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-button {
  position: relative;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.notification-button:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #e4e6eb;
}

.notification-button.has-notifications {
  color: #6366f1;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.notification-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 360px;
  max-height: 480px;
  background: #1e293b;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.dropdown-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.mark-read-btn {
  background: none;
  border: none;
  color: #6366f1;
  font-size: 0.875rem;
  cursor: pointer;
  transition: color 0.2s;
}

.mark-read-btn:hover {
  color: #8b5cf6;
}

.notifications-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #64748b;
}

.empty-state svg {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

.notification-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.05);
  cursor: pointer;
  transition: background 0.2s;
}

.notification-item:hover {
  background: rgba(148, 163, 184, 0.05);
}

.notification-item.unread {
  background: rgba(99, 102, 241, 0.05);
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.notification-icon.warning {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.notification-icon.error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.notification-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.notification-icon.revision {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.notification-icon.achievement {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  margin: 0 0 0.25rem;
  font-size: 0.875rem;
  color: #e4e6eb;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: #64748b;
}

/* Dropdown transition */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom scrollbar */
.notifications-list::-webkit-scrollbar {
  width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.notifications-list::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>