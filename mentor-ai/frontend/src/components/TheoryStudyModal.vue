<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="theory-modal">
      <div class="modal-header">
        <div class="modal-title">
          <i class="fas fa-book"></i>
          <h2>Registrar Estudo Teórico</h2>
        </div>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-body">
        <div class="form-section">
          <h3>Informações do Estudo</h3>
          
          <div class="form-group">
            <label>Título do Conteúdo</label>
            <input 
              v-model="formData.title" 
              type="text" 
              class="form-control" 
              placeholder="Ex: Anatomia do Sistema Cardiovascular"
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Disciplina</label>
              <select v-model="formData.subject" class="form-control" required>
                <option value="">Selecione...</option>
                <option value="Anatomia">Anatomia</option>
                <option value="Fisiologia">Fisiologia</option>
                <option value="Patologia">Patologia</option>
                <option value="Farmacologia">Farmacologia</option>
                <option value="Microbiologia">Microbiologia</option>
                <option value="Bioquímica">Bioquímica</option>
                <option value="Clínica Médica">Clínica Médica</option>
                <option value="Cirurgia">Cirurgia</option>
                <option value="Pediatria">Pediatria</option>
                <option value="Ginecologia">Ginecologia</option>
              </select>
            </div>

            <div class="form-group">
              <label>Data do Estudo</label>
              <input 
                v-model="formData.date" 
                type="date" 
                class="form-control"
                :max="today"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label>Duração do Estudo</label>
            <div class="duration-inputs">
              <input 
                v-model.number="formData.hours" 
                type="number" 
                min="0" 
                max="12"
                class="form-control"
                placeholder="0"
              />
              <span>horas</span>
              <input 
                v-model.number="formData.minutes" 
                type="number" 
                min="0" 
                max="59"
                class="form-control"
                placeholder="0"
              />
              <span>minutos</span>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>Avaliação do Conteúdo</h3>
          
          <div class="form-group">
            <label>Grau de Dificuldade</label>
            <div class="difficulty-selector">
              <button 
                v-for="level in ['Fácil', 'Difícil']" 
                :key="level"
                type="button"
                @click="formData.difficulty = level"
                :class="{ active: formData.difficulty === level }"
                class="difficulty-btn"
              >
                <i :class="getDifficultyIcon(level)"></i>
                {{ level }}
              </button>
            </div>
            <p class="help-text">
              <i class="fas fa-info-circle"></i>
              {{ difficultyHelpText }}
            </p>
          </div>

          <div class="form-group">
            <label>Tipo de Estudo</label>
            <div class="study-type-selector">
              <label v-for="type in studyTypes" :key="type.value" class="study-type">
                <input 
                  v-model="formData.studyType" 
                  type="radio" 
                  :value="type.value"
                  required
                />
                <span class="type-card">
                  <i :class="type.icon"></i>
                  <span class="type-name">{{ type.label }}</span>
                </span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>Anotações (opcional)</label>
            <textarea 
              v-model="formData.notes" 
              class="form-control" 
              rows="3"
              placeholder="Principais conceitos, dúvidas, observações..."
            ></textarea>
          </div>
        </div>

        <div class="first-contact-preview">
          <h3>
            <i class="fas fa-calendar-check"></i>
            Previsão do Primeiro Contato
          </h3>
          <div class="preview-content">
            <div class="preview-info">
              <span class="preview-label">Data do primeiro contato com questões:</span>
              <span class="preview-date">{{ firstContactDate }}</span>
            </div>
            <p class="preview-explanation">
              Baseado no grau de dificuldade {{ formData.difficulty || 'selecionado' }}, 
              suas questões serão agendadas para {{ daysUntilContact }} após o estudo teórico.
            </p>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary" :disabled="!isFormValid">
            <i class="fas fa-save"></i>
            Registrar Estudo
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'TheoryStudyModal',
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const formData = ref({
      title: '',
      subject: '',
      date: new Date().toISOString().split('T')[0],
      hours: 0,
      minutes: 0,
      difficulty: '',
      studyType: 'reading',
      notes: ''
    });

    const studyTypes = [
      { value: 'reading', label: 'Leitura', icon: 'fas fa-book-open' },
      { value: 'video', label: 'Videoaula', icon: 'fas fa-video' },
      { value: 'notes', label: 'Anotações', icon: 'fas fa-pen' },
      { value: 'mindmap', label: 'Mapa Mental', icon: 'fas fa-project-diagram' }
    ];

    const today = computed(() => new Date().toISOString().split('T')[0]);

    const difficultyHelpText = computed(() => {
      if (formData.value.difficulty === 'Fácil') {
        return 'Conteúdo familiar ou de fácil compreensão - Primeiro contato em 2 dias';
      } else if (formData.value.difficulty === 'Difícil') {
        return 'Conteúdo complexo ou novo - Primeiro contato em 1 dia';
      }
      return 'Selecione o grau de dificuldade para ver a recomendação';
    });

    const daysUntilContact = computed(() => {
      return formData.value.difficulty === 'Fácil' ? '2 dias' : '1 dia';
    });

    const firstContactDate = computed(() => {
      if (!formData.value.difficulty || !formData.value.date) return '---';
      
      const date = new Date(formData.value.date);
      const daysToAdd = formData.value.difficulty === 'Fácil' ? 2 : 1;
      date.setDate(date.getDate() + daysToAdd);
      
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    });

    const isFormValid = computed(() => {
      return formData.value.title &&
             formData.value.subject &&
             formData.value.date &&
             formData.value.difficulty &&
             formData.value.studyType &&
             (formData.value.hours > 0 || formData.value.minutes > 0);
    });

    const getDifficultyIcon = (level) => {
      return level === 'Fácil' ? 'fas fa-smile' : 'fas fa-brain';
    };

    const handleSubmit = () => {
      if (!isFormValid.value) return;

      const study = {
        ...formData.value,
        duration: formData.value.hours * 60 + formData.value.minutes,
        date: new Date(formData.value.date)
      };

      emit('save', study);
    };

    return {
      formData,
      studyTypes,
      today,
      difficultyHelpText,
      daysUntilContact,
      firstContactDate,
      isFormValid,
      getDifficultyIcon,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s;
}

.theory-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-title i {
  font-size: 1.5rem;
  color: #6366f1;
}

.modal-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 2rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.duration-inputs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.duration-inputs .form-control {
  width: 80px;
}

.duration-inputs span {
  color: #94a3b8;
  font-size: 0.875rem;
}

.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.difficulty-btn {
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.difficulty-btn i {
  font-size: 1.5rem;
}

.difficulty-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.difficulty-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.help-text {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  font-size: 0.875rem;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-text i {
  color: #6366f1;
}

.study-type-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.study-type {
  cursor: pointer;
}

.study-type input {
  display: none;
}

.type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  transition: all 0.2s;
}

.type-card i {
  font-size: 1.5rem;
  color: #94a3b8;
}

.type-name {
  font-size: 0.875rem;
  color: #94a3b8;
}

.study-type input:checked + .type-card {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
}

.study-type input:checked + .type-card i,
.study-type input:checked + .type-card .type-name {
  color: #6366f1;
}

.first-contact-preview {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
}

.first-contact-preview h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.preview-date {
  font-size: 1rem;
  font-weight: 600;
  color: #3b82f6;
}

.preview-explanation {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.3);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.theory-modal::-webkit-scrollbar {
  width: 8px;
}

.theory-modal::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.theory-modal::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.theory-modal::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>