<template>
  <div class="question-block-system">
    <!-- Background Effects -->
    <div class="ultra-background">
      <div class="neural-network"></div>
      <div class="data-flow"></div>
    </div>

    <!-- Header -->
    <header class="system-header">
      <div class="header-content">
        <div class="study-info">
          <h2>{{ currentBlock.topic }}</h2>
          <p>{{ currentBlock.subject }} - <PERSON><PERSON> de {{ currentBlock.questionCount }} questões</p>
        </div>

        <div class="timer-section">
          <div class="timer" :class="{ warning: timeRemaining < 300, danger: timeRemaining < 60 }">
            <i class="fas fa-clock"></i>
            <span>{{ formatTime(timeRemaining) }}</span>
          </div>
          <button @click="toggleTimer" class="timer-control">
            <i :class="timerActive ? 'fas fa-pause' : 'fas fa-play'"></i>
          </button>
        </div>

        <div class="progress-info">
          <div class="question-counter">
            <span class="current">{{ currentQuestionIndex + 1 }}</span>
            <span class="separator">/</span>
            <span class="total">{{ currentBlock.questionCount }}</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
        </div>
      </div>
    </header>

    <!-- Question Content -->
    <main class="question-content">
      <transition name="question-slide" mode="out-in">
        <div :key="currentQuestionIndex" class="question-card">
          <div class="question-header">
            <span class="question-type">
              <i :class="getQuestionTypeIcon(currentQuestion.type)"></i>
              {{ currentQuestion.type }}
            </span>
            <span class="difficulty" :class="currentQuestion.difficulty">
              {{ currentQuestion.difficulty }}
            </span>
          </div>

          <div class="question-text">
            <p>{{ currentQuestion.text }}</p>
            <div v-if="currentQuestion.image" class="question-image">
              <img :src="currentQuestion.image" alt="Question illustration">
            </div>
          </div>

          <div class="answer-options">
            <div v-for="(option, index) in currentQuestion.options" 
                 :key="index"
                 @click="selectAnswer(index)"
                 class="option"
                 :class="getOptionClass(index)">
              <span class="option-letter">{{ String.fromCharCode(65 + index) }}</span>
              <span class="option-text">{{ option }}</span>
              <div class="option-feedback">
                <i v-if="showFeedback && index === correctAnswer" class="fas fa-check"></i>
                <i v-else-if="showFeedback && index === selectedAnswer && index !== correctAnswer" class="fas fa-times"></i>
              </div>
            </div>
          </div>

          <div v-if="showExplanation" class="explanation">
            <h3><i class="fas fa-lightbulb"></i> Explicação</h3>
            <p>{{ currentQuestion.explanation }}</p>
            <div v-if="currentQuestion.references" class="references">
              <h4>Referências:</h4>
              <ul>
                <li v-for="ref in currentQuestion.references" :key="ref">{{ ref }}</li>
              </ul>
            </div>
          </div>
        </div>
      </transition>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button v-if="!answered" @click="skipQuestion" class="btn-secondary">
          <i class="fas fa-forward"></i>
          Pular
        </button>
        
        <button v-if="answered && !isLastQuestion" @click="nextQuestion" class="btn-primary">
          Próxima
          <i class="fas fa-arrow-right"></i>
        </button>
        
        <button v-if="answered && isLastQuestion" @click="finishBlock" class="btn-success">
          <i class="fas fa-check-circle"></i>
          Finalizar Bloco
        </button>
      </div>
    </main>

    <!-- Real-time Stats Sidebar -->
    <aside class="stats-sidebar" :class="{ expanded: showStats }">
      <button @click="showStats = !showStats" class="stats-toggle">
        <i class="fas fa-chart-line"></i>
      </button>

      <div class="stats-content">
        <h3>Performance em Tempo Real</h3>
        
        <div class="stat-item">
          <span class="stat-label">Taxa de Acerto</span>
          <div class="stat-value">
            <span class="percentage">{{ currentPerformance }}%</span>
            <div class="mini-chart">
              <canvas ref="performanceChart"></canvas>
            </div>
          </div>
        </div>

        <div class="stat-item">
          <span class="stat-label">Tempo Médio</span>
          <span class="stat-value">{{ averageTime }}s</span>
        </div>

        <div class="stat-item">
          <span class="stat-label">Questões</span>
          <div class="question-stats">
            <span class="correct">{{ correctCount }}</span>
            <span class="separator">/</span>
            <span class="incorrect">{{ incorrectCount }}</span>
            <span class="separator">/</span>
            <span class="skipped">{{ skippedCount }}</span>
          </div>
        </div>

        <div class="performance-prediction">
          <h4>Previsão de Retenção</h4>
          <div class="retention-bar">
            <div class="retention-fill" :style="{ width: predictedRetention + '%' }"></div>
          </div>
          <p>{{ retentionMessage }}</p>
        </div>
      </div>
    </aside>

    <!-- Results Modal -->
    <teleport to="body">
      <transition name="modal-fade">
        <div v-if="showResults" class="results-modal-backdrop">
          <div class="results-modal">
            <div class="results-header">
              <h2>Resultados do Bloco</h2>
              <div class="final-score" :class="getScoreClass(finalScore)">
                {{ finalScore }}%
              </div>
            </div>

            <div class="results-body">
              <div class="results-summary">
                <div class="summary-item">
                  <i class="fas fa-check-circle"></i>
                  <span class="value">{{ correctCount }}</span>
                  <span class="label">Acertos</span>
                </div>
                <div class="summary-item">
                  <i class="fas fa-times-circle"></i>
                  <span class="value">{{ incorrectCount }}</span>
                  <span class="label">Erros</span>
                </div>
                <div class="summary-item">
                  <i class="fas fa-clock"></i>
                  <span class="value">{{ formatTime(totalTime) }}</span>
                  <span class="label">Tempo Total</span>
                </div>
              </div>

              <div class="performance-analysis">
                <h3>Análise de Desempenho</h3>
                <div class="analysis-content">
                  <div class="analysis-level" :class="performanceAnalysis.level">
                    <i :class="getAnalysisIcon(performanceAnalysis.level)"></i>
                    <span>{{ getAnalysisLabel(performanceAnalysis.level) }}</span>
                  </div>
                  <p class="recommendation">{{ performanceAnalysis.recommendation }}</p>
                  
                  <div class="next-revision">
                    <h4>Próxima Revisão</h4>
                    <div class="revision-info">
                      <i class="fas fa-calendar-alt"></i>
                      <span>{{ formatDate(nextRevisionDate) }}</span>
                      <span class="interval">(em {{ revisionInterval }} dias)</span>
                    </div>
                  </div>

                  <div class="study-suggestion">
                    <h4>Sugestão de Estudo</h4>
                    <p>{{ performanceAnalysis.nextAction }}</p>
                    <div class="suggested-time">
                      <i class="fas fa-hourglass-half"></i>
                      <span>Tempo recomendado: {{ performanceAnalysis.suggestedStudyTime }} minutos</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="topics-analysis" v-if="topicBreakdown.length > 0">
                <h3>Desempenho por Tópico</h3>
                <div class="topics-list">
                  <div v-for="topic in topicBreakdown" :key="topic.name" 
                       class="topic-item">
                    <span class="topic-name">{{ topic.name }}</span>
                    <div class="topic-bar">
                      <div class="topic-fill" 
                           :style="{ width: topic.percentage + '%', background: getTopicColor(topic.percentage) }">
                      </div>
                    </div>
                    <span class="topic-percentage">{{ topic.percentage }}%</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="results-actions">
              <button @click="reviewMistakes" class="btn-secondary">
                <i class="fas fa-eye"></i>
                Revisar Erros
              </button>
              <button @click="saveAndContinue" class="btn-primary">
                <i class="fas fa-save"></i>
                Salvar e Continuar
              </button>
            </div>
          </div>
        </div>
      </transition>
    </teleport>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';
import { 
  analyzePerformance, 
  calculateNextRevisionDate,
  calculateRevisionInterval 
} from '@/utils/spacedRepetitionAlgorithm';

export default {
  name: 'QuestionBlockSystem',
  props: {
    block: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const store = useStore();
    
    // State
    const currentBlock = ref(props.block);
    const currentQuestionIndex = ref(0);
    const selectedAnswer = ref(null);
    const answered = ref(false);
    const showFeedback = ref(false);
    const showExplanation = ref(false);
    const showStats = ref(false);
    const showResults = ref(false);
    
    // Timer
    const timeRemaining = ref(currentBlock.value.timeLimit * 60);
    const timerActive = ref(true);
    const questionStartTime = ref(Date.now());
    const totalTime = ref(0);
    
    // Performance tracking
    const answers = ref([]);
    const correctCount = ref(0);
    const incorrectCount = ref(0);
    const skippedCount = ref(0);
    
    // Chart
    let performanceChart = null;
    const performanceHistory = ref([]);
    
    // Mock questions (in real app, load from API)
    const questions = ref([
      {
        id: 1,
        type: 'Múltipla Escolha',
        difficulty: 'medium',
        text: 'Qual é o mecanismo de ação dos antibióticos beta-lactâmicos?',
        options: [
          'Inibição da síntese de DNA',
          'Inibição da síntese da parede celular',
          'Inibição da síntese proteica',
          'Alteração da permeabilidade da membrana'
        ],
        correctAnswer: 1,
        explanation: 'Os antibióticos beta-lactâmicos inibem a síntese da parede celular bacteriana ao se ligarem às proteínas ligadoras de penicilina (PBPs), impedindo a formação de ligações cruzadas no peptidoglicano.',
        references: ['Goodman & Gilman, 13ª ed.', 'Rang & Dale Farmacologia, 8ª ed.']
      },
      // Add more questions...
    ]);
    
    // Computed
    const currentQuestion = computed(() => questions.value[currentQuestionIndex.value]);
    const correctAnswer = computed(() => currentQuestion.value?.correctAnswer);
    const isLastQuestion = computed(() => currentQuestionIndex.value === questions.value.length - 1);
    
    const progressPercentage = computed(() => {
      return Math.round(((currentQuestionIndex.value + 1) / currentBlock.value.questionCount) * 100);
    });
    
    const currentPerformance = computed(() => {
      const total = correctCount.value + incorrectCount.value;
      if (total === 0) return 0;
      return Math.round((correctCount.value / total) * 100);
    });
    
    const averageTime = computed(() => {
      if (answers.value.length === 0) return 0;
      const totalAnswerTime = answers.value.reduce((sum, a) => sum + a.timeSpent, 0);
      return Math.round(totalAnswerTime / answers.value.length);
    });
    
    const predictedRetention = computed(() => {
      const analysis = analyzePerformance(currentPerformance.value);
      return analysis.retentionProbability;
    });
    
    const retentionMessage = computed(() => {
      if (predictedRetention.value >= 80) return 'Excelente retenção esperada!';
      if (predictedRetention.value >= 60) return 'Boa retenção esperada';
      if (predictedRetention.value >= 40) return 'Retenção moderada - mais prática recomendada';
      return 'Baixa retenção - revisar conceitos básicos';
    });
    
    const finalScore = computed(() => {
      const total = questions.value.length;
      return Math.round((correctCount.value / total) * 100);
    });
    
    const performanceAnalysis = computed(() => {
      return analyzePerformance(finalScore.value, 1);
    });
    
    const revisionInterval = computed(() => {
      return calculateRevisionInterval(finalScore.value);
    });
    
    const nextRevisionDate = computed(() => {
      return calculateNextRevisionDate(new Date(), finalScore.value);
    });
    
    const topicBreakdown = computed(() => {
      // Group questions by topic and calculate performance
      // Mock data for demo
      return [
        { name: 'Farmacocinética', percentage: 85, correct: 17, total: 20 },
        { name: 'Farmacodinâmica', percentage: 70, correct: 7, total: 10 },
        { name: 'Interações Medicamentosas', percentage: 60, correct: 6, total: 10 }
      ];
    });
    
    // Methods
    const selectAnswer = (index) => {
      if (answered.value) return;
      
      selectedAnswer.value = index;
      answered.value = true;
      showFeedback.value = true;
      
      const timeSpent = Math.round((Date.now() - questionStartTime.value) / 1000);
      const isCorrect = index === correctAnswer.value;
      
      answers.value.push({
        questionId: currentQuestion.value.id,
        selectedAnswer: index,
        correct: isCorrect,
        timeSpent
      });
      
      if (isCorrect) {
        correctCount.value++;
      } else {
        incorrectCount.value++;
      }
      
      // Update performance history for chart
      performanceHistory.value.push(currentPerformance.value);
      updatePerformanceChart();
      
      // Show explanation after a delay
      setTimeout(() => {
        showExplanation.value = true;
      }, 500);
    };
    
    const skipQuestion = () => {
      skippedCount.value++;
      answers.value.push({
        questionId: currentQuestion.value.id,
        selectedAnswer: null,
        correct: false,
        skipped: true,
        timeSpent: Math.round((Date.now() - questionStartTime.value) / 1000)
      });
      
      nextQuestion();
    };
    
    const nextQuestion = () => {
      if (currentQuestionIndex.value < questions.value.length - 1) {
        currentQuestionIndex.value++;
        resetQuestion();
      }
    };
    
    const resetQuestion = () => {
      selectedAnswer.value = null;
      answered.value = false;
      showFeedback.value = false;
      showExplanation.value = false;
      questionStartTime.value = Date.now();
    };
    
    const finishBlock = () => {
      timerActive.value = false;
      totalTime.value = currentBlock.value.timeLimit * 60 - timeRemaining.value;
      showResults.value = true;
    };
    
    const toggleTimer = () => {
      timerActive.value = !timerActive.value;
    };
    
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    
    const formatDate = (date) => {
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    
    const getOptionClass = (index) => {
      if (!showFeedback.value) {
        return { selected: selectedAnswer.value === index };
      }
      
      if (index === correctAnswer.value) {
        return { correct: true };
      }
      
      if (index === selectedAnswer.value && index !== correctAnswer.value) {
        return { incorrect: true };
      }
      
      return {};
    };
    
    const getQuestionTypeIcon = (type) => {
      const icons = {
        'Múltipla Escolha': 'fas fa-list-ul',
        'Verdadeiro/Falso': 'fas fa-check-double',
        'Associação': 'fas fa-link',
        'Caso Clínico': 'fas fa-stethoscope'
      };
      return icons[type] || 'fas fa-question';
    };
    
    const getScoreClass = (score) => {
      if (score >= 80) return 'excellent';
      if (score >= 70) return 'good';
      if (score >= 60) return 'average';
      return 'poor';
    };
    
    const getAnalysisIcon = (level) => {
      const icons = {
        excellent: 'fas fa-trophy',
        good: 'fas fa-thumbs-up',
        medium: 'fas fa-exclamation-triangle',
        low: 'fas fa-exclamation-circle',
        critical: 'fas fa-times-circle'
      };
      return icons[level];
    };
    
    const getAnalysisLabel = (level) => {
      const labels = {
        excellent: 'Excelente Desempenho!',
        good: 'Bom Desempenho',
        medium: 'Desempenho Médio',
        low: 'Desempenho Baixo',
        critical: 'Atenção Necessária'
      };
      return labels[level];
    };
    
    const getTopicColor = (percentage) => {
      if (percentage >= 80) return '#10b981';
      if (percentage >= 60) return '#3b82f6';
      if (percentage >= 40) return '#f59e0b';
      return '#ef4444';
    };
    
    const updatePerformanceChart = () => {
      if (!performanceChart) return;
      
      performanceChart.data.labels = performanceHistory.value.map((_, i) => i + 1);
      performanceChart.data.datasets[0].data = performanceHistory.value;
      performanceChart.update();
    };
    
    const initPerformanceChart = () => {
      const ctx = document.querySelector('.mini-chart canvas')?.getContext('2d');
      if (!ctx) return;
      
      performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [{
            data: [],
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4,
            pointRadius: 2,
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: { display: false },
            y: { 
              display: false,
              min: 0,
              max: 100
            }
          }
        }
      });
    };
    
    const reviewMistakes = () => {
      // Navigate to mistakes review
      emit('review-mistakes', answers.value.filter(a => !a.correct));
    };
    
    const saveAndContinue = () => {
      // Save performance to store
      const performance = {
        id: Date.now(),
        blockId: currentBlock.value.id,
        subject: currentBlock.value.subject,
        topic: currentBlock.value.topic,
        date: new Date(),
        percentage: finalScore.value,
        totalQuestions: questions.value.length,
        correctAnswers: correctCount.value,
        timeSpent: totalTime.value,
        answers: answers.value
      };
      
      store.dispatch('revisions/addPerformance', performance);
      
      // Schedule next revision
      const nextRevision = {
        id: Date.now() + 1,
        title: `Revisão - ${currentBlock.value.topic}`,
        subject: currentBlock.value.subject,
        date: nextRevisionDate.value,
        type: 'revision',
        priority: finalScore.value < 70 ? 'high' : 'medium',
        blockId: currentBlock.value.id,
        cycleNumber: 2
      };
      
      store.dispatch('revisions/addSpacedRevision', nextRevision);
      
      emit('complete', {
        performance,
        nextRevision
      });
    };
    
    // Timer
    let timerInterval;
    
    const startTimer = () => {
      timerInterval = setInterval(() => {
        if (timerActive.value && timeRemaining.value > 0) {
          timeRemaining.value--;
        } else if (timeRemaining.value === 0) {
          finishBlock();
        }
      }, 1000);
    };
    
    // Lifecycle
    onMounted(() => {
      startTimer();
      setTimeout(initPerformanceChart, 100);
    });
    
    onUnmounted(() => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
      if (performanceChart) {
        performanceChart.destroy();
      }
    });
    
    // Watch for question changes
    watch(currentQuestionIndex, () => {
      if (showStats.value) {
        setTimeout(updatePerformanceChart, 100);
      }
    });
    
    return {
      // State
      currentBlock,
      currentQuestionIndex,
      currentQuestion,
      selectedAnswer,
      answered,
      showFeedback,
      showExplanation,
      showStats,
      showResults,
      timeRemaining,
      timerActive,
      
      // Performance
      correctCount,
      incorrectCount,
      skippedCount,
      answers,
      performanceHistory,
      
      // Computed
      correctAnswer,
      isLastQuestion,
      progressPercentage,
      currentPerformance,
      averageTime,
      predictedRetention,
      retentionMessage,
      finalScore,
      performanceAnalysis,
      revisionInterval,
      nextRevisionDate,
      topicBreakdown,
      
      // Methods
      selectAnswer,
      skipQuestion,
      nextQuestion,
      finishBlock,
      toggleTimer,
      formatTime,
      formatDate,
      getOptionClass,
      getQuestionTypeIcon,
      getScoreClass,
      getAnalysisIcon,
      getAnalysisLabel,
      getTopicColor,
      reviewMistakes,
      saveAndContinue
    };
  }
};
</script>

<style scoped>
/* Ultra Design System */
.question-block-system {
  min-height: 100vh;
  background: #0a0e1a;
  color: #e4e6eb;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Background Effects */
.ultra-background {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.neural-network {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  animation: pulse 8s ease-in-out infinite;
}

.data-flow {
  position: absolute;
  inset: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(99, 102, 241, 0.03) 10px,
    rgba(99, 102, 241, 0.03) 20px
  );
  animation: flow 20s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes flow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20px, 20px); }
}

/* Header */
.system-header {
  position: relative;
  z-index: 10;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 1.5rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.study-info h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #e4e6eb;
}

.study-info p {
  margin: 0.25rem 0 0;
  color: #94a3b8;
}

/* Timer Section */
.timer-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #10b981;
  padding: 0.75rem 1.5rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  transition: all 0.3s;
}

.timer.warning {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.timer.danger {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  animation: timer-pulse 1s ease-in-out infinite;
}

@keyframes timer-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.timer-control {
  width: 48px;
  height: 48px;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  color: #6366f1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.timer-control:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: scale(1.1);
}

/* Progress Info */
.progress-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.question-counter {
  font-size: 1.125rem;
  font-weight: 600;
}

.question-counter .current {
  color: #6366f1;
  font-size: 1.5rem;
}

.question-counter .separator {
  color: #64748b;
  margin: 0 0.25rem;
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Question Content */
.question-content {
  flex: 1;
  position: relative;
  z-index: 1;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  padding: 2rem;
}

.question-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.question-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty.easy {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.difficulty.medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.difficulty.hard {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Question Text */
.question-text {
  margin-bottom: 2rem;
}

.question-text p {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #e4e6eb;
  margin: 0;
}

.question-image {
  margin-top: 1rem;
  border-radius: 12px;
  overflow: hidden;
}

.question-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Answer Options */
.answer-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.option:hover:not(.correct):not(.incorrect) {
  background: rgba(99, 102, 241, 0.05);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateX(4px);
}

.option.selected {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
}

.option.correct {
  background: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
  cursor: default;
}

.option.incorrect {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  cursor: default;
}

.option-letter {
  width: 32px;
  height: 32px;
  background: rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6366f1;
  flex-shrink: 0;
}

.option.correct .option-letter {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.option.incorrect .option-letter {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.option-text {
  flex: 1;
  color: #e4e6eb;
}

.option-feedback {
  position: absolute;
  right: 1.5rem;
  font-size: 1.25rem;
}

.option-feedback .fa-check {
  color: #10b981;
}

.option-feedback .fa-times {
  color: #ef4444;
}

/* Explanation */
.explanation {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  animation: fadeIn 0.5s ease;
}

.explanation h3 {
  margin: 0 0 1rem;
  font-size: 1.125rem;
  color: #6366f1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.explanation p {
  margin: 0;
  line-height: 1.6;
  color: #cbd5e1;
}

.references {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.references h4 {
  margin: 0 0 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.references ul {
  margin: 0;
  padding-left: 1.5rem;
}

.references li {
  color: #64748b;
  font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn-primary,
.btn-secondary,
.btn-success {
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #e4e6eb;
}

.btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

/* Stats Sidebar */
.stats-sidebar {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px 0 0 20px;
  width: 60px;
  transition: all 0.3s;
  z-index: 100;
}

.stats-sidebar.expanded {
  width: 320px;
}

.stats-toggle {
  position: absolute;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 10px;
  color: #6366f1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.stats-toggle:hover {
  background: rgba(99, 102, 241, 0.2);
}

.stats-content {
  padding: 80px 20px 20px;
  opacity: 0;
  transition: opacity 0.3s;
}

.stats-sidebar.expanded .stats-content {
  opacity: 1;
}

.stats-content h3 {
  margin: 0 0 1.5rem;
  font-size: 1.125rem;
  color: #e4e6eb;
}

.stat-item {
  margin-bottom: 1.5rem;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6eb;
}

.percentage {
  color: #6366f1;
}

.mini-chart {
  margin-top: 0.5rem;
  height: 60px;
}

.question-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.question-stats .correct {
  color: #10b981;
}

.question-stats .incorrect {
  color: #ef4444;
}

.question-stats .skipped {
  color: #64748b;
}

.performance-prediction {
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 12px;
}

.performance-prediction h4 {
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.retention-bar {
  height: 12px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.retention-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 6px;
  transition: width 0.5s ease;
}

.performance-prediction p {
  margin: 0;
  font-size: 0.75rem;
  color: #64748b;
}

/* Results Modal */
.results-modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
}

.results-modal {
  background: #0f1419;
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 24px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.results-header {
  padding: 2rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h2 {
  margin: 0;
  font-size: 1.75rem;
  color: #e4e6eb;
}

.final-score {
  font-size: 3rem;
  font-weight: 800;
  padding: 1rem 2rem;
  border-radius: 16px;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid;
}

.final-score.excellent {
  color: #10b981;
  border-color: #10b981;
}

.final-score.good {
  color: #3b82f6;
  border-color: #3b82f6;
}

.final-score.average {
  color: #f59e0b;
  border-color: #f59e0b;
}

.final-score.poor {
  color: #ef4444;
  border-color: #ef4444;
}

/* Results Body */
.results-body {
  padding: 2rem;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 16px;
}

.summary-item i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.summary-item .fa-check-circle {
  color: #10b981;
}

.summary-item .fa-times-circle {
  color: #ef4444;
}

.summary-item .fa-clock {
  color: #6366f1;
}

.summary-item .value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #e4e6eb;
  margin: 0.5rem 0;
}

.summary-item .label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Performance Analysis */
.performance-analysis {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.performance-analysis h3 {
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  color: #e4e6eb;
}

.analysis-level {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  font-weight: 600;
  margin-bottom: 1rem;
}

.analysis-level.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.analysis-level.good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.analysis-level.medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.analysis-level.low,
.analysis-level.critical {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.recommendation {
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.next-revision,
.study-suggestion {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.next-revision h4,
.study-suggestion h4 {
  margin: 0 0 0.75rem;
  font-size: 1rem;
  color: #94a3b8;
}

.revision-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  color: #e4e6eb;
}

.revision-info .interval {
  font-size: 0.875rem;
  color: #64748b;
}

.suggested-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #cbd5e1;
}

/* Topics Analysis */
.topics-analysis {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
}

.topics-analysis h3 {
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  color: #e4e6eb;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topic-item {
  display: grid;
  grid-template-columns: 150px 1fr 60px;
  align-items: center;
  gap: 1rem;
}

.topic-name {
  font-weight: 500;
  color: #cbd5e1;
}

.topic-bar {
  height: 12px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.topic-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.5s ease;
}

.topic-percentage {
  text-align: right;
  font-weight: 600;
  color: #e4e6eb;
}

/* Results Actions */
.results-actions {
  padding: 1.5rem 2rem;
  background: rgba(30, 41, 59, 0.3);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* Animations */
.question-slide-enter-active,
.question-slide-leave-active {
  transition: all 0.3s ease;
}

.question-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.question-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .timer-section {
    justify-content: center;
  }
  
  .progress-info {
    align-items: center;
  }
  
  .stats-sidebar {
    display: none;
  }
  
  .results-summary {
    grid-template-columns: 1fr;
  }
  
  .topic-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .topic-bar {
    margin: 0.5rem 0;
  }
}
</style>