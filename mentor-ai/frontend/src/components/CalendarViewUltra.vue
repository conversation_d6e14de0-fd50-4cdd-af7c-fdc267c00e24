<template>
  <div class="calendar-ultra">
    <!-- Ultra Background Effects -->
    <div class="ultra-background">
      <div class="particle-field"></div>
      <div class="gradient-mesh"></div>
      <div class="floating-orbs">
        <div class="orb" v-for="i in 5" :key="i"></div>
      </div>
      <div class="grid-pattern"></div>
    </div>

    <!-- Ultra Header -->
    <header class="ultra-header">
      <div class="header-container">
        <div class="header-brand">
          <div class="brand-icon">
            <i class="fas fa-calendar-alt"></i>
            <div class="icon-pulse"></div>
          </div>
          <div class="brand-info">
            <h1 class="brand-title">Calendário Inteligente de Revisões</h1>
            <p class="brand-subtitle">
              <i class="fas fa-sync-alt"></i>
              Sincronizado com Sistema de Revisões Espaçadas
            </p>
          </div>
        </div>

        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-value">{{ todayRevisions }}</span>
            <span class="stat-label">Hoje</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ weekRevisions }}</span>
            <span class="stat-label">Esta Semana</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ completionRate }}%</span>
            <span class="stat-label">Conclusão</span>
          </div>
        </div>

        <div class="header-actions">
          <button @click="openUltraModal" class="ultra-action-btn primary">
            <div class="btn-bg"></div>
            <i class="fas fa-plus-circle"></i>
            <span>Registrar Estudo / Nova Revisão</span>
            <span class="btn-badge" v-if="pendingCount > 0">{{ pendingCount }}</span>
          </button>
          
          <button @click="syncWithRevisions" class="ultra-action-btn secondary">
            <i class="fas fa-sync"></i>
            <span>Sincronizar</span>
          </button>
        </div>
      </div>

      <!-- Navigation Bar -->
      <div class="navigation-bar">
        <div class="date-navigation">
          <button @click="previousPeriod" class="nav-btn">
            <i class="fas fa-chevron-left"></i>
          </button>
          <div class="current-period">
            <i class="far fa-calendar"></i>
            {{ formattedCurrentPeriod }}
          </div>
          <button @click="nextPeriod" class="nav-btn">
            <i class="fas fa-chevron-right"></i>
          </button>
          <button @click="goToToday" class="today-btn">
            <i class="fas fa-calendar-day"></i>
            Hoje
          </button>
        </div>

        <div class="view-switcher">
          <button v-for="view in viewOptions" :key="view.id"
                  @click="currentView = view.id"
                  class="view-btn"
                  :class="{ active: currentView === view.id }">
            <i :class="view.icon"></i>
            {{ view.label }}
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="calendar-main">
      <!-- Month View -->
      <transition name="view-transition">
        <div v-if="currentView === 'month'" class="month-view">
          <div class="calendar-grid">
            <div class="weekday-headers">
              <div v-for="day in weekDays" :key="day" class="weekday-header">
                {{ day }}
              </div>
            </div>
            
            <div class="days-grid">
              <div v-for="(day, index) in calendarDays" :key="index" 
                   class="calendar-day"
                   :class="getDayClasses(day)"
                   @click="selectDay(day)">
                <div class="day-header">
                  <span class="day-number">{{ day.date.getDate() }}</span>
                  <div v-if="day.revisions.length > 0" class="revision-count">
                    {{ day.revisions.length }}
                  </div>
                </div>
                
                <div class="day-content">
                  <div v-for="(revision, idx) in day.revisions.slice(0, 3)" 
                       :key="revision.id"
                       class="revision-preview"
                       :class="`priority-${revision.priority || 'medium'}`"
                       @click.stop="showRevisionDetail(revision)">
                    <span class="revision-time">{{ formatTime(revision.date) }}</span>
                    <span class="revision-title">{{ revision.title }}</span>
                  </div>
                  
                  <div v-if="day.revisions.length > 3" class="more-indicator">
                    +{{ day.revisions.length - 3 }} mais
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- Week View -->
      <transition name="view-transition">
        <div v-if="currentView === 'week'" class="week-view">
          <div class="week-grid">
            <div class="time-column">
              <div v-for="hour in hours" :key="hour" class="time-slot">
                {{ hour }}:00
              </div>
            </div>
            
            <div class="days-columns">
              <div v-for="day in weekDaysData" :key="day.date" class="day-column">
                <div class="day-header">
                  <span class="day-name">{{ formatDayName(day.date) }}</span>
                  <span class="day-date">{{ day.date.getDate() }}</span>
                </div>
                
                <div class="events-container">
                  <div v-for="revision in day.revisions" :key="revision.id"
                       class="week-event"
                       :class="`priority-${revision.priority || 'medium'}`"
                       :style="getEventStyle(revision)"
                       @click="showRevisionDetail(revision)">
                    <div class="event-time">{{ formatTime(revision.date) }}</div>
                    <div class="event-title">{{ revision.title }}</div>
                    <div class="event-subject">{{ revision.subject }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- List View -->
      <transition name="view-transition">
        <div v-if="currentView === 'list'" class="list-view">
          <div class="list-filters">
            <button v-for="filter in listFilters" :key="filter.id"
                    @click="activeFilter = filter.id"
                    class="filter-btn"
                    :class="{ active: activeFilter === filter.id }">
              <i :class="filter.icon"></i>
              {{ filter.label }}
            </button>
          </div>
          
          <div class="revisions-list">
            <div v-for="group in groupedRevisions" :key="group.date" class="revision-group">
              <h3 class="group-header">
                <i class="fas fa-calendar-day"></i>
                {{ formatGroupDate(group.date) }}
                <span class="group-count">{{ group.items.length }} revisões</span>
              </h3>
              
              <div class="group-items">
                <div v-for="revision in group.items" :key="revision.id"
                     class="list-item"
                     :class="`priority-${revision.priority || 'medium'}`"
                     @click="showRevisionDetail(revision)">
                  <div class="item-time">
                    <i class="fas fa-clock"></i>
                    {{ formatTime(revision.date) }}
                  </div>
                  
                  <div class="item-content">
                    <h4 class="item-title">{{ revision.title }}</h4>
                    <div class="item-meta">
                      <span class="item-subject">
                        <i class="fas fa-book"></i>
                        {{ revision.subject }}
                      </span>
                      <span class="item-type">
                        <i :class="getTypeIcon(revision.type)"></i>
                        {{ revision.type }}
                      </span>
                    </div>
                  </div>
                  
                  <div class="item-actions">
                    <button @click.stop="markComplete(revision)" class="action-btn complete">
                      <i class="fas fa-check"></i>
                    </button>
                    <button @click.stop="editRevision(revision)" class="action-btn edit">
                      <i class="fas fa-edit"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </main>

    <!-- Sidebar Detail -->
    <transition name="sidebar-slide">
      <div v-if="selectedRevision" class="sidebar-detail">
        <div class="sidebar-header">
          <h2>Detalhes da Revisão</h2>
          <button @click="selectedRevision = null" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="sidebar-content">
          <div class="detail-section">
            <h3>{{ selectedRevision.title }}</h3>
            <div class="detail-meta">
              <span class="meta-item">
                <i class="fas fa-calendar"></i>
                {{ formatDate(selectedRevision.date) }}
              </span>
              <span class="meta-item">
                <i class="fas fa-clock"></i>
                {{ formatTime(selectedRevision.date) }}
              </span>
              <span class="meta-item priority" :class="`priority-${selectedRevision.priority}`">
                <i class="fas fa-flag"></i>
                {{ selectedRevision.priority }}
              </span>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>Informações</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Matéria</label>
                <span>{{ selectedRevision.subject }}</span>
              </div>
              <div class="info-item">
                <label>Tipo</label>
                <span>{{ selectedRevision.type }}</span>
              </div>
              <div class="info-item">
                <label>Ciclo</label>
                <span>{{ selectedRevision.cycleNumber || 1 }}º ciclo</span>
              </div>
              <div class="info-item">
                <label>Status</label>
                <span :class="{ completed: selectedRevision.completed }">
                  {{ selectedRevision.completed ? 'Concluída' : 'Pendente' }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="detail-section" v-if="selectedRevision.notes">
            <h4>Anotações</h4>
            <p>{{ selectedRevision.notes }}</p>
          </div>
          
          <div class="detail-actions">
            <button @click="startRevision(selectedRevision)" class="detail-btn primary">
              <i class="fas fa-play"></i>
              Iniciar Revisão
            </button>
            <button @click="editRevision(selectedRevision)" class="detail-btn secondary">
              <i class="fas fa-edit"></i>
              Editar
            </button>
            <button @click="deleteRevision(selectedRevision)" class="detail-btn danger">
              <i class="fas fa-trash"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Ultra Modal -->
    <teleport to="body">
      <transition name="modal-fade">
        <div v-if="showModal" class="ultra-modal-backdrop" @click.self="closeModal">
          <div class="ultra-modal-container">
            <NewRevisionModalUltra
              @close="closeModal"
              @save="handleSaveRevision"
              @saveStudy="handleSaveStudy"
            />
          </div>
        </div>
      </transition>
    </teleport>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <transition-group name="toast">
        <div v-for="toast in toasts" :key="toast.id" 
             class="toast" :class="`toast-${toast.type}`">
          <i :class="toast.icon"></i>
          <span>{{ toast.message }}</span>
        </div>
      </transition-group>
    </div>

    <!-- Question Block System Modal -->
    <teleport to="body">
      <transition name="modal-fade">
        <div v-if="showQuestionBlock" class="ultra-modal-backdrop" @click.self="closeQuestionBlock">
          <div class="ultra-modal-container">
            <QuestionBlockSystem
              :subject="currentRevisionForQuestions?.subject"
              :topic="currentRevisionForQuestions?.title"
              :revisionId="currentRevisionForQuestions?.id"
              @close="closeQuestionBlock"
              @complete="handleQuestionBlockComplete"
            />
          </div>
        </div>
      </transition>
    </teleport>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import NewRevisionModalUltra from './NewRevisionModalUltra.vue';
import QuestionBlockSystem from './QuestionBlockSystem.vue';

export default {
  name: 'CalendarViewUltra',
  components: {
    NewRevisionModalUltra,
    QuestionBlockSystem
  },
  setup() {
    const store = useStore();
    
    // State
    const currentDate = ref(new Date());
    const currentView = ref('month');
    const showModal = ref(false);
    const showQuestionBlock = ref(false);
    const currentRevisionForQuestions = ref(null);
    const selectedRevision = ref(null);
    const activeFilter = ref('all');
    const toasts = ref([]);
    
    // Load data from store
    onMounted(() => {
      store.dispatch('revisions/loadRevisions');
    });
    
    // Computed - Revisions from store
    const allRevisions = computed(() => {
      const spacedRevisions = store.getters['revisions/getSpacedRevisions'] || [];
      const firstContacts = store.getters['revisions/getFirstContacts'] || [];
      
      // Combine and normalize all revisions
      return [...spacedRevisions, ...firstContacts].map(rev => ({
        ...rev,
        date: new Date(rev.date),
        priority: rev.priority || 'medium',
        type: rev.type || 'revision'
      }));
    });
    
    // Stats
    const todayRevisions = computed(() => {
      const today = new Date().toDateString();
      return allRevisions.value.filter(r => 
        r.date.toDateString() === today && !r.completed
      ).length;
    });
    
    const weekRevisions = computed(() => {
      const startOfWeek = new Date();
      startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(endOfWeek.getDate() + 6);
      
      return allRevisions.value.filter(r => 
        r.date >= startOfWeek && r.date <= endOfWeek && !r.completed
      ).length;
    });
    
    const completionRate = computed(() => {
      if (allRevisions.value.length === 0) return 100;
      const completed = allRevisions.value.filter(r => r.completed).length;
      return Math.round((completed / allRevisions.value.length) * 100);
    });
    
    const pendingCount = computed(() => {
      return allRevisions.value.filter(r => !r.completed).length;
    });
    
    // Calendar data
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    
    const viewOptions = [
      { id: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
      { id: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
      { id: 'list', label: 'Lista', icon: 'fas fa-list' }
    ];
    
    const listFilters = [
      { id: 'all', label: 'Todas', icon: 'fas fa-infinity' },
      { id: 'today', label: 'Hoje', icon: 'fas fa-calendar-day' },
      { id: 'week', label: 'Esta Semana', icon: 'fas fa-calendar-week' },
      { id: 'pending', label: 'Pendentes', icon: 'fas fa-clock' }
    ];
    
    const formattedCurrentPeriod = computed(() => {
      const options = { month: 'long', year: 'numeric' };
      if (currentView.value === 'week') {
        const start = new Date(currentDate.value);
        start.setDate(start.getDate() - start.getDay());
        const end = new Date(start);
        end.setDate(end.getDate() + 6);
        return `${start.getDate()} - ${end.getDate()} de ${start.toLocaleDateString('pt-BR', options)}`;
      }
      return currentDate.value.toLocaleDateString('pt-BR', options);
    });
    
    const calendarDays = computed(() => {
      const year = currentDate.value.getFullYear();
      const month = currentDate.value.getMonth();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());
      
      const days = [];
      const endDate = new Date(lastDay);
      endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dayDate = new Date(d);
        const dayRevisions = allRevisions.value.filter(r => 
          r.date.toDateString() === dayDate.toDateString()
        );
        
        days.push({
          date: dayDate,
          isCurrentMonth: dayDate.getMonth() === month,
          isToday: dayDate.toDateString() === new Date().toDateString(),
          revisions: dayRevisions
        });
      }
      
      return days;
    });
    
    const weekDaysData = computed(() => {
      const days = [];
      const start = new Date(currentDate.value);
      start.setDate(start.getDate() - start.getDay());
      
      for (let i = 0; i < 7; i++) {
        const dayDate = new Date(start);
        dayDate.setDate(start.getDate() + i);
        
        const dayRevisions = allRevisions.value.filter(r => 
          r.date.toDateString() === dayDate.toDateString()
        );
        
        days.push({
          date: dayDate,
          revisions: dayRevisions
        });
      }
      
      return days;
    });
    
    const groupedRevisions = computed(() => {
      let filtered = allRevisions.value;
      
      if (activeFilter.value === 'today') {
        const today = new Date().toDateString();
        filtered = filtered.filter(r => r.date.toDateString() === today);
      } else if (activeFilter.value === 'week') {
        const startOfWeek = new Date();
        startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(endOfWeek.getDate() + 6);
        filtered = filtered.filter(r => r.date >= startOfWeek && r.date <= endOfWeek);
      } else if (activeFilter.value === 'pending') {
        filtered = filtered.filter(r => !r.completed);
      }
      
      // Group by date
      const groups = {};
      filtered.forEach(revision => {
        const dateKey = revision.date.toDateString();
        if (!groups[dateKey]) {
          groups[dateKey] = {
            date: dateKey,
            items: []
          };
        }
        groups[dateKey].items.push(revision);
      });
      
      // Sort groups by date and items by time
      return Object.values(groups)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .map(group => ({
          ...group,
          items: group.items.sort((a, b) => a.date - b.date)
        }));
    });
    
    const hours = Array.from({ length: 24 }, (_, i) => i);
    
    // Methods
    const openUltraModal = () => {
      showModal.value = true;
    };
    
    const closeModal = () => {
      showModal.value = false;
    };
    
    const handleSaveStudy = (studyData) => {
      // Calculate first contact date
      const daysToAdd = studyData.difficulty === 'easy' ? 2 : 1;
      const firstContactDate = new Date(studyData.date);
      firstContactDate.setDate(firstContactDate.getDate() + daysToAdd);
      
      // Save to store
      const study = {
        ...studyData,
        id: Date.now(),
        date: new Date(studyData.date)
      };
      
      store.dispatch('revisions/addTheoryStudy', study);
      
      // Create first contact
      const firstContact = {
        id: Date.now() + 1,
        title: `Questões - ${studyData.title}`,
        subject: studyData.subject,
        date: firstContactDate,
        type: 'questions',
        priority: 'high',
        studyId: study.id
      };
      
      store.dispatch('revisions/addFirstContact', firstContact);
      
      closeModal();
      showToast('success', `Estudo registrado! Primeiro contato em ${firstContactDate.toLocaleDateString('pt-BR')}`);
    };
    
    const handleSaveRevision = (revisionData) => {
      const revision = {
        ...revisionData,
        id: Date.now(),
        date: new Date(`${revisionData.date} ${revisionData.time || '09:00'}`),
        completed: false
      };
      
      store.dispatch('revisions/addSpacedRevision', revision);
      
      closeModal();
      showToast('success', 'Revisão agendada com sucesso!');
    };
    
    const syncWithRevisions = () => {
      store.dispatch('revisions/loadRevisions');
      showToast('info', 'Calendário sincronizado com sucesso!');
    };
    
    const previousPeriod = () => {
      if (currentView.value === 'month') {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1);
      } else if (currentView.value === 'week') {
        currentDate.value = new Date(currentDate.value.getTime() - 7 * 24 * 60 * 60 * 1000);
      }
    };
    
    const nextPeriod = () => {
      if (currentView.value === 'month') {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1);
      } else if (currentView.value === 'week') {
        currentDate.value = new Date(currentDate.value.getTime() + 7 * 24 * 60 * 60 * 1000);
      }
    };
    
    const goToToday = () => {
      currentDate.value = new Date();
    };
    
    const selectDay = (day) => {
      if (day.revisions.length === 0) {
        // Open modal to create new revision for this day
        showModal.value = true;
      } else if (day.revisions.length === 1) {
        selectedRevision.value = day.revisions[0];
      } else {
        // Show day's revisions in list view
        currentView.value = 'list';
        activeFilter.value = 'all';
      }
    };
    
    const showRevisionDetail = (revision) => {
      selectedRevision.value = revision;
    };
    
    const markComplete = (revision) => {
      if (revision.type === 'questions') {
        store.dispatch('revisions/completeFirstContact', revision.id);
      } else {
        store.dispatch('revisions/completeRevision', revision.id);
      }
      showToast('success', 'Revisão marcada como concluída!');
    };
    
    const editRevision = (revision) => {
      // TODO: Implement edit functionality
      showToast('info', 'Função de edição em desenvolvimento');
    };
    
    const deleteRevision = (revision) => {
      if (confirm('Tem certeza que deseja excluir esta revisão?')) {
        // TODO: Implement delete functionality
        showToast('warning', 'Revisão excluída');
        selectedRevision.value = null;
      }
    };
    
    const startRevision = (revision) => {
      showToast('info', 'Iniciando revisão...');
      
      // Se for uma revisão de questões, abrir o sistema de blocos
      if (revision.revisionType === 'Prática' || revision.revisionType === 'Questões' || revision.type === 'questions') {
        showQuestionBlock.value = true;
        currentRevisionForQuestions.value = revision;
        selectedRevision.value = null; // Fechar modal de detalhes
      } else {
        // Para outros tipos de revisão
        showToast('warning', 'Sistema de revisão em desenvolvimento');
      }
    };
    
    const closeQuestionBlock = () => {
      showQuestionBlock.value = false;
      currentRevisionForQuestions.value = null;
    };
    
    const handleQuestionBlockComplete = (result) => {
      console.log('📊 Question Block completed:', result);
      
      // Atualizar a revisão como concluída
      const revision = currentRevisionForQuestions.value;
      if (revision) {
        // Marcar como concluída no store
        store.dispatch('revisions/completeRevision', {
          id: revision.id,
          performance: result.percentage
        });
        
        // Salvar o desempenho
        store.dispatch('revisions/addPerformance', {
          id: Date.now(),
          subject: revision.subject,
          topic: revision.title,
          date: new Date(),
          percentage: result.percentage,
          totalQuestions: result.totalQuestions,
          correctAnswers: result.correctAnswers,
          timeSpent: result.totalTime,
          revisionId: revision.id
        });
        
        // Mostrar notificação de sucesso
        if (result.percentage >= 80) {
          showToast('success', 
            `Excelente! ${result.percentage}% de acerto. Próxima revisão em ${result.nextRevisionDays} dias.`
          );
        } else if (result.percentage >= 60) {
          showToast('info', 
            `Bom trabalho! ${result.percentage}% de acerto. Continue praticando!`
          );
        } else {
          showToast('warning', 
            `${result.percentage}% de acerto. Recomenda-se revisar o conteúdo teórico.`
          );
        }
        
        // Recarregar dados
        loadCalendarData();
      }
      
      closeQuestionBlock();
    };

    // Helper functions
    const getDayClasses = (day) => {
      return {
        'other-month': !day.isCurrentMonth,
        'today': day.isToday,
        'has-revisions': day.revisions.length > 0,
        'weekend': day.date.getDay() === 0 || day.date.getDay() === 6
      };
    };
    
    const formatTime = (date) => {
      return new Date(date).toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    };
    
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    
    const formatDayName = (date) => {
      return date.toLocaleDateString('pt-BR', { weekday: 'short' });
    };
    
    const formatGroupDate = (dateString) => {
      const date = new Date(dateString);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (date.toDateString() === today.toDateString()) {
        return 'Hoje';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Ontem';
      } else {
        return date.toLocaleDateString('pt-BR', {
          weekday: 'long',
          day: 'numeric',
          month: 'long'
        });
      }
    };
    
    const getEventStyle = (revision) => {
      const startHour = revision.date.getHours();
      const minutes = revision.date.getMinutes();
      const top = (startHour + minutes / 60) * 60; // 60px per hour
      
      return {
        top: `${top}px`,
        height: '50px' // Default 1 hour height
      };
    };
    
    const getTypeIcon = (type) => {
      const icons = {
        questions: 'fas fa-question-circle',
        summary: 'fas fa-file-alt',
        flashcards: 'fas fa-layer-group',
        practice: 'fas fa-stethoscope'
      };
      return icons[type] || 'fas fa-book';
    };
    
    const showToast = (type, message) => {
      const toast = {
        id: Date.now(),
        type,
        message,
        icon: type === 'success' ? 'fas fa-check-circle' : 
              type === 'warning' ? 'fas fa-exclamation-triangle' :
              type === 'error' ? 'fas fa-times-circle' : 'fas fa-info-circle'
      };
      
      toasts.value.push(toast);
      
      setTimeout(() => {
        const index = toasts.value.findIndex(t => t.id === toast.id);
        if (index > -1) {
          toasts.value.splice(index, 1);
        }
      }, 3000);
    };
    
    // Watch for changes in revisions store
    watch(() => store.state.revisions.spacedRevisions, () => {
      // Force reactivity update when store changes
    }, { deep: true });
    
    return {
      // State
      currentDate,
      currentView,
      showModal,
      showQuestionBlock,
      currentRevisionForQuestions,
      selectedRevision,
      activeFilter,
      toasts,
      
      // Computed
      allRevisions,
      todayRevisions,
      weekRevisions,
      completionRate,
      pendingCount,
      weekDays,
      viewOptions,
      listFilters,
      formattedCurrentPeriod,
      calendarDays,
      weekDaysData,
      groupedRevisions,
      hours,
      
      // Methods
      openUltraModal,
      closeModal,
      handleSaveStudy,
      handleSaveRevision,
      syncWithRevisions,
      previousPeriod,
      nextPeriod,
      goToToday,
      selectDay,
      showRevisionDetail,
      markComplete,
      editRevision,
      deleteRevision,
      startRevision,
      closeQuestionBlock,
      handleQuestionBlockComplete,
      getDayClasses,
      formatTime,
      formatDate,
      formatDayName,
      formatGroupDate,
      getEventStyle,
      getTypeIcon
    };
  }
};
</script>

<style scoped>
/* Ultra Design System */
:root {
  --ultra-bg: #0a0e1a;
  --ultra-surface: #141925;
  --ultra-surface-light: #1e2433;
  --ultra-border: rgba(148, 163, 184, 0.1);
  --ultra-text: #e4e6eb;
  --ultra-text-secondary: #94a3b8;
  --ultra-primary: #6366f1;
  --ultra-primary-light: #818cf8;
  --ultra-success: #10b981;
  --ultra-warning: #f59e0b;
  --ultra-danger: #ef4444;
  --ultra-gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Base Layout */
.calendar-ultra {
  min-height: 100vh;
  background: var(--ultra-bg);
  color: var(--ultra-text);
  position: relative;
  overflow-x: hidden;
}

/* Ultra Background */
.ultra-background {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.particle-field {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.gradient-mesh {
  position: absolute;
  inset: 0;
  background: 
    linear-gradient(180deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
}

.floating-orbs {
  position: absolute;
  inset: 0;
}

.orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
  animation: float-orb 20s infinite;
}

.orb:nth-child(1) {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-duration: 25s;
}

.orb:nth-child(2) {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-duration: 30s;
  animation-delay: -5s;
}

.orb:nth-child(3) {
  width: 250px;
  height: 250px;
  bottom: 20%;
  left: 30%;
  animation-duration: 22s;
  animation-delay: -10s;
}

.orb:nth-child(4) {
  width: 150px;
  height: 150px;
  top: 30%;
  right: 40%;
  animation-duration: 28s;
  animation-delay: -15s;
}

.orb:nth-child(5) {
  width: 180px;
  height: 180px;
  bottom: 40%;
  right: 20%;
  animation-duration: 24s;
  animation-delay: -7s;
}

@keyframes float-orb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translate(30px, -50px) scale(1.1);
    opacity: 0.4;
  }
  50% {
    transform: translate(-20px, 30px) scale(0.9);
    opacity: 0.2;
  }
  75% {
    transform: translate(40px, 20px) scale(1.05);
    opacity: 0.35;
  }
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 60px 60px;
}

/* Ultra Header */
.ultra-header {
  position: relative;
  z-index: 10;
  background: linear-gradient(180deg, rgba(15, 23, 42, 0.8) 0%, rgba(15, 23, 42, 0.6) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ultra-border);
}

.header-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.brand-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: var(--ultra-gradient);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

.icon-pulse {
  position: absolute;
  inset: -4px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 24px;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0; }
}

.brand-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #e4e6eb 0%, #cbd5e1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.brand-subtitle {
  margin: 0.25rem 0 0;
  color: var(--ultra-text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Header Stats */
.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ultra-primary);
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
  margin-top: 0.25rem;
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 1rem;
}

.ultra-action-btn {
  position: relative;
  padding: 1rem 2rem;
  background: transparent;
  border: 2px solid var(--ultra-primary);
  border-radius: 16px;
  color: var(--ultra-text);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  overflow: hidden;
  transition: all 0.3s;
}

.btn-bg {
  position: absolute;
  inset: 0;
  background: var(--ultra-gradient);
  opacity: 0;
  transition: opacity 0.3s;
}

.ultra-action-btn:hover .btn-bg {
  opacity: 1;
}

.ultra-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(99, 102, 241, 0.4);
  border-color: transparent;
}

.ultra-action-btn.primary {
  border-color: transparent;
  background: var(--ultra-gradient);
  color: white;
}

.ultra-action-btn.secondary {
  border-color: var(--ultra-border);
  background: rgba(30, 41, 59, 0.5);
}

.ultra-action-btn i,
.ultra-action-btn span {
  position: relative;
  z-index: 1;
}

.btn-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--ultra-danger);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 24px;
  text-align: center;
}

/* Navigation Bar */
.navigation-bar {
  padding: 1.5rem 2rem;
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn,
.today-btn {
  width: 40px;
  height: 40px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  color: var(--ultra-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.today-btn {
  width: auto;
  padding: 0 1rem;
  gap: 0.5rem;
}

.nav-btn:hover,
.today-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
  color: var(--ultra-primary);
}

.current-period {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ultra-text);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-switcher {
  display: flex;
  gap: 0.5rem;
  background: rgba(30, 41, 59, 0.3);
  padding: 0.25rem;
  border-radius: 12px;
}

.view-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--ultra-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  font-weight: 500;
}

.view-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--ultra-text);
}

.view-btn.active {
  background: var(--ultra-gradient);
  color: white;
}

/* Main Content */
.calendar-main {
  position: relative;
  z-index: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

/* Month View */
.month-view {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid var(--ultra-border);
  border-radius: 24px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.calendar-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.weekday-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.weekday-header {
  text-align: center;
  font-weight: 600;
  color: var(--ultra-text-secondary);
  padding: 0.75rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  min-height: 100px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.calendar-day:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.calendar-day.other-month {
  opacity: 0.5;
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
}

.calendar-day.today::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--ultra-gradient);
}

.calendar-day.weekend {
  background: rgba(148, 163, 184, 0.05);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.day-number {
  font-weight: 600;
  color: var(--ultra-text);
}

.revision-count {
  background: var(--ultra-gradient);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 10px;
}

.day-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.revision-preview {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.2s;
}

.revision-preview:hover {
  background: rgba(99, 102, 241, 0.2);
}

.revision-preview.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #ff6b6b;
}

.revision-preview.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #ffa94d;
}

.revision-preview.priority-low {
  background: rgba(16, 185, 129, 0.1);
  color: #51cf66;
}

.revision-time {
  font-weight: 600;
  margin-right: 0.5rem;
}

.more-indicator {
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
  text-align: center;
  margin-top: 0.25rem;
}

/* Week View */
.week-view {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid var(--ultra-border);
  border-radius: 24px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.week-grid {
  display: flex;
  gap: 1rem;
  height: 1440px; /* 24 hours * 60px */
  overflow-y: auto;
}

.time-column {
  width: 60px;
  flex-shrink: 0;
}

.time-slot {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
  border-bottom: 1px solid var(--ultra-border);
}

.days-columns {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.day-column {
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  overflow: hidden;
}

.day-column .day-header {
  background: rgba(99, 102, 241, 0.05);
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid var(--ultra-border);
}

.day-name {
  display: block;
  font-weight: 600;
  color: var(--ultra-text);
  margin-bottom: 0.25rem;
}

.day-date {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--ultra-primary);
}

.events-container {
  position: relative;
  height: calc(100% - 60px);
}

.week-event {
  position: absolute;
  left: 4px;
  right: 4px;
  background: rgba(99, 102, 241, 0.2);
  border: 1px solid var(--ultra-primary);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
}

.week-event:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  z-index: 10;
}

.week-event.priority-high {
  background: rgba(239, 68, 68, 0.2);
  border-color: var(--ultra-danger);
}

.week-event.priority-medium {
  background: rgba(245, 158, 11, 0.2);
  border-color: var(--ultra-warning);
}

.event-time {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ultra-text);
}

.event-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--ultra-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-subject {
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
}

/* List View */
.list-view {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid var(--ultra-border);
  border-radius: 24px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.list-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 8px;
  color: var(--ultra-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.filter-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
  color: var(--ultra-text);
}

.filter-btn.active {
  background: var(--ultra-gradient);
  border-color: transparent;
  color: white;
}

.revisions-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.revision-group {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 16px;
  padding: 1.5rem;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ultra-text);
}

.group-count {
  margin-left: auto;
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
  font-weight: 400;
}

.group-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.list-item {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.list-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateX(4px);
}

.list-item.priority-high {
  border-left: 4px solid var(--ultra-danger);
}

.list-item.priority-medium {
  border-left: 4px solid var(--ultra-warning);
}

.list-item.priority-low {
  border-left: 4px solid var(--ultra-success);
}

.item-time {
  color: var(--ultra-text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ultra-text);
  margin: 0 0 0.25rem;
}

.item-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

.item-subject,
.item-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  border-radius: 8px;
  color: var(--ultra-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.action-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--ultra-primary);
  color: var(--ultra-primary);
}

.action-btn.complete:hover {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--ultra-success);
  color: var(--ultra-success);
}

/* Sidebar Detail */
.sidebar-detail {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  background: var(--ultra-surface);
  border-left: 1px solid var(--ultra-border);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid var(--ultra-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--ultra-text);
}

.close-btn {
  width: 36px;
  height: 36px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: var(--ultra-danger);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: rotate(90deg);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  color: var(--ultra-text);
}

.detail-section h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--ultra-text-secondary);
}

.detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--ultra-text-secondary);
}

.meta-item.priority {
  padding: 0.25rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 6px;
  font-weight: 500;
}

.meta-item.priority.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--ultra-danger);
}

.meta-item.priority.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--ultra-warning);
}

.meta-item.priority.priority-low {
  background: rgba(16, 185, 129, 0.1);
  color: var(--ultra-success);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.info-item {
  background: rgba(30, 41, 59, 0.3);
  padding: 1rem;
  border-radius: 8px;
}

.info-item label {
  display: block;
  font-size: 0.75rem;
  color: var(--ultra-text-secondary);
  margin-bottom: 0.25rem;
}

.info-item span {
  display: block;
  font-size: 0.875rem;
  color: var(--ultra-text);
  font-weight: 500;
}

.info-item span.completed {
  color: var(--ultra-success);
}

.detail-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1.5rem;
}

.detail-btn {
  padding: 0.875rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.detail-btn.primary {
  background: var(--ultra-gradient);
  color: white;
}

.detail-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.detail-btn.secondary {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--ultra-border);
  color: var(--ultra-text);
}

.detail-btn.secondary:hover {
  background: rgba(30, 41, 59, 0.7);
}

.detail-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--ultra-danger);
}

.detail-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Modal */
.ultra-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999999;
  padding: 2rem;
}

.ultra-modal-container {
  position: relative;
  z-index: 10000000;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: auto;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 999999;
}

.toast {
  padding: 1rem 1.5rem;
  background: var(--ultra-surface);
  border: 1px solid var(--ultra-border);
  border-radius: 12px;
  color: var(--ultra-text);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  min-width: 300px;
}

.toast-success {
  border-color: rgba(16, 185, 129, 0.5);
  background: rgba(16, 185, 129, 0.1);
}

.toast-success i {
  color: var(--ultra-success);
}

.toast-warning {
  border-color: rgba(245, 158, 11, 0.5);
  background: rgba(245, 158, 11, 0.1);
}

.toast-warning i {
  color: var(--ultra-warning);
}

.toast-error {
  border-color: rgba(239, 68, 68, 0.5);
  background: rgba(239, 68, 68, 0.1);
}

.toast-error i {
  color: var(--ultra-danger);
}

.toast-info {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.toast-info i {
  color: #3b82f6;
}

/* Transitions */
.view-transition-enter-active,
.view-transition-leave-active {
  transition: all 0.3s ease;
}

.view-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.view-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.sidebar-slide-enter-active,
.sidebar-slide-leave-active {
  transition: all 0.3s ease;
}

.sidebar-slide-enter-from,
.sidebar-slide-leave-to {
  transform: translateX(100%);
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* Responsive */
@media (max-width: 1200px) {
  .header-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-stats {
    order: -1;
    justify-content: space-around;
  }
  
  .sidebar-detail {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .navigation-bar {
    flex-direction: column;
  }
  
  .days-grid {
    grid-template-columns: repeat(7, 1fr);
    font-size: 0.8rem;
  }
  
  .calendar-day {
    min-height: 80px;
    padding: 0.5rem;
  }
  
  .revision-preview {
    font-size: 0.625rem;
  }
  
  .week-grid {
    height: auto;
  }
  
  .days-columns {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Modal backdrop styles for Question Block */
.ultra-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999999;
  padding: 2rem;
}

.ultra-modal-container {
  position: relative;
  z-index: 10000000;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow: auto;
}
</style>