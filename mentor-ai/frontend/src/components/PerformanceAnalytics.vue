<template>
  <div class="performance-analytics">
    <!-- Header com Estatísticas Principais -->
    <div class="analytics-header">
      <div class="header-gradient"></div>
      <div class="header-content">
        <h2 class="analytics-title">
          <i class="fas fa-chart-line"></i>
          Central de Análise de Desempenho
        </h2>
        
        <!-- KPIs Principais -->
        <div class="kpi-cards">
          <div class="kpi-card" v-for="kpi in mainKPIs" :key="kpi.id">
            <div class="kpi-icon" :style="{ background: kpi.color }">
              <i :class="kpi.icon"></i>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">
                <span class="value">{{ kpi.value }}</span>
                <span class="unit">{{ kpi.unit }}</span>
              </div>
              <div class="kpi-label">{{ kpi.label }}</div>
              <div class="kpi-trend" :class="kpi.trend.type">
                <i :class="kpi.trend.icon"></i>
                {{ kpi.trend.value }}% vs mês anterior
              </div>
            </div>
            <div class="kpi-spark">
              <canvas :ref="`spark-${kpi.id}`"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros e Controles -->
    <div class="analytics-controls">
      <div class="period-selector">
        <button 
          v-for="period in periods" 
          :key="period.value"
          @click="selectedPeriod = period.value"
          :class="['period-btn', { active: selectedPeriod === period.value }]"
        >
          <i :class="period.icon"></i>
          {{ period.label }}
        </button>
      </div>
      
      <div class="filter-controls">
        <div class="filter-dropdown">
          <button @click="showSubjectFilter = !showSubjectFilter" class="filter-btn">
            <i class="fas fa-filter"></i>
            Matérias
            <i class="fas fa-chevron-down"></i>
          </button>
          <transition name="dropdown">
            <div v-if="showSubjectFilter" class="dropdown-menu">
              <label v-for="subject in availableSubjects" :key="subject" class="dropdown-item">
                <input 
                  type="checkbox" 
                  :value="subject" 
                  v-model="selectedSubjects"
                />
                <span>{{ subject }}</span>
              </label>
            </div>
          </transition>
        </div>
        
        <button @click="exportData" class="export-btn">
          <i class="fas fa-download"></i>
          Exportar
        </button>
      </div>
    </div>

    <!-- Dashboard de Gráficos -->
    <div class="charts-dashboard">
      <!-- Gráfico de Evolução Temporal -->
      <div class="chart-container large">
        <div class="chart-header">
          <h3>Evolução do Desempenho</h3>
          <div class="chart-options">
            <button 
              v-for="metric in evolutionMetrics" 
              :key="metric"
              @click="selectedEvolutionMetric = metric"
              :class="['metric-btn', { active: selectedEvolutionMetric === metric }]"
            >
              {{ metric }}
            </button>
          </div>
        </div>
        <div class="chart-body">
          <canvas ref="evolutionChart"></canvas>
        </div>
        <div class="chart-insights">
          <div class="insight" v-for="insight in evolutionInsights" :key="insight.id">
            <i :class="insight.icon" :style="{ color: insight.color }"></i>
            <span>{{ insight.text }}</span>
          </div>
        </div>
      </div>

      <!-- Heat Map de Atividades -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>Mapa de Calor - Atividades</h3>
          <span class="chart-subtitle">Últimos 365 dias</span>
        </div>
        <div class="chart-body">
          <div class="heatmap-container">
            <div class="heatmap-months">
              <span v-for="month in heatmapMonths" :key="month">{{ month }}</span>
            </div>
            <div class="heatmap-grid">
              <div class="heatmap-weekdays">
                <span v-for="day in weekdays" :key="day">{{ day }}</span>
              </div>
              <div class="heatmap-cells">
                <div 
                  v-for="(day, index) in heatmapData" 
                  :key="index"
                  class="heatmap-cell"
                  :class="getHeatmapClass(day.value)"
                  :title="`${day.date}: ${day.value} atividades`"
                  @click="showDayDetails(day)"
                ></div>
              </div>
            </div>
            <div class="heatmap-legend">
              <span>Menos</span>
              <div class="legend-cells">
                <div v-for="i in 5" :key="i" :class="`level-${i-1}`"></div>
              </div>
              <span>Mais</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Gráfico de Radar - Competências -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>Análise de Competências</h3>
          <button @click="toggleCompetencyView" class="view-toggle">
            <i :class="competencyView === 'radar' ? 'fas fa-chart-bar' : 'fas fa-chart-radar'"></i>
          </button>
        </div>
        <div class="chart-body">
          <canvas ref="competencyChart"></canvas>
        </div>
        <div class="competency-details">
          <div 
            v-for="competency in competencies" 
            :key="competency.name"
            class="competency-item"
            @click="selectCompetency(competency)"
            :class="{ selected: selectedCompetency?.name === competency.name }"
          >
            <div class="competency-bar">
              <div 
                class="competency-fill" 
                :style="{ 
                  width: competency.percentage + '%',
                  background: competency.color 
                }"
              ></div>
            </div>
            <div class="competency-info">
              <span class="competency-name">{{ competency.name }}</span>
              <span class="competency-value">{{ competency.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Análise de Tempo de Estudo -->
      <div class="chart-container small">
        <div class="chart-header">
          <h3>Distribuição de Tempo</h3>
        </div>
        <div class="chart-body">
          <canvas ref="timeDistributionChart"></canvas>
        </div>
        <div class="time-stats">
          <div class="time-stat">
            <i class="fas fa-clock"></i>
            <div>
              <span class="stat-value">{{ totalStudyTime }}h</span>
              <span class="stat-label">Total</span>
            </div>
          </div>
          <div class="time-stat">
            <i class="fas fa-calendar-day"></i>
            <div>
              <span class="stat-value">{{ avgDailyTime }}h</span>
              <span class="stat-label">Média/dia</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Taxa de Retenção -->
      <div class="chart-container small">
        <div class="chart-header">
          <h3>Curva de Esquecimento</h3>
        </div>
        <div class="chart-body">
          <canvas ref="forgettingCurveChart"></canvas>
        </div>
        <div class="retention-info">
          <div class="retention-stat">
            <span class="retention-label">Retenção após 24h:</span>
            <span class="retention-value">{{ retention24h }}%</span>
          </div>
          <div class="retention-stat">
            <span class="retention-label">Retenção após 7 dias:</span>
            <span class="retention-value">{{ retention7d }}%</span>
          </div>
        </div>
      </div>

      <!-- Performance por Tipo de Questão -->
      <div class="chart-container small">
        <div class="chart-header">
          <h3>Desempenho por Tipo</h3>
        </div>
        <div class="chart-body">
          <canvas ref="questionTypeChart"></canvas>
        </div>
        <div class="question-type-legend">
          <div 
            v-for="type in questionTypes" 
            :key="type.name"
            class="legend-item"
          >
            <span class="legend-color" :style="{ background: type.color }"></span>
            <span class="legend-label">{{ type.name }}</span>
            <span class="legend-value">{{ type.accuracy }}%</span>
          </div>
        </div>
      </div>

      <!-- Análise Preditiva -->
      <div class="chart-container large">
        <div class="chart-header">
          <h3>Análise Preditiva e Recomendações</h3>
          <div class="prediction-confidence">
            <i class="fas fa-robot"></i>
            Confiança: {{ predictionConfidence }}%
          </div>
        </div>
        <div class="prediction-content">
          <div class="prediction-chart">
            <canvas ref="predictionChart"></canvas>
          </div>
          <div class="prediction-insights">
            <h4>Projeções para os Próximos 30 Dias</h4>
            <div class="prediction-cards">
              <div 
                v-for="prediction in predictions" 
                :key="prediction.id"
                class="prediction-card"
                :class="prediction.type"
              >
                <div class="prediction-icon">
                  <i :class="prediction.icon"></i>
                </div>
                <div class="prediction-info">
                  <h5>{{ prediction.title }}</h5>
                  <p>{{ prediction.description }}</p>
                  <div class="prediction-action" v-if="prediction.action">
                    <button @click="prediction.action.handler" class="action-btn">
                      {{ prediction.action.label }}
                      <i class="fas fa-arrow-right"></i>
                    </button>
                  </div>
                </div>
                <div class="prediction-metric">
                  <span class="metric-value">{{ prediction.metric.value }}</span>
                  <span class="metric-label">{{ prediction.metric.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comparação com Pares -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>Comparação com Outros Estudantes</h3>
          <button @click="toggleAnonymous" class="anonymous-toggle">
            <i :class="isAnonymous ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            {{ isAnonymous ? 'Anônimo' : 'Identificado' }}
          </button>
        </div>
        <div class="chart-body">
          <canvas ref="comparisonChart"></canvas>
        </div>
        <div class="percentile-info">
          <div class="percentile-card">
            <div class="percentile-value">{{ userPercentile }}</div>
            <div class="percentile-label">Percentil</div>
            <div class="percentile-description">
              Você está entre os top {{ 100 - userPercentile }}% dos estudantes
            </div>
          </div>
        </div>
      </div>

      <!-- Metas e Progresso -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>Metas e Progresso</h3>
          <button @click="showGoalModal = true" class="add-goal-btn">
            <i class="fas fa-plus"></i>
            Nova Meta
          </button>
        </div>
        <div class="goals-content">
          <div 
            v-for="goal in goals" 
            :key="goal.id"
            class="goal-item"
            :class="{ completed: goal.progress >= 100 }"
          >
            <div class="goal-header">
              <div class="goal-info">
                <h4>{{ goal.title }}</h4>
                <p>{{ goal.description }}</p>
              </div>
              <div class="goal-deadline">
                <i class="fas fa-calendar-alt"></i>
                {{ formatDate(goal.deadline) }}
              </div>
            </div>
            <div class="goal-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ 
                    width: goal.progress + '%',
                    background: getGoalColor(goal.progress)
                  }"
                ></div>
              </div>
              <span class="progress-text">{{ goal.progress }}%</span>
            </div>
            <div class="goal-actions">
              <button @click="updateGoal(goal)" class="goal-action">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="deleteGoal(goal)" class="goal-action delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Detalhes do Dia -->
    <transition name="modal">
      <div v-if="selectedDay" class="modal-overlay" @click.self="selectedDay = null">
        <div class="day-details-modal">
          <div class="modal-header">
            <h3>{{ formatDate(selectedDay.date) }}</h3>
            <button @click="selectedDay = null" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="day-summary">
              <div class="summary-stat">
                <i class="fas fa-book"></i>
                <span>{{ selectedDay.studies }} estudos</span>
              </div>
              <div class="summary-stat">
                <i class="fas fa-question-circle"></i>
                <span>{{ selectedDay.questions }} questões</span>
              </div>
              <div class="summary-stat">
                <i class="fas fa-clock"></i>
                <span>{{ selectedDay.time }}h estudadas</span>
              </div>
            </div>
            <div class="day-activities">
              <h4>Atividades do Dia</h4>
              <div class="activity-timeline">
                <div 
                  v-for="activity in selectedDay.activities" 
                  :key="activity.id"
                  class="activity-item"
                >
                  <div class="activity-time">{{ activity.time }}</div>
                  <div class="activity-marker" :class="activity.type"></div>
                  <div class="activity-content">
                    <h5>{{ activity.title }}</h5>
                    <p>{{ activity.description }}</p>
                    <div class="activity-stats" v-if="activity.stats">
                      <span v-for="(value, key) in activity.stats" :key="key">
                        {{ key }}: {{ value }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Modal de Nova Meta -->
    <transition name="modal">
      <div v-if="showGoalModal" class="modal-overlay" @click.self="showGoalModal = false">
        <div class="goal-modal">
          <div class="modal-header">
            <h3>Criar Nova Meta</h3>
            <button @click="showGoalModal = false" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <form @submit.prevent="createGoal" class="goal-form">
            <div class="form-group">
              <label>Título da Meta</label>
              <input 
                v-model="newGoal.title" 
                type="text" 
                required
                placeholder="Ex: Completar 500 questões de Anatomia"
              />
            </div>
            <div class="form-group">
              <label>Descrição</label>
              <textarea 
                v-model="newGoal.description" 
                rows="3"
                placeholder="Descreva sua meta em detalhes..."
              ></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Tipo de Meta</label>
                <select v-model="newGoal.type" required>
                  <option value="questions">Questões</option>
                  <option value="study-time">Tempo de Estudo</option>
                  <option value="accuracy">Taxa de Acerto</option>
                  <option value="streak">Dias Consecutivos</option>
                </select>
              </div>
              <div class="form-group">
                <label>Valor Alvo</label>
                <input 
                  v-model.number="newGoal.target" 
                  type="number" 
                  required
                  min="1"
                />
              </div>
            </div>
            <div class="form-group">
              <label>Prazo</label>
              <input 
                v-model="newGoal.deadline" 
                type="date" 
                required
                :min="minDate"
              />
            </div>
            <div class="form-actions">
              <button type="button" @click="showGoalModal = false" class="btn-cancel">
                Cancelar
              </button>
              <button type="submit" class="btn-submit">
                Criar Meta
              </button>
            </div>
          </form>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

const store = useStore()
const emit = defineEmits(['close', 'update-data'])

// Estado
const selectedPeriod = ref('30d')
const selectedSubjects = ref([])
const showSubjectFilter = ref(false)
const selectedEvolutionMetric = ref('Taxa de Acerto')
const competencyView = ref('radar')
const selectedCompetency = ref(null)
const isAnonymous = ref(true)
const selectedDay = ref(null)
const showGoalModal = ref(false)

// Dados
const newGoal = ref({
  title: '',
  description: '',
  type: 'questions',
  target: 0,
  deadline: ''
})

// Chart refs
const evolutionChart = ref(null)
const competencyChart = ref(null)
const timeDistributionChart = ref(null)
const forgettingCurveChart = ref(null)
const questionTypeChart = ref(null)
const predictionChart = ref(null)
const comparisonChart = ref(null)

let charts = {}

// Configurações
const periods = [
  { value: '7d', label: '7 dias', icon: 'fas fa-calendar-week' },
  { value: '30d', label: '30 dias', icon: 'fas fa-calendar-alt' },
  { value: '90d', label: '3 meses', icon: 'fas fa-calendar' },
  { value: '365d', label: '1 ano', icon: 'fas fa-calendar-check' }
]

const evolutionMetrics = ['Taxa de Acerto', 'Questões Resolvidas', 'Tempo de Estudo', 'Revisões Completadas']

const weekdays = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']

// Computed
const mainKPIs = computed(() => [
  {
    id: 'accuracy',
    icon: 'fas fa-percentage',
    label: 'Taxa de Acerto Geral',
    value: 78,
    unit: '%',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: { type: 'positive', value: 5, icon: 'fas fa-arrow-up' }
  },
  {
    id: 'questions',
    icon: 'fas fa-question-circle',
    label: 'Questões Resolvidas',
    value: '2.4k',
    unit: '',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: { type: 'positive', value: 12, icon: 'fas fa-arrow-up' }
  },
  {
    id: 'time',
    icon: 'fas fa-clock',
    label: 'Horas de Estudo',
    value: 156,
    unit: 'h',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: { type: 'neutral', value: 0, icon: 'fas fa-minus' }
  },
  {
    id: 'streak',
    icon: 'fas fa-fire',
    label: 'Dias Consecutivos',
    value: 23,
    unit: '',
    color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    trend: { type: 'positive', value: 15, icon: 'fas fa-arrow-up' }
  }
])

const availableSubjects = computed(() => {
  return ['Anatomia', 'Fisiologia', 'Patologia', 'Farmacologia', 'Bioquímica', 'Microbiologia']
})

const heatmapMonths = computed(() => {
  const months = []
  const now = new Date()
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    months.push(date.toLocaleDateString('pt-BR', { month: 'short' }))
  }
  return months
})

const heatmapData = computed(() => {
  // Gerar dados mock para o heatmap
  const data = []
  const now = new Date()
  for (let i = 364; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(Math.random() * 10)
    })
  }
  return data
})

const competencies = computed(() => [
  { name: 'Anatomia', percentage: 85, color: '#667eea' },
  { name: 'Fisiologia', percentage: 72, color: '#764ba2' },
  { name: 'Patologia', percentage: 68, color: '#f093fb' },
  { name: 'Farmacologia', percentage: 45, color: '#f5576c' },
  { name: 'Bioquímica', percentage: 78, color: '#4facfe' },
  { name: 'Microbiologia', percentage: 60, color: '#00f2fe' }
])

const evolutionInsights = computed(() => [
  {
    id: 1,
    icon: 'fas fa-trending-up',
    color: '#10b981',
    text: 'Melhoria de 8% na última semana'
  },
  {
    id: 2,
    icon: 'fas fa-trophy',
    color: '#f59e0b',
    text: 'Melhor desempenho em Anatomia'
  },
  {
    id: 3,
    icon: 'fas fa-exclamation-triangle',
    color: '#ef4444',
    text: 'Farmacologia precisa de mais atenção'
  }
])

const totalStudyTime = computed(() => 156)
const avgDailyTime = computed(() => 2.3)
const retention24h = computed(() => 82)
const retention7d = computed(() => 65)

const questionTypes = computed(() => [
  { name: 'Múltipla Escolha', accuracy: 78, color: '#667eea' },
  { name: 'Verdadeiro/Falso', accuracy: 85, color: '#764ba2' },
  { name: 'Caso Clínico', accuracy: 62, color: '#f093fb' }
])

const predictionConfidence = computed(() => 87)

const predictions = computed(() => [
  {
    id: 1,
    type: 'success',
    icon: 'fas fa-chart-line',
    title: 'Taxa de Acerto Projetada',
    description: 'Com base no seu ritmo atual, você atingirá 85% de acerto geral',
    metric: { value: '85%', label: 'em 30 dias' },
    action: {
      label: 'Ver Plano',
      handler: () => console.log('Ver plano')
    }
  },
  {
    id: 2,
    type: 'warning',
    icon: 'fas fa-exclamation-triangle',
    title: 'Risco de Esquecimento',
    description: '45 conceitos precisam de revisão urgente para manter a retenção',
    metric: { value: '45', label: 'conceitos' },
    action: {
      label: 'Revisar Agora',
      handler: () => console.log('Revisar')
    }
  },
  {
    id: 3,
    type: 'info',
    icon: 'fas fa-lightbulb',
    title: 'Oportunidade de Melhoria',
    description: 'Aumente sessões de Farmacologia para equilibrar seu desempenho',
    metric: { value: '+20%', label: 'potencial' }
  }
])

const userPercentile = computed(() => 85)

const goals = ref([
  {
    id: 1,
    title: 'Resolver 1000 questões',
    description: 'Meta mensal de questões',
    progress: 72,
    deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000)
  },
  {
    id: 2,
    title: 'Atingir 90% em Anatomia',
    description: 'Melhorar desempenho na matéria',
    progress: 85,
    deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000)
  }
])

const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

// Methods
const getHeatmapClass = (value) => {
  if (value === 0) return 'level-0'
  if (value <= 2) return 'level-1'
  if (value <= 4) return 'level-2'
  if (value <= 6) return 'level-3'
  return 'level-4'
}

const showDayDetails = (day) => {
  selectedDay.value = {
    ...day,
    studies: Math.floor(Math.random() * 5) + 1,
    questions: Math.floor(Math.random() * 50) + 10,
    time: (Math.random() * 4 + 0.5).toFixed(1),
    activities: generateDayActivities()
  }
}

const generateDayActivities = () => {
  return [
    {
      id: 1,
      time: '09:30',
      type: 'study',
      title: 'Estudo Teórico - Anatomia',
      description: 'Sistema Cardiovascular',
      stats: { duração: '45min', páginas: 12 }
    },
    {
      id: 2,
      time: '14:15',
      type: 'questions',
      title: 'Sessão de Questões',
      description: '30 questões de Fisiologia',
      stats: { acertos: '24/30', tempo: '35min' }
    },
    {
      id: 3,
      time: '20:00',
      type: 'revision',
      title: 'Revisão Espaçada',
      description: 'Conceitos de Farmacologia',
      stats: { cartões: 45, retenção: '78%' }
    }
  ]
}

const toggleCompetencyView = () => {
  competencyView.value = competencyView.value === 'radar' ? 'bar' : 'radar'
  updateCompetencyChart()
}

const selectCompetency = (competency) => {
  selectedCompetency.value = competency
}

const exportData = () => {
  console.log('Exportar dados')
}

const toggleAnonymous = () => {
  isAnonymous.value = !isAnonymous.value
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('pt-BR')
}

const getGoalColor = (progress) => {
  if (progress >= 80) return '#10b981'
  if (progress >= 50) return '#f59e0b'
  return '#ef4444'
}

const createGoal = () => {
  goals.value.push({
    id: Date.now(),
    ...newGoal.value,
    progress: 0,
    deadline: new Date(newGoal.value.deadline)
  })
  showGoalModal.value = false
  newGoal.value = {
    title: '',
    description: '',
    type: 'questions',
    target: 0,
    deadline: ''
  }
}

const updateGoal = (goal) => {
  console.log('Atualizar meta:', goal)
}

const deleteGoal = (goal) => {
  const index = goals.value.findIndex(g => g.id === goal.id)
  if (index > -1) {
    goals.value.splice(index, 1)
  }
}

// Chart Functions
const createCharts = () => {
  // Sparklines para KPIs
  mainKPIs.value.forEach(kpi => {
    const canvas = document.querySelector(`canvas[ref="spark-${kpi.id}"]`)
    if (canvas) {
      charts[`spark-${kpi.id}`] = new Chart(canvas, {
        type: 'line',
        data: {
          labels: Array(7).fill(''),
          datasets: [{
            data: Array(7).fill(0).map(() => Math.random() * 20 + 70),
            borderColor: '#667eea',
            borderWidth: 2,
            fill: false,
            tension: 0.4,
            pointRadius: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: { enabled: false }
          },
          scales: {
            x: { display: false },
            y: { display: false }
          }
        }
      })
    }
  })

  // Gráfico de Evolução
  if (evolutionChart.value) {
    charts.evolution = new Chart(evolutionChart.value, {
      type: 'line',
      data: {
        labels: generateDateLabels(30),
        datasets: [
          {
            label: 'Taxa de Acerto',
            data: generateRandomData(30, 60, 85),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            fill: true,
            tension: 0.4
          },
          {
            label: 'Meta',
            data: Array(30).fill(80),
            borderColor: '#ef4444',
            borderDash: [5, 5],
            fill: false
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          y: {
            beginAtZero: false,
            min: 50,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }

  // Gráfico de Competências
  updateCompetencyChart()

  // Distribuição de Tempo
  if (timeDistributionChart.value) {
    charts.timeDistribution = new Chart(timeDistributionChart.value, {
      type: 'doughnut',
      data: {
        labels: ['Teoria', 'Questões', 'Revisão'],
        datasets: [{
          data: [45, 35, 20],
          backgroundColor: ['#667eea', '#764ba2', '#f093fb']
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    })
  }

  // Curva de Esquecimento
  if (forgettingCurveChart.value) {
    charts.forgettingCurve = new Chart(forgettingCurveChart.value, {
      type: 'line',
      data: {
        labels: ['Imediato', '20min', '1h', '24h', '2d', '7d', '30d'],
        datasets: [
          {
            label: 'Sem Revisão',
            data: [100, 58, 44, 36, 33, 25, 21],
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            fill: true,
            tension: 0.4
          },
          {
            label: 'Com Revisão',
            data: [100, 95, 92, 82, 78, 65, 55],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            fill: true,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }

  // Desempenho por Tipo
  if (questionTypeChart.value) {
    charts.questionType = new Chart(questionTypeChart.value, {
      type: 'polarArea',
      data: {
        labels: questionTypes.value.map(t => t.name),
        datasets: [{
          data: questionTypes.value.map(t => t.accuracy),
          backgroundColor: questionTypes.value.map(t => t.color + '80')
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            beginAtZero: true,
            max: 100
          }
        }
      }
    })
  }

  // Gráfico Preditivo
  if (predictionChart.value) {
    charts.prediction = new Chart(predictionChart.value, {
      type: 'line',
      data: {
        labels: [...generateDateLabels(30, -30), ...generateDateLabels(30)],
        datasets: [
          {
            label: 'Histórico',
            data: [...generateRandomData(30, 65, 78), ...Array(30).fill(null)],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            fill: true
          },
          {
            label: 'Projeção',
            data: [...Array(30).fill(null), ...generatePredictionData(30, 78, 85)],
            borderColor: '#10b981',
            borderDash: [5, 5],
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          annotation: {
            annotations: {
              line1: {
                type: 'line',
                xMin: 30,
                xMax: 30,
                borderColor: '#ef4444',
                borderWidth: 2,
                borderDash: [5, 5]
              }
            }
          }
        }
      }
    })
  }

  // Comparação com Pares
  if (comparisonChart.value) {
    charts.comparison = new Chart(comparisonChart.value, {
      type: 'bar',
      data: {
        labels: ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%'],
        datasets: [{
          label: 'Distribuição de Estudantes',
          data: [5, 15, 35, 30, 15],
          backgroundColor: ['#ef4444', '#f59e0b', '#eab308', '#84cc16', '#10b981'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }
}

const updateCompetencyChart = () => {
  if (charts.competency) {
    charts.competency.destroy()
  }

  if (competencyChart.value) {
    const type = competencyView.value === 'radar' ? 'radar' : 'bar'
    
    charts.competency = new Chart(competencyChart.value, {
      type,
      data: {
        labels: competencies.value.map(c => c.name),
        datasets: [{
          label: 'Desempenho Atual',
          data: competencies.value.map(c => c.percentage),
          backgroundColor: competencies.value.map(c => c.color + '80'),
          borderColor: competencies.value.map(c => c.color),
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: type === 'radar' ? {
          r: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        } : {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }
}

// Helper Functions
const generateDateLabels = (days, offset = 0) => {
  const labels = []
  const now = new Date()
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i + offset)
    labels.push(date.toLocaleDateString('pt-BR', { day: '2-digit', month: 'short' }))
  }
  return labels
}

const generateRandomData = (count, min, max) => {
  return Array(count).fill(0).map(() => Math.floor(Math.random() * (max - min) + min))
}

const generatePredictionData = (count, start, end) => {
  const data = []
  const step = (end - start) / count
  for (let i = 0; i < count; i++) {
    data.push(Math.floor(start + step * i + (Math.random() - 0.5) * 5))
  }
  return data
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    createCharts()
  })
})

onUnmounted(() => {
  Object.values(charts).forEach(chart => chart?.destroy())
})

// Watchers
watch(selectedPeriod, () => {
  // Atualizar gráficos com novo período
  updateCharts()
})

watch(selectedEvolutionMetric, () => {
  // Atualizar gráfico de evolução
  updateEvolutionChart()
})

const updateCharts = () => {
  // Implementar atualização dos gráficos
}

const updateEvolutionChart = () => {
  // Implementar atualização do gráfico de evolução
}
</script>

<style scoped>
.performance-analytics {
  width: 100%;
  background: var(--surface-color, #ffffff);
  border-radius: 20px;
  overflow: hidden;
}

/* Header */
.analytics-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  color: white;
}

.header-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  animation: shimmer 3s linear infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.header-content {
  position: relative;
  z-index: 1;
}

.analytics-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* KPI Cards */
.kpi-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.kpi-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.kpi-content {
  flex: 1;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.kpi-value .unit {
  font-size: 1.25rem;
  font-weight: 400;
  opacity: 0.8;
}

.kpi-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.kpi-trend {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.kpi-trend.positive {
  color: #10b981;
}

.kpi-trend.negative {
  color: #ef4444;
}

.kpi-trend.neutral {
  color: #f59e0b;
}

.kpi-spark {
  width: 80px;
  height: 40px;
}

/* Controls */
.analytics-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: var(--surface-light, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.period-selector {
  display: flex;
  gap: 0.5rem;
}

.period-btn {
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color, #e5e7eb);
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.period-btn:hover {
  border-color: #667eea;
}

.period-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.filter-controls {
  display: flex;
  gap: 1rem;
}

.filter-dropdown {
  position: relative;
}

.filter-btn,
.export-btn {
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color, #e5e7eb);
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.filter-btn:hover,
.export-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  min-width: 200px;
  z-index: 100;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: var(--surface-light, #f8f9fa);
}

.dropdown-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Charts Dashboard */
.charts-dashboard {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
  grid-auto-rows: minmax(300px, auto);
}

.chart-container {
  background: var(--surface-light, #f8f9fa);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.chart-container.large {
  grid-column: span 12;
}

.chart-container.medium {
  grid-column: span 6;
}

.chart-container.small {
  grid-column: span 4;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.chart-options {
  display: flex;
  gap: 0.5rem;
}

.metric-btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color, #e5e7eb);
  background: white;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.metric-btn:hover {
  border-color: #667eea;
}

.metric-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.view-toggle {
  background: white;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle:hover {
  border-color: #667eea;
  color: #667eea;
}

.chart-body {
  flex: 1;
  position: relative;
  min-height: 200px;
}

.chart-body canvas {
  max-height: 100%;
}

.chart-insights {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.insight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.insight i {
  font-size: 1.1rem;
}

/* Heatmap */
.heatmap-container {
  width: 100%;
}

.heatmap-months {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-left: 2rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.heatmap-grid {
  display: flex;
  gap: 0.5rem;
}

.heatmap-weekdays {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.heatmap-weekdays span {
  height: 13px;
  display: flex;
  align-items: center;
}

.heatmap-cells {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(53, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 2px;
  grid-auto-flow: column;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.heatmap-cell.level-0 {
  background: var(--border-color, #e5e7eb);
}

.heatmap-cell.level-1 {
  background: #c7d2fe;
}

.heatmap-cell.level-2 {
  background: #a5b4fc;
}

.heatmap-cell.level-3 {
  background: #818cf8;
}

.heatmap-cell.level-4 {
  background: #6366f1;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.legend-cells {
  display: flex;
  gap: 2px;
}

.legend-cells div {
  width: 13px;
  height: 13px;
  border-radius: 2px;
}

/* Competency Details */
.competency-details {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.competency-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.competency-item:hover {
  transform: translateX(5px);
}

.competency-item.selected {
  transform: translateX(10px);
}

.competency-bar {
  height: 8px;
  background: var(--border-color, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.competency-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.competency-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.competency-name {
  color: var(--text-primary);
  font-weight: 500;
}

.competency-value {
  color: var(--text-secondary);
}

/* Time Stats */
.time-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.time-stat {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-stat i {
  font-size: 1.5rem;
  color: #667eea;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Retention Info */
.retention-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.retention-stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.retention-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.retention-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Question Type Legend */
.question-type-legend {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-label {
  flex: 1;
  color: var(--text-primary);
}

.legend-value {
  font-weight: 600;
  color: var(--text-secondary);
}

/* Prediction Content */
.prediction-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.prediction-chart {
  position: relative;
}

.prediction-confidence {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  font-size: 0.875rem;
  color: #667eea;
  font-weight: 500;
}

.prediction-insights h4 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.prediction-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.prediction-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  border: 2px solid var(--border-color, #e5e7eb);
  transition: all 0.3s ease;
}

.prediction-card:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.prediction-card.success {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.prediction-card.warning {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.prediction-card.info {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.prediction-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.prediction-card.success .prediction-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.prediction-card.warning .prediction-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.prediction-card.info .prediction-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.prediction-info {
  flex: 1;
}

.prediction-info h5 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.prediction-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.prediction-action .action-btn {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: gap 0.2s ease;
}

.prediction-action .action-btn:hover {
  gap: 0.5rem;
}

.prediction-metric {
  text-align: center;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Percentile Card */
.percentile-info {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.percentile-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.percentile-value {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}

.percentile-label {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.percentile-description {
  font-size: 0.875rem;
  opacity: 0.8;
}

.anonymous-toggle {
  padding: 0.375rem 0.75rem;
  background: white;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.anonymous-toggle:hover {
  border-color: #667eea;
  color: #667eea;
}

/* Goals */
.add-goal-btn {
  padding: 0.375rem 0.75rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.add-goal-btn:hover {
  background: #5a67d8;
}

.goals-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.goal-item {
  padding: 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.goal-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.goal-item.completed {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 1rem;
}

.goal-info h4 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.goal-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--border-color, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.progress-text {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.goal-actions {
  display: flex;
  gap: 0.5rem;
}

.goal-action {
  background: white;
  border: 1px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.goal-action:hover {
  border-color: #667eea;
  color: #667eea;
}

.goal-action.delete:hover {
  border-color: #ef4444;
  color: #ef4444;
}

/* Modals */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.day-details-modal,
.goal-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.modal-header h3 {
  font-size: 1.25rem;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.day-summary {
  display: flex;
  justify-content: space-around;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.summary-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.summary-stat i {
  font-size: 1.25rem;
  color: #667eea;
}

.day-activities h4 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.activity-timeline {
  position: relative;
  padding-left: 2rem;
}

.activity-timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-color, #e5e7eb);
}

.activity-item {
  position: relative;
  margin-bottom: 1.5rem;
  display: grid;
  grid-template-columns: 60px auto;
  gap: 1rem;
  align-items: start;
}

.activity-time {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: right;
}

.activity-marker {
  position: absolute;
  left: -1.25rem;
  top: 0.25rem;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid white;
  background: #667eea;
}

.activity-marker.study {
  background: #667eea;
}

.activity-marker.questions {
  background: #764ba2;
}

.activity-marker.revision {
  background: #f093fb;
}

.activity-content h5 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.activity-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.activity-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

/* Goal Form */
.goal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
}

.btn-cancel:hover {
  border-color: #667eea;
  color: #667eea;
}

.btn-submit {
  background: #667eea;
  border: none;
  color: white;
}

.btn-submit:hover {
  background: #5a67d8;
}

/* Transitions */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .day-details-modal,
.modal-enter-active .goal-modal {
  transition: transform 0.3s ease;
}

.modal-enter-from .day-details-modal,
.modal-enter-from .goal-modal {
  transform: scale(0.9);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .performance-analytics {
    background: #1a1a1a;
    color: #ffffff;
  }

  .chart-container,
  .filter-btn,
  .export-btn,
  .period-btn,
  .metric-btn,
  .view-toggle,
  .anonymous-toggle,
  .goal-item,
  .dropdown-menu,
  .day-details-modal,
  .goal-modal {
    background: #2a2a2a;
    border-color: #444444;
    color: #ffffff;
  }

  .dropdown-item:hover,
  .kpi-card {
    background: #333333;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    background: #333333;
    border-color: #444444;
    color: #ffffff;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .chart-container.medium {
    grid-column: span 12;
  }

  .chart-container.small {
    grid-column: span 6;
  }

  .prediction-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .kpi-cards {
    grid-template-columns: 1fr;
  }

  .analytics-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .period-selector {
    flex-wrap: wrap;
  }

  .chart-container.small {
    grid-column: span 12;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .day-summary {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>