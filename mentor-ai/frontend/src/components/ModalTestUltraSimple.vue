<template>
  <div style="padding: 50px; background: #000; color: #fff; min-height: 100vh;">
    <h1>TESTE ULTRA SIMPLES DO MODAL</h1>
    
    <div style="margin: 20px 0; padding: 20px; border: 2px solid #fff;">
      <p>Estado do Modal: <strong style="color: #00ff00;">{{ modalAberto }}</strong></p>
      <button 
        @click="modalAberto = !modalAberto" 
        style="padding: 20px; font-size: 24px; background: #ff0000; color: #fff; border: none; cursor: pointer;"
      >
        CLIQUE AQUI ({{ modalAberto ? 'FECHAR' : 'ABRIR' }})
      </button>
    </div>
    
    <!-- Modal Super Simples -->
    <div v-if="modalAberto" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,0,0,0.9); display: flex; align-items: center; justify-content: center; z-index: 999999;">
      <div style="background: white; color: black; padding: 50px; border-radius: 10px;">
        <h1>🎉 MODAL FUNCIONANDO! 🎉</h1>
        <button @click="modalAberto = false" style="padding: 10px 20px; background: #000; color: #fff; border: none; cursor: pointer;">
          FECHAR
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  setup() {
    const modalAberto = ref(false);
    return { modalAberto };
  }
};
</script>