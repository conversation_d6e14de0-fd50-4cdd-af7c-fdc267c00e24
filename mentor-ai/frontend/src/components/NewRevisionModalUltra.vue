<template>
  <div class="ultra-modal">
    <!-- <PERSON><PERSON> -->
    <div class="modal-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="fas fa-graduation-cap"></i>
          <div class="icon-pulse"></div>
        </div>
        <div class="header-info">
          <h2 class="modal-title">Central de Estudos e Revisões</h2>
          <p class="modal-subtitle">Registre seus estudos e organize suas revisões</p>
        </div>
      </div>
      <button @click="$emit('close')" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Progress Bar -->
    <div class="progress-indicator">
      <div class="progress-track">
        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
      </div>
      <div class="progress-steps">
        <div v-for="(step, index) in progressSteps" :key="index" 
             class="step" 
             :class="{ active: currentStep === index, completed: currentStep > index }">
          <div class="step-dot">
            <i v-if="currentStep > index" class="fas fa-check"></i>
            <span v-else>{{ index + 1 }}</span>
          </div>
          <span class="step-label">{{ step }}</span>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button v-for="tab in tabs" :key="tab.id"
              @click="activeTab = tab.id"
              class="tab-button"
              :class="{ active: activeTab === tab.id }">
        <div class="tab-icon">
          <i :class="tab.icon"></i>
        </div>
        <div class="tab-content">
          <span class="tab-label">{{ tab.label }}</span>
          <span class="tab-description">{{ tab.description }}</span>
        </div>
        <div v-if="tab.badge" class="tab-badge">{{ tab.badge }}</div>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
      <!-- Registrar Estudo Teórico -->
      <transition name="tab-slide">
        <div v-if="activeTab === 'theory'" class="tab-panel">
          <div class="panel-header">
            <h3>Registrar Novo Estudo Teórico</h3>
            <p>Registre o que você estudou hoje e agende automaticamente o primeiro contato com questões</p>
          </div>

          <form @submit.prevent="saveTheoryStudy" class="study-form">
            <!-- Quick Templates -->
            <div class="quick-templates">
              <h4>Templates Rápidos</h4>
              <div class="template-grid">
                <button v-for="template in studyTemplates" :key="template.id"
                        type="button"
                        @click="applyTemplate(template)"
                        class="template-card">
                  <i :class="template.icon"></i>
                  <span>{{ template.name }}</span>
                </button>
              </div>
            </div>

            <!-- Form Grid -->
            <div class="form-grid">
              <div class="form-group">
                <label>
                  <i class="fas fa-book"></i>
                  Título do Estudo
                </label>
                <input 
                  v-model="theoryForm.title" 
                  type="text" 
                  required
                  placeholder="Ex: Sistema Cardiovascular - Anatomia"
                  class="form-input">
                <span class="input-hint">Seja específico para melhor organização</span>
              </div>

              <div class="form-group">
                <label>
                  <i class="fas fa-graduation-cap"></i>
                  Matéria
                </label>
                <div class="custom-select">
                  <select v-model="theoryForm.subject" required class="form-select">
                    <option value="">Selecione a matéria</option>
                    <optgroup label="Básicas">
                      <option v-for="subj in basicSubjects" :key="subj" :value="subj">{{ subj }}</option>
                    </optgroup>
                    <optgroup label="Clínicas">
                      <option v-for="subj in clinicalSubjects" :key="subj" :value="subj">{{ subj }}</option>
                    </optgroup>
                  </select>
                  <i class="fas fa-chevron-down select-icon"></i>
                </div>
              </div>

              <div class="form-group">
                <label>
                  <i class="fas fa-tachometer-alt"></i>
                  Nível de Dificuldade
                </label>
                <div class="difficulty-selector">
                  <button v-for="diff in difficulties" :key="diff.value"
                          type="button"
                          @click="theoryForm.difficulty = diff.value"
                          class="difficulty-option"
                          :class="{ active: theoryForm.difficulty === diff.value }">
                    <div class="diff-icon" :style="{ color: diff.color }">
                      <i :class="diff.icon"></i>
                    </div>
                    <span class="diff-label">{{ diff.label }}</span>
                    <span class="diff-description">{{ diff.description }}</span>
                  </button>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>
                    <i class="fas fa-calendar-day"></i>
                    Data do Estudo
                  </label>
                  <input 
                    v-model="theoryForm.date" 
                    type="date" 
                    required
                    class="form-input">
                </div>

                <div class="form-group">
                  <label>
                    <i class="fas fa-clock"></i>
                    Duração (minutos)
                  </label>
                  <div class="duration-input">
                    <button type="button" @click="adjustDuration(-15)" class="duration-btn">
                      <i class="fas fa-minus"></i>
                    </button>
                    <input 
                      v-model.number="theoryForm.duration" 
                      type="number" 
                      min="15" 
                      step="15"
                      required
                      class="form-input text-center">
                    <button type="button" @click="adjustDuration(15)" class="duration-btn">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="form-group full-width">
                <label>
                  <i class="fas fa-lightbulb"></i>
                  Principais Aprendizados
                </label>
                <div class="tags-input">
                  <div class="tags-list">
                    <span v-for="(tag, index) in theoryForm.keyLearnings" :key="index" class="tag">
                      {{ tag }}
                      <button type="button" @click="removeTag(index)" class="tag-remove">
                        <i class="fas fa-times"></i>
                      </button>
                    </span>
                  </div>
                  <input 
                    v-model="newTag"
                    @keydown.enter.prevent="addTag"
                    type="text"
                    placeholder="Digite e pressione Enter para adicionar"
                    class="tag-input">
                </div>
              </div>

              <div class="form-group full-width">
                <label>
                  <i class="fas fa-sticky-note"></i>
                  Anotações e Observações
                </label>
                <div class="rich-textarea">
                  <div class="textarea-toolbar">
                    <button type="button" @click="formatText('bold')" class="toolbar-btn">
                      <i class="fas fa-bold"></i>
                    </button>
                    <button type="button" @click="formatText('italic')" class="toolbar-btn">
                      <i class="fas fa-italic"></i>
                    </button>
                    <button type="button" @click="formatText('list')" class="toolbar-btn">
                      <i class="fas fa-list-ul"></i>
                    </button>
                  </div>
                  <textarea 
                    v-model="theoryForm.notes" 
                    rows="4"
                    placeholder="Dúvidas, insights, pontos importantes..."
                    class="form-textarea"></textarea>
                </div>
              </div>
            </div>

            <!-- AI Suggestion -->
            <div class="ai-suggestion">
              <div class="suggestion-header">
                <i class="fas fa-magic"></i>
                <span>Sugestão da IA</span>
              </div>
              <p>{{ getAISuggestion() }}</p>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
              <button type="button" @click="$emit('close')" class="btn-secondary">
                Cancelar
              </button>
              <button type="submit" class="btn-primary">
                <i class="fas fa-save"></i>
                Registrar Estudo
              </button>
            </div>
          </form>
        </div>
      </transition>

      <!-- Criar Revisão Manual -->
      <transition name="tab-slide">
        <div v-if="activeTab === 'manual'" class="tab-panel">
          <div class="panel-header">
            <h3>Agendar Revisão Manual</h3>
            <p>Crie revisões personalizadas para conteúdos específicos</p>
          </div>

          <form @submit.prevent="saveManualRevision" class="revision-form">
            <!-- Revision Type Selector -->
            <div class="revision-types">
              <h4>Tipo de Revisão</h4>
              <div class="type-grid">
                <label v-for="type in revisionTypes" :key="type.value" 
                       class="type-option">
                  <input 
                    type="radio" 
                    v-model="manualForm.type" 
                    :value="type.value"
                    name="revisionType">
                  <div class="type-card" :class="{ selected: manualForm.type === type.value }">
                    <div class="type-icon" :style="{ background: type.color }">
                      <i :class="type.icon"></i>
                    </div>
                    <div class="type-info">
                      <span class="type-name">{{ type.label }}</span>
                      <span class="type-desc">{{ type.description }}</span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Form Fields -->
            <div class="form-grid">
              <div class="form-group">
                <label>
                  <i class="fas fa-edit"></i>
                  Título da Revisão
                </label>
                <input 
                  v-model="manualForm.title" 
                  type="text" 
                  required
                  placeholder="Ex: Revisão de Farmacologia - Antibióticos"
                  class="form-input">
              </div>

              <div class="form-group">
                <label>
                  <i class="fas fa-book-medical"></i>
                  Matéria
                </label>
                <div class="custom-select">
                  <select v-model="manualForm.subject" required class="form-select">
                    <option value="">Selecione a matéria</option>
                    <option v-for="subj in allSubjects" :key="subj" :value="subj">{{ subj }}</option>
                  </select>
                  <i class="fas fa-chevron-down select-icon"></i>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>
                    <i class="fas fa-calendar-check"></i>
                    Data da Revisão
                  </label>
                  <input 
                    v-model="manualForm.date" 
                    type="date" 
                    required
                    :min="minDate"
                    class="form-input">
                </div>

                <div class="form-group">
                  <label>
                    <i class="fas fa-clock"></i>
                    Horário
                  </label>
                  <input 
                    v-model="manualForm.time" 
                    type="time" 
                    required
                    class="form-input">
                </div>
              </div>

              <div class="form-group full-width">
                <label>
                  <i class="fas fa-exclamation-triangle"></i>
                  Prioridade
                </label>
                <div class="priority-selector">
                  <button v-for="priority in priorities" :key="priority.value"
                          type="button"
                          @click="manualForm.priority = priority.value"
                          class="priority-option"
                          :class="{ active: manualForm.priority === priority.value }">
                    <div class="priority-indicator" :style="{ background: priority.color }"></div>
                    <span class="priority-label">{{ priority.label }}</span>
                    <span class="priority-desc">{{ priority.description }}</span>
                  </button>
                </div>
              </div>

              <div class="form-group full-width">
                <label>
                  <i class="fas fa-repeat"></i>
                  Repetição
                </label>
                <div class="repeat-options">
                  <label class="checkbox-option">
                    <input 
                      type="checkbox" 
                      v-model="manualForm.repeat.enabled"
                      class="form-checkbox">
                    <span>Repetir esta revisão</span>
                  </label>
                  
                  <div v-if="manualForm.repeat.enabled" class="repeat-config">
                    <select v-model="manualForm.repeat.frequency" class="form-select small">
                      <option value="daily">Diariamente</option>
                      <option value="weekly">Semanalmente</option>
                      <option value="biweekly">Quinzenalmente</option>
                      <option value="monthly">Mensalmente</option>
                    </select>
                    
                    <div class="repeat-count">
                      <label>Por</label>
                      <input 
                        v-model.number="manualForm.repeat.count" 
                        type="number" 
                        min="1" 
                        max="12"
                        class="form-input small">
                      <label>vezes</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Calendar Preview -->
            <div class="calendar-preview">
              <h4>
                <i class="fas fa-calendar-alt"></i>
                Prévia no Calendário
              </h4>
              <div class="mini-calendar">
                <div class="calendar-month">{{ getMonthName() }}</div>
                <div class="calendar-grid">
                  <div v-for="day in calendarPreview" :key="day.date" 
                       class="calendar-day-mini"
                       :class="{ 
                         selected: isSelectedDate(day.date),
                         today: isToday(day.date),
                         repeat: isRepeatDate(day.date)
                       }">
                    {{ day.number }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
              <button type="button" @click="$emit('close')" class="btn-secondary">
                Cancelar
              </button>
              <button type="submit" class="btn-primary">
                <i class="fas fa-calendar-plus"></i>
                Agendar Revisão
              </button>
            </div>
          </form>
        </div>
      </transition>

      <!-- Insights e Analytics -->
      <transition name="tab-slide">
        <div v-if="activeTab === 'insights'" class="tab-panel">
          <div class="panel-header">
            <h3>Insights de Aprendizado</h3>
            <p>Análise inteligente do seu progresso e sugestões personalizadas</p>
          </div>

          <div class="insights-content">
            <!-- Performance Overview -->
            <div class="insight-card">
              <div class="insight-header">
                <i class="fas fa-chart-line"></i>
                <h4>Visão Geral de Desempenho</h4>
              </div>
              <div class="performance-metrics">
                <div class="metric">
                  <span class="metric-value">{{ insights.studyHours }}h</span>
                  <span class="metric-label">Horas estudadas</span>
                </div>
                <div class="metric">
                  <span class="metric-value">{{ insights.completionRate }}%</span>
                  <span class="metric-label">Taxa de conclusão</span>
                </div>
                <div class="metric">
                  <span class="metric-value">{{ insights.avgScore }}%</span>
                  <span class="metric-label">Média geral</span>
                </div>
              </div>
            </div>

            <!-- Best Study Times -->
            <div class="insight-card">
              <div class="insight-header">
                <i class="fas fa-clock"></i>
                <h4>Melhores Horários de Estudo</h4>
              </div>
              <div class="time-heatmap">
                <div v-for="hour in studyHeatmap" :key="hour.time" 
                     class="hour-block"
                     :style="{ background: getHeatmapColor(hour.productivity) }">
                  <span class="hour-label">{{ hour.time }}</span>
                  <span class="hour-value">{{ hour.productivity }}%</span>
                </div>
              </div>
              <p class="insight-recommendation">
                <i class="fas fa-lightbulb"></i>
                Você é mais produtivo entre {{ insights.bestTimeRange }}
              </p>
            </div>

            <!-- Subject Recommendations -->
            <div class="insight-card">
              <div class="insight-header">
                <i class="fas fa-brain"></i>
                <h4>Recomendações por Matéria</h4>
              </div>
              <div class="subject-recommendations">
                <div v-for="rec in subjectRecommendations" :key="rec.subject" 
                     class="recommendation">
                  <div class="rec-header">
                    <span class="rec-subject">{{ rec.subject }}</span>
                    <span class="rec-status" :class="rec.status">{{ rec.statusLabel }}</span>
                  </div>
                  <p class="rec-text">{{ rec.recommendation }}</p>
                  <div class="rec-action">
                    <button @click="applyRecommendation(rec)" class="btn-small">
                      <i class="fas fa-magic"></i>
                      Aplicar sugestão
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Study Streak -->
            <div class="insight-card">
              <div class="insight-header">
                <i class="fas fa-fire"></i>
                <h4>Sequência de Estudos</h4>
              </div>
              <div class="streak-display">
                <div class="current-streak">
                  <span class="streak-number">{{ insights.currentStreak }}</span>
                  <span class="streak-label">dias consecutivos</span>
                </div>
                <div class="streak-calendar">
                  <div v-for="day in streakCalendar" :key="day.date" 
                       class="streak-day"
                       :class="{ active: day.studied, today: day.isToday }">
                    <span class="day-abbr">{{ day.abbr }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'NewRevisionModalUltra',
  emits: ['close', 'save', 'saveStudy'],
  setup(props, { emit }) {
    // State
    const activeTab = ref('theory');
    const currentStep = ref(0);
    const newTag = ref('');
    
    // Forms
    const theoryForm = ref({
      title: '',
      subject: '',
      difficulty: 'medium',
      date: new Date().toISOString().split('T')[0],
      duration: 60,
      keyLearnings: [],
      notes: ''
    });
    
    const manualForm = ref({
      title: '',
      subject: '',
      type: 'questions',
      date: '',
      time: '14:00',
      priority: 'medium',
      repeat: {
        enabled: false,
        frequency: 'weekly',
        count: 4
      }
    });
    
    // Tab configuration
    const tabs = [
      {
        id: 'theory',
        label: 'Registrar Estudo',
        description: 'Novo estudo teórico',
        icon: 'fas fa-book-open'
      },
      {
        id: 'manual',
        label: 'Agendar Revisão',
        description: 'Revisão manual',
        icon: 'fas fa-calendar-plus'
      },
      {
        id: 'insights',
        label: 'Insights',
        description: 'Análise e sugestões',
        icon: 'fas fa-chart-line',
        badge: 'NEW'
      }
    ];
    
    // Progress steps
    const progressSteps = ['Informações', 'Detalhes', 'Confirmação'];
    
    const progressPercentage = computed(() => {
      return ((currentStep.value + 1) / progressSteps.length) * 100;
    });
    
    // Study templates
    const studyTemplates = [
      {
        id: 1,
        name: 'Anatomia',
        icon: 'fas fa-user',
        data: { subject: 'Anatomia', difficulty: 'medium', duration: 90 }
      },
      {
        id: 2,
        name: 'Farmacologia',
        icon: 'fas fa-pills',
        data: { subject: 'Farmacologia', difficulty: 'hard', duration: 120 }
      },
      {
        id: 3,
        name: 'Fisiologia',
        icon: 'fas fa-heartbeat',
        data: { subject: 'Fisiologia', difficulty: 'medium', duration: 90 }
      },
      {
        id: 4,
        name: 'Patologia',
        icon: 'fas fa-microscope',
        data: { subject: 'Patologia', difficulty: 'hard', duration: 120 }
      }
    ];
    
    // Subjects
    const basicSubjects = ['Anatomia', 'Fisiologia', 'Bioquímica', 'Histologia', 'Embriologia'];
    const clinicalSubjects = ['Clínica Médica', 'Cirurgia', 'Pediatria', 'Ginecologia', 'Psiquiatria'];
    const allSubjects = [...basicSubjects, ...clinicalSubjects];
    
    // Difficulties
    const difficulties = [
      {
        value: 'easy',
        label: 'Fácil',
        description: 'Revisão em 2 dias',
        icon: 'fas fa-smile',
        color: '#10b981'
      },
      {
        value: 'medium',
        label: 'Médio',
        description: 'Revisão em 1 dia',
        icon: 'fas fa-meh',
        color: '#f59e0b'
      },
      {
        value: 'hard',
        label: 'Difícil',
        description: 'Revisão em 1 dia',
        icon: 'fas fa-frown',
        color: '#ef4444'
      }
    ];
    
    // Priorities
    const priorities = [
      {
        value: 'low',
        label: 'Baixa',
        description: 'Pode ser adiada',
        color: '#10b981'
      },
      {
        value: 'medium',
        label: 'Média',
        description: 'Importante',
        color: '#f59e0b'
      },
      {
        value: 'high',
        label: 'Alta',
        description: 'Urgente',
        color: '#ef4444'
      }
    ];
    
    // Revision types
    const revisionTypes = [
      {
        value: 'questions',
        label: 'Questões',
        description: 'Resolver questões práticas',
        icon: 'fas fa-question-circle',
        color: 'linear-gradient(135deg, #6366f1, #8b5cf6)'
      },
      {
        value: 'summary',
        label: 'Resumo',
        description: 'Revisar resumos e anotações',
        icon: 'fas fa-file-alt',
        color: 'linear-gradient(135deg, #3b82f6, #06b6d4)'
      },
      {
        value: 'flashcards',
        label: 'Flashcards',
        description: 'Memorização ativa',
        icon: 'fas fa-layer-group',
        color: 'linear-gradient(135deg, #8b5cf6, #ec4899)'
      },
      {
        value: 'practice',
        label: 'Prática',
        description: 'Casos clínicos e simulações',
        icon: 'fas fa-stethoscope',
        color: 'linear-gradient(135deg, #10b981, #059669)'
      }
    ];
    
    // Insights data
    const insights = ref({
      studyHours: 47,
      completionRate: 92,
      avgScore: 85,
      bestTimeRange: '14h às 18h',
      currentStreak: 12
    });
    
    const studyHeatmap = ref([
      { time: '06h', productivity: 20 },
      { time: '08h', productivity: 40 },
      { time: '10h', productivity: 60 },
      { time: '14h', productivity: 95 },
      { time: '16h', productivity: 85 },
      { time: '18h', productivity: 70 },
      { time: '20h', productivity: 50 },
      { time: '22h', productivity: 30 }
    ]);
    
    const subjectRecommendations = ref([
      {
        subject: 'Anatomia',
        status: 'excellent',
        statusLabel: 'Excelente',
        recommendation: 'Continue com o ritmo atual. Considere aumentar a dificuldade das questões.'
      },
      {
        subject: 'Farmacologia',
        status: 'attention',
        statusLabel: 'Atenção',
        recommendation: 'Dedique mais tempo para revisões. Foque em mecanismos de ação.'
      },
      {
        subject: 'Fisiologia',
        status: 'good',
        statusLabel: 'Bom',
        recommendation: 'Progresso consistente. Adicione mais casos clínicos às revisões.'
      }
    ]);
    
    const streakCalendar = ref([
      { date: '2024-12-01', abbr: 'D', studied: true, isToday: false },
      { date: '2024-12-02', abbr: 'S', studied: true, isToday: false },
      { date: '2024-12-03', abbr: 'T', studied: true, isToday: false },
      { date: '2024-12-04', abbr: 'Q', studied: true, isToday: false },
      { date: '2024-12-05', abbr: 'Q', studied: true, isToday: true },
      { date: '2024-12-06', abbr: 'S', studied: false, isToday: false },
      { date: '2024-12-07', abbr: 'S', studied: false, isToday: false }
    ]);
    
    // Calendar preview
    const calendarPreview = computed(() => {
      const days = [];
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      for (let i = 1; i <= lastDay.getDate(); i++) {
        days.push({
          date: new Date(date.getFullYear(), date.getMonth(), i),
          number: i
        });
      }
      
      return days;
    });
    
    // Methods
    const applyTemplate = (template) => {
      Object.assign(theoryForm.value, template.data);
    };
    
    const adjustDuration = (amount) => {
      const newDuration = theoryForm.value.duration + amount;
      if (newDuration >= 15 && newDuration <= 240) {
        theoryForm.value.duration = newDuration;
      }
    };
    
    const addTag = () => {
      if (newTag.value.trim() && theoryForm.value.keyLearnings.length < 5) {
        theoryForm.value.keyLearnings.push(newTag.value.trim());
        newTag.value = '';
      }
    };
    
    const removeTag = (index) => {
      theoryForm.value.keyLearnings.splice(index, 1);
    };
    
    const formatText = (format) => {
      // Placeholder for text formatting
      console.log('Format:', format);
    };
    
    const getAISuggestion = () => {
      const suggestions = {
        easy: 'Conteúdo marcado como fácil. O primeiro contato com questões será em 2 dias para consolidar o aprendizado.',
        medium: 'Dificuldade média identificada. Revisão agendada para amanhã para reforçar os conceitos.',
        hard: 'Material complexo detectado. Recomendamos revisar amanhã e fazer anotações adicionais.'
      };
      return suggestions[theoryForm.value.difficulty] || suggestions.medium;
    };
    
    const getMonthName = () => {
      const date = manualForm.value.date ? new Date(manualForm.value.date) : new Date();
      return date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    };
    
    const isSelectedDate = (date) => {
      if (!manualForm.value.date) return false;
      return date.toDateString() === new Date(manualForm.value.date).toDateString();
    };
    
    const isToday = (date) => {
      return date.toDateString() === new Date().toDateString();
    };
    
    const isRepeatDate = (date) => {
      if (!manualForm.value.repeat.enabled || !manualForm.value.date) return false;
      // Simplified repeat date calculation
      const selected = new Date(manualForm.value.date);
      const diff = Math.floor((date - selected) / (1000 * 60 * 60 * 24));
      
      if (manualForm.value.repeat.frequency === 'weekly') {
        return diff > 0 && diff % 7 === 0 && diff / 7 <= manualForm.value.repeat.count;
      }
      
      return false;
    };
    
    const getHeatmapColor = (productivity) => {
      if (productivity >= 80) return 'linear-gradient(135deg, #10b981, #059669)';
      if (productivity >= 60) return 'linear-gradient(135deg, #3b82f6, #06b6d4)';
      if (productivity >= 40) return 'linear-gradient(135deg, #f59e0b, #fbbf24)';
      return 'linear-gradient(135deg, #6b7280, #9ca3af)';
    };
    
    const applyRecommendation = (rec) => {
      // Switch to manual tab and pre-fill with recommendation
      activeTab.value = 'manual';
      manualForm.value.subject = rec.subject;
      manualForm.value.priority = rec.status === 'attention' ? 'high' : 'medium';
    };
    
    const saveTheoryStudy = () => {
      currentStep.value = 2;
      setTimeout(() => {
        emit('saveStudy', theoryForm.value);
        resetForm();
      }, 500);
    };
    
    const saveManualRevision = () => {
      currentStep.value = 2;
      setTimeout(() => {
        emit('save', manualForm.value);
        resetForm();
      }, 500);
    };
    
    const resetForm = () => {
      currentStep.value = 0;
      theoryForm.value = {
        title: '',
        subject: '',
        difficulty: 'medium',
        date: new Date().toISOString().split('T')[0],
        duration: 60,
        keyLearnings: [],
        notes: ''
      };
      manualForm.value = {
        title: '',
        subject: '',
        type: 'questions',
        date: '',
        time: '14:00',
        priority: 'medium',
        repeat: {
          enabled: false,
          frequency: 'weekly',
          count: 4
        }
      };
    };
    
    // Computed
    const minDate = computed(() => {
      return new Date().toISOString().split('T')[0];
    });
    
    // Watch active tab changes
    watch(activeTab, () => {
      currentStep.value = 0;
    });
    
    return {
      // State
      activeTab,
      currentStep,
      newTag,
      theoryForm,
      manualForm,
      
      // Data
      tabs,
      progressSteps,
      progressPercentage,
      studyTemplates,
      basicSubjects,
      clinicalSubjects,
      allSubjects,
      difficulties,
      priorities,
      revisionTypes,
      insights,
      studyHeatmap,
      subjectRecommendations,
      streakCalendar,
      calendarPreview,
      minDate,
      
      // Methods
      applyTemplate,
      adjustDuration,
      addTag,
      removeTag,
      formatText,
      getAISuggestion,
      getMonthName,
      isSelectedDate,
      isToday,
      isRepeatDate,
      getHeatmapColor,
      applyRecommendation,
      saveTheoryStudy,
      saveManualRevision
    };
  }
};
</script>

<style scoped>
/* Ultra Modal Base */
.ultra-modal {
  background: #0f1419;
  border-radius: 24px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

/* Modal Header */
.modal-header {
  background: linear-gradient(135deg, #1e2433 0%, #141925 100%);
  padding: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.icon-pulse {
  position: absolute;
  inset: -3px;
  border: 2px solid rgba(99, 102, 241, 0.4);
  border-radius: 23px;
  animation: pulse 2s infinite;
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: #e4e6eb;
}

.modal-subtitle {
  margin: 0.25rem 0 0;
  color: #94a3b8;
  font-size: 0.875rem;
}

.close-btn {
  width: 48px;
  height: 48px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  color: #94a3b8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 1.25rem;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
  transform: rotate(90deg);
}

/* Progress Indicator */
.progress-indicator {
  padding: 1.5rem 2rem;
  background: rgba(30, 41, 59, 0.3);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.progress-track {
  height: 4px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.5;
  transition: all 0.3s;
}

.step.active,
.step.completed {
  opacity: 1;
}

.step-dot {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(148, 163, 184, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0f1419;
  font-size: 0.875rem;
  color: #94a3b8;
  transition: all 0.3s;
}

.step.active .step-dot {
  border-color: #6366f1;
  color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.step.completed .step-dot {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: transparent;
  color: white;
}

.step-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: rgba(30, 41, 59, 0.2);
  overflow-x: auto;
}

.tab-button {
  flex: 1;
  padding: 1rem;
  background: transparent;
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s;
}

.tab-button:hover {
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
}

.tab-button.active::before {
  opacity: 0.1;
}

.tab-button.active {
  border-color: #6366f1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.tab-icon {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: #6366f1;
  position: relative;
  z-index: 1;
}

.tab-button.active .tab-icon {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.tab-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.tab-label {
  display: block;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 0.25rem;
}

.tab-description {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
}

.tab-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #ef4444, #f59e0b);
  color: white;
  font-size: 0.625rem;
  font-weight: 700;
  border-radius: 4px;
  text-transform: uppercase;
}

/* Modal Body */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: #0a0e1a;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

.panel-header {
  margin-bottom: 2rem;
}

.panel-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #e4e6eb;
}

.panel-header p {
  margin: 0;
  color: #94a3b8;
}

/* Forms */
.study-form,
.revision-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  grid-column: 1 / -1;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 500;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.form-group label i {
  color: #6366f1;
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #e4e6eb;
  font-size: 1rem;
  transition: all 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-hint {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Custom Select */
.custom-select {
  position: relative;
}

.select-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
}

/* Quick Templates */
.quick-templates {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
}

.quick-templates h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: #e4e6eb;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.template-card {
  padding: 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  color: #e4e6eb;
}

.template-card:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-2px);
}

.template-card i {
  font-size: 1.5rem;
  color: #6366f1;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  grid-column: 1 / -1;
}

.difficulty-option {
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  text-align: center;
}

.difficulty-option:hover {
  background: rgba(30, 41, 59, 0.7);
}

.difficulty-option.active {
  border-color: var(--diff-color, #6366f1);
  background: rgba(99, 102, 241, 0.1);
}

.diff-icon {
  font-size: 2rem;
}

.diff-label {
  font-weight: 600;
  color: #e4e6eb;
}

.diff-description {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Duration Input */
.duration-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.duration-btn {
  width: 36px;
  height: 36px;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  color: #6366f1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.duration-btn:hover {
  background: rgba(99, 102, 241, 0.2);
}

.text-center {
  text-align: center;
}

/* Tags Input */
.tags-input {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  min-height: 32px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 6px;
  font-size: 0.875rem;
}

.tag-remove {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: 0.75rem;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.tag-remove:hover {
  opacity: 1;
}

.tag-input {
  width: 100%;
  background: transparent;
  border: none;
  color: #e4e6eb;
  outline: none;
}

/* Rich Textarea */
.rich-textarea {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.textarea-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.form-textarea {
  border: none;
  background: transparent;
  resize: vertical;
  min-height: 100px;
}

/* AI Suggestion */
.ai-suggestion {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 1rem;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #6366f1;
}

.ai-suggestion p {
  margin: 0;
  color: #94a3b8;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Revision Types */
.revision-types {
  margin-bottom: 2rem;
}

.revision-types h4 {
  margin: 0 0 1rem;
  color: #e4e6eb;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.type-option input {
  display: none;
}

.type-card {
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  gap: 1rem;
  transition: all 0.2s;
}

.type-card:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.3);
}

.type-card.selected {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.type-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.type-name {
  font-weight: 600;
  color: #e4e6eb;
}

.type-desc {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Priority Selector */
.priority-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.priority-option {
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s;
}

.priority-option:hover {
  background: rgba(30, 41, 59, 0.7);
}

.priority-option.active {
  border-color: currentColor;
  background: rgba(99, 102, 241, 0.1);
}

.priority-indicator {
  height: 4px;
  border-radius: 2px;
  width: 100%;
}

.priority-label {
  font-weight: 600;
  color: #e4e6eb;
}

.priority-desc {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Repeat Options */
.repeat-options {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  padding: 1rem;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #e4e6eb;
}

.form-checkbox {
  width: 20px;
  height: 20px;
  accent-color: #6366f1;
}

.repeat-config {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.form-select.small,
.form-input.small {
  width: auto;
  padding: 0.5rem 0.75rem;
}

.repeat-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
}

/* Calendar Preview */
.calendar-preview {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
}

.calendar-preview h4 {
  margin: 0 0 1rem;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mini-calendar {
  max-width: 300px;
  margin: 0 auto;
}

.calendar-month {
  text-align: center;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1rem;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.calendar-day-mini {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #94a3b8;
  background: rgba(148, 163, 184, 0.05);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.calendar-day-mini:hover {
  background: rgba(148, 163, 184, 0.1);
}

.calendar-day-mini.today {
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  font-weight: 600;
}

.calendar-day-mini.selected {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  font-weight: 600;
}

.calendar-day-mini.repeat {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

/* Insights Content */
.insights-content {
  display: grid;
  gap: 1.5rem;
}

.insight-card {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.insight-header i {
  font-size: 1.25rem;
  color: #6366f1;
}

.insight-header h4 {
  margin: 0;
  font-size: 1.125rem;
  color: #e4e6eb;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
}

.metric-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #6366f1;
}

.metric-label {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Time Heatmap */
.time-heatmap {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.hour-block {
  padding: 0.75rem;
  border-radius: 8px;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.hour-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
}

.hour-value {
  display: block;
  font-size: 0.75rem;
  opacity: 0.9;
}

.insight-recommendation {
  padding: 0.75rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
  color: #94a3b8;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.insight-recommendation i {
  color: #f59e0b;
}

/* Subject Recommendations */
.subject-recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation {
  padding: 1rem;
  background: rgba(148, 163, 184, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.rec-subject {
  font-weight: 600;
  color: #e4e6eb;
}

.rec-status {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.rec-status.excellent {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.rec-status.good {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.rec-status.attention {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.rec-text {
  margin: 0 0 1rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.rec-action {
  display: flex;
  justify-content: flex-end;
}

.btn-small {
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 6px;
  color: #6366f1;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.btn-small:hover {
  background: rgba(99, 102, 241, 0.2);
}

/* Streak Display */
.streak-display {
  text-align: center;
}

.current-streak {
  margin-bottom: 1.5rem;
}

.streak-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.streak-label {
  display: block;
  color: #94a3b8;
  font-size: 0.875rem;
}

.streak-calendar {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.streak-day {
  width: 40px;
  height: 40px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
  transition: all 0.2s;
}

.streak-day.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.streak-day.today {
  border: 2px solid #6366f1;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.btn-primary,
.btn-secondary {
  padding: 0.875rem 1.75rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #e4e6eb;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-slide-enter-active,
.tab-slide-leave-active {
  transition: all 0.3s ease;
}

.tab-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.tab-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* Responsive */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .template-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .type-grid {
    grid-template-columns: 1fr;
  }
  
  .priority-selector {
    grid-template-columns: 1fr;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .time-heatmap {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Scrollbar */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>