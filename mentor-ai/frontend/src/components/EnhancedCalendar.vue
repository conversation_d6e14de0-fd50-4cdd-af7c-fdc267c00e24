<template>
  <div class="enhanced-calendar">
    <!-- Header do <PERSON> -->
    <div class="calendar-header">
      <div class="calendar-nav">
        <button @click="previousMonth" class="nav-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="current-month">
          <h2>{{ currentMonthYear }}</h2>
          <button @click="showMonthPicker = !showMonthPicker" class="month-selector">
            <i class="fas fa-calendar-alt"></i>
          </button>
        </div>
        
        <button @click="nextMonth" class="nav-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      
      <div class="calendar-actions">
        <button @click="goToToday" class="today-btn">
          <i class="fas fa-calendar-day"></i>
          Hoje
        </button>
        
        <div class="view-selector">
          <button 
            v-for="view in views" 
            :key="view.id"
            @click="currentView = view.id"
            :class="['view-btn', { active: currentView === view.id }]"
          >
            <i :class="view.icon"></i>
            {{ view.label }}
          </button>
        </div>
        
        <button @click="showFilters = !showFilters" class="filter-btn">
          <i class="fas fa-filter"></i>
          Filtros
          <span v-if="activeFiltersCount > 0" class="filter-badge">{{ activeFiltersCount }}</span>
        </button>
      </div>
    </div>

    <!-- Filtros (Collapsível) -->
    <transition name="slide-down">
      <div v-if="showFilters" class="calendar-filters">
        <div class="filter-group">
          <label>Tipo de Evento</label>
          <div class="filter-options">
            <label v-for="type in eventTypes" :key="type.id" class="filter-option">
              <input 
                type="checkbox" 
                :value="type.id" 
                v-model="selectedTypes"
              />
              <span class="filter-indicator" :style="{ background: type.color }"></span>
              <span>{{ type.label }}</span>
            </label>
          </div>
        </div>
        
        <div class="filter-group">
          <label>Matérias</label>
          <div class="filter-options">
            <label v-for="subject in subjects" :key="subject" class="filter-option">
              <input 
                type="checkbox" 
                :value="subject" 
                v-model="selectedSubjects"
              />
              <span>{{ subject }}</span>
            </label>
          </div>
        </div>
        
        <div class="filter-group">
          <label>Prioridade</label>
          <div class="filter-options">
            <label v-for="priority in priorities" :key="priority.value" class="filter-option">
              <input 
                type="checkbox" 
                :value="priority.value" 
                v-model="selectedPriorities"
              />
              <span class="priority-indicator" :class="`priority-${priority.value}`">
                <i :class="priority.icon"></i>
              </span>
              <span>{{ priority.label }}</span>
            </label>
          </div>
        </div>
        
        <button @click="clearFilters" class="clear-filters-btn">
          <i class="fas fa-times"></i>
          Limpar Filtros
        </button>
      </div>
    </transition>

    <!-- Month Picker Modal -->
    <transition name="fade">
      <div v-if="showMonthPicker" class="month-picker-overlay" @click.self="showMonthPicker = false">
        <div class="month-picker">
          <div class="year-selector">
            <button @click="selectedYear--" class="year-nav">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="year-display">{{ selectedYear }}</span>
            <button @click="selectedYear++" class="year-nav">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <div class="months-grid">
            <button 
              v-for="(month, index) in months" 
              :key="index"
              @click="selectMonth(index)"
              :class="['month-btn', { 
                active: selectedMonth === index && selectedYear === currentDate.getFullYear(),
                current: index === new Date().getMonth() && selectedYear === new Date().getFullYear()
              }]"
            >
              {{ month }}
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Calendário Principal -->
    <div class="calendar-container" :class="`view-${currentView}`">
      <!-- Vista Mensal -->
      <div v-if="currentView === 'month'" class="month-view">
        <div class="weekdays">
          <div v-for="day in weekdays" :key="day" class="weekday">
            {{ day }}
          </div>
        </div>
        
        <div class="days-grid">
          <div 
            v-for="(day, index) in calendarDays" 
            :key="`day-${index}`"
            :class="getDayClasses(day)"
            @click="selectDate(day)"
            @dragover.prevent
            @drop="handleDrop($event, day)"
          >
            <div class="day-header">
              <span class="day-number">{{ day.date.getDate() }}</span>
              <div v-if="day.indicators.length > 0" class="day-indicators">
                <span 
                  v-for="indicator in day.indicators" 
                  :key="indicator"
                  class="indicator"
                  :class="`indicator-${indicator}`"
                ></span>
              </div>
            </div>
            
            <div class="day-events">
              <div 
                v-for="event in getVisibleEvents(day.events)" 
                :key="event.id"
                @click.stop="selectEvent(event)"
                class="event-item"
                :style="{ backgroundColor: event.color + '20', borderLeft: `3px solid ${event.color}` }"
                draggable="true"
                @dragstart="handleDragStart($event, event)"
              >
                <span class="event-time" v-if="event.time">{{ event.time }}</span>
                <span class="event-title">{{ event.title }}</span>
                <i v-if="event.completed" class="fas fa-check-circle completed-icon"></i>
              </div>
              
              <button 
                v-if="day.events.length > 3" 
                @click.stop="showDayDetails(day)"
                class="more-events"
              >
                +{{ day.events.length - 3 }} mais
              </button>
            </div>
            
            <button 
              v-if="day.isToday || day.isSelected" 
              @click.stop="quickAddEvent(day)"
              class="quick-add-btn"
            >
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Vista Semanal -->
      <div v-if="currentView === 'week'" class="week-view">
        <div class="week-header">
          <div class="time-column"></div>
          <div 
            v-for="day in weekDays" 
            :key="day.date"
            class="week-day-header"
            :class="{ today: isToday(day.date) }"
          >
            <span class="week-day-name">{{ formatWeekDay(day.date) }}</span>
            <span class="week-day-number">{{ day.date.getDate() }}</span>
          </div>
        </div>
        
        <div class="week-content">
          <div class="time-slots">
            <div v-for="hour in 24" :key="hour" class="time-slot">
              <span class="time-label">{{ formatHour(hour - 1) }}</span>
              <div class="time-grid">
                <div 
                  v-for="day in weekDays" 
                  :key="`${day.date}-${hour}`"
                  class="time-cell"
                  @click="createEventAtTime(day.date, hour - 1)"
                  @dragover.prevent
                  @drop="handleDropTime($event, day.date, hour - 1)"
                >
                  <div 
                    v-for="event in getEventsAtTime(day.events, hour - 1)" 
                    :key="event.id"
                    class="time-event"
                    :style="getEventStyle(event)"
                    @click.stop="selectEvent(event)"
                    draggable="true"
                    @dragstart="handleDragStart($event, event)"
                  >
                    <span class="event-title">{{ event.title }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vista de Lista -->
      <div v-if="currentView === 'list'" class="list-view">
        <div class="list-header">
          <h3>Próximos Eventos</h3>
          <div class="list-controls">
            <select v-model="listPeriod" class="period-select">
              <option value="week">Esta Semana</option>
              <option value="month">Este Mês</option>
              <option value="all">Todos</option>
            </select>
          </div>
        </div>
        
        <div class="events-list">
          <div 
            v-for="(group, date) in groupedEvents" 
            :key="date"
            class="event-group"
          >
            <div class="group-header">
              <h4>{{ formatListDate(date) }}</h4>
              <span class="event-count">{{ group.length }} eventos</span>
            </div>
            
            <div class="group-events">
              <div 
                v-for="event in group" 
                :key="event.id"
                @click="selectEvent(event)"
                class="list-event"
                :class="{ completed: event.completed }"
              >
                <div class="event-time-block" :style="{ backgroundColor: event.color }">
                  <i :class="getEventIcon(event.type)"></i>
                  <span v-if="event.time">{{ event.time }}</span>
                </div>
                
                <div class="event-details">
                  <h5>{{ event.title }}</h5>
                  <p v-if="event.description">{{ event.description }}</p>
                  <div class="event-meta">
                    <span v-if="event.subject" class="meta-item">
                      <i class="fas fa-tag"></i>
                      {{ event.subject }}
                    </span>
                    <span v-if="event.duration" class="meta-item">
                      <i class="fas fa-clock"></i>
                      {{ event.duration }}
                    </span>
                    <span v-if="event.priority" class="meta-item priority" :class="`priority-${event.priority}`">
                      <i :class="getPriorityIcon(event.priority)"></i>
                      {{ event.priority }}
                    </span>
                  </div>
                </div>
                
                <div class="event-actions">
                  <button @click.stop="toggleEventComplete(event)" class="action-btn">
                    <i :class="event.completed ? 'fas fa-check-circle' : 'far fa-circle'"></i>
                  </button>
                  <button @click.stop="editEvent(event)" class="action-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click.stop="deleteEvent(event)" class="action-btn delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mini Calendário Lateral -->
    <div class="sidebar-calendar">
      <h3>Visão Geral</h3>
      <div class="mini-calendar">
        <div class="mini-header">
          <button @click="miniMonth--" class="mini-nav">
            <i class="fas fa-chevron-left"></i>
          </button>
          <span>{{ miniMonthYear }}</span>
          <button @click="miniMonth++" class="mini-nav">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <div class="mini-weekdays">
          <span v-for="day in ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']" :key="day">{{ day }}</span>
        </div>
        <div class="mini-days">
          <span 
            v-for="(day, index) in miniCalendarDays" 
            :key="index"
            :class="getMiniDayClass(day)"
            @click="goToDate(day.date)"
          >
            {{ day.date.getDate() }}
            <span v-if="day.eventCount > 0" class="event-dot"></span>
          </span>
        </div>
      </div>
      
      <!-- Resumo de Estatísticas -->
      <div class="calendar-stats">
        <h3>Resumo do Mês</h3>
        <div class="stat-item">
          <span class="stat-label">Total de Eventos</span>
          <span class="stat-value">{{ monthStats.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Concluídos</span>
          <span class="stat-value">{{ monthStats.completed }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Pendentes</span>
          <span class="stat-value">{{ monthStats.pending }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Taxa de Conclusão</span>
          <span class="stat-value">{{ monthStats.completionRate }}%</span>
        </div>
      </div>
      
      <!-- Legenda -->
      <div class="calendar-legend">
        <h3>Legenda</h3>
        <div class="legend-items">
          <div v-for="type in eventTypes" :key="type.id" class="legend-item">
            <span class="legend-color" :style="{ background: type.color }"></span>
            <span>{{ type.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Detalhes do Evento -->
    <transition name="modal">
      <div v-if="selectedEvent" class="event-modal-overlay" @click.self="selectedEvent = null">
        <div class="event-modal">
          <div class="modal-header" :style="{ borderColor: selectedEvent.color }">
            <h3>{{ selectedEvent.title }}</h3>
            <button @click="selectedEvent = null" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="event-info">
              <div class="info-item">
                <i class="fas fa-calendar-alt"></i>
                <span>{{ formatEventDate(selectedEvent.date) }}</span>
              </div>
              <div v-if="selectedEvent.time" class="info-item">
                <i class="fas fa-clock"></i>
                <span>{{ selectedEvent.time }}</span>
              </div>
              <div v-if="selectedEvent.subject" class="info-item">
                <i class="fas fa-tag"></i>
                <span>{{ selectedEvent.subject }}</span>
              </div>
              <div v-if="selectedEvent.priority" class="info-item">
                <i :class="getPriorityIcon(selectedEvent.priority)"></i>
                <span>Prioridade {{ selectedEvent.priority }}</span>
              </div>
            </div>
            
            <div v-if="selectedEvent.description" class="event-description">
              <h4>Descrição</h4>
              <p>{{ selectedEvent.description }}</p>
            </div>
            
            <div v-if="selectedEvent.checklist" class="event-checklist">
              <h4>Checklist</h4>
              <div 
                v-for="(item, index) in selectedEvent.checklist" 
                :key="index"
                class="checklist-item"
              >
                <input 
                  type="checkbox" 
                  :checked="item.completed"
                  @change="toggleChecklistItem(index)"
                />
                <span :class="{ completed: item.completed }">{{ item.text }}</span>
              </div>
            </div>
            
            <div v-if="selectedEvent.attachments" class="event-attachments">
              <h4>Anexos</h4>
              <div class="attachments-list">
                <a 
                  v-for="attachment in selectedEvent.attachments" 
                  :key="attachment.id"
                  :href="attachment.url"
                  class="attachment-item"
                  target="_blank"
                >
                  <i :class="getFileIcon(attachment.type)"></i>
                  <span>{{ attachment.name }}</span>
                </a>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button @click="editEvent(selectedEvent)" class="btn-edit">
              <i class="fas fa-edit"></i>
              Editar
            </button>
            <button @click="duplicateEvent(selectedEvent)" class="btn-duplicate">
              <i class="fas fa-copy"></i>
              Duplicar
            </button>
            <button @click="deleteEvent(selectedEvent)" class="btn-delete">
              <i class="fas fa-trash"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Modal de Criação/Edição Rápida -->
    <transition name="modal">
      <div v-if="showQuickCreate" class="quick-create-overlay" @click.self="showQuickCreate = false">
        <div class="quick-create-modal">
          <div class="modal-header">
            <h3>{{ editingEvent ? 'Editar Evento' : 'Novo Evento' }}</h3>
            <button @click="closeQuickCreate" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <form @submit.prevent="saveEvent" class="event-form">
            <div class="form-group">
              <label>Título</label>
              <input 
                v-model="eventForm.title" 
                type="text" 
                required
                placeholder="Título do evento"
              />
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>Data</label>
                <input 
                  v-model="eventForm.date" 
                  type="date" 
                  required
                />
              </div>
              <div class="form-group">
                <label>Hora</label>
                <input 
                  v-model="eventForm.time" 
                  type="time"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>Tipo</label>
                <select v-model="eventForm.type" required>
                  <option v-for="type in eventTypes" :key="type.id" :value="type.id">
                    {{ type.label }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Matéria</label>
                <select v-model="eventForm.subject">
                  <option value="">Selecione...</option>
                  <option v-for="subject in subjects" :key="subject" :value="subject">
                    {{ subject }}
                  </option>
                </select>
              </div>
            </div>
            
            <div class="form-group">
              <label>Prioridade</label>
              <div class="priority-selector">
                <button 
                  v-for="priority in priorities" 
                  :key="priority.value"
                  type="button"
                  @click="eventForm.priority = priority.value"
                  :class="['priority-btn', `priority-${priority.value}`, { active: eventForm.priority === priority.value }]"
                >
                  <i :class="priority.icon"></i>
                  {{ priority.label }}
                </button>
              </div>
            </div>
            
            <div class="form-group">
              <label>Descrição</label>
              <textarea 
                v-model="eventForm.description" 
                rows="3"
                placeholder="Adicione uma descrição..."
              ></textarea>
            </div>
            
            <div class="form-group">
              <label>
                <input 
                  type="checkbox" 
                  v-model="eventForm.recurring"
                />
                Evento recorrente
              </label>
            </div>
            
            <div v-if="eventForm.recurring" class="recurring-options">
              <div class="form-group">
                <label>Repetir</label>
                <select v-model="eventForm.recurrence.frequency">
                  <option value="daily">Diariamente</option>
                  <option value="weekly">Semanalmente</option>
                  <option value="monthly">Mensalmente</option>
                </select>
              </div>
              <div class="form-group">
                <label>Até</label>
                <input 
                  v-model="eventForm.recurrence.until" 
                  type="date"
                  :min="eventForm.date"
                />
              </div>
            </div>
            
            <div class="form-actions">
              <button type="button" @click="closeQuickCreate" class="btn-cancel">
                Cancelar
              </button>
              <button type="submit" class="btn-save">
                <i class="fas fa-save"></i>
                {{ editingEvent ? 'Salvar' : 'Criar' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </transition>

    <!-- Day Details Modal -->
    <transition name="modal">
      <div v-if="dayDetailsModal" class="day-details-overlay" @click.self="dayDetailsModal = null">
        <div class="day-details-modal">
          <div class="modal-header">
            <h3>{{ formatDayDetailsDate(dayDetailsModal.date) }}</h3>
            <button @click="dayDetailsModal = null" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="day-summary">
              <div class="summary-stat">
                <i class="fas fa-calendar-check"></i>
                <span>{{ dayDetailsModal.events.length }} eventos</span>
              </div>
              <div class="summary-stat">
                <i class="fas fa-check-circle"></i>
                <span>{{ getDayCompletedCount(dayDetailsModal) }} concluídos</span>
              </div>
            </div>
            
            <div class="day-events-list">
              <div 
                v-for="event in dayDetailsModal.events" 
                :key="event.id"
                @click="selectEvent(event)"
                class="day-event-item"
                :style="{ borderColor: event.color }"
              >
                <div class="event-header">
                  <span class="event-time" v-if="event.time">{{ event.time }}</span>
                  <span class="event-type">{{ getEventTypeLabel(event.type) }}</span>
                </div>
                <h4>{{ event.title }}</h4>
                <div v-if="event.subject" class="event-subject">
                  <i class="fas fa-tag"></i>
                  {{ event.subject }}
                </div>
              </div>
            </div>
            
            <button @click="quickAddEvent(dayDetailsModal)" class="add-event-btn">
              <i class="fas fa-plus"></i>
              Adicionar Evento
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const emit = defineEmits(['event-selected', 'event-created', 'event-updated', 'event-deleted'])

// Props
const props = defineProps({
  events: {
    type: Array,
    default: () => []
  },
  selectedDate: Date
})

// Estado
const currentDate = ref(new Date())
const selectedDate = ref(props.selectedDate || new Date())
const currentView = ref('month')
const showFilters = ref(false)
const showMonthPicker = ref(false)
const selectedEvent = ref(null)
const showQuickCreate = ref(false)
const editingEvent = ref(null)
const dayDetailsModal = ref(null)
const draggedEvent = ref(null)

// Filtros
const selectedTypes = ref([])
const selectedSubjects = ref([])
const selectedPriorities = ref([])

// Mini Calendar
const miniMonth = ref(0)

// Form
const eventForm = ref({
  title: '',
  date: '',
  time: '',
  type: 'revision',
  subject: '',
  priority: 'medium',
  description: '',
  recurring: false,
  recurrence: {
    frequency: 'weekly',
    until: ''
  }
})

// Configurações
const views = [
  { id: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
  { id: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
  { id: 'list', label: 'Lista', icon: 'fas fa-list' }
]

const eventTypes = [
  { id: 'revision', label: 'Revisão', color: '#667eea', icon: 'fas fa-redo' },
  { id: 'study', label: 'Estudo', color: '#764ba2', icon: 'fas fa-book' },
  { id: 'questions', label: 'Questões', color: '#f093fb', icon: 'fas fa-question-circle' },
  { id: 'exam', label: 'Prova', color: '#f5576c', icon: 'fas fa-file-alt' },
  { id: 'deadline', label: 'Prazo', color: '#f59e0b', icon: 'fas fa-clock' }
]

const subjects = ['Anatomia', 'Fisiologia', 'Patologia', 'Farmacologia', 'Bioquímica', 'Microbiologia']

const priorities = [
  { value: 'high', label: 'Alta', icon: 'fas fa-exclamation-circle' },
  { value: 'medium', label: 'Média', icon: 'fas fa-minus-circle' },
  { value: 'low', label: 'Baixa', icon: 'fas fa-info-circle' }
]

const weekdays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
const months = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 
                'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro']

// Computed
const currentMonthYear = computed(() => {
  return `${months[currentDate.value.getMonth()]} ${currentDate.value.getFullYear()}`
})

const selectedMonth = computed(() => currentDate.value.getMonth())
const selectedYear = ref(currentDate.value.getFullYear())

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startingDayOfWeek = firstDay.getDay()
  
  const days = []
  
  // Dias do mês anterior
  const prevMonth = month === 0 ? 11 : month - 1
  const prevYear = month === 0 ? year - 1 : year
  const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate()
  
  for (let i = startingDayOfWeek - 1; i >= 0; i--) {
    const date = new Date(prevYear, prevMonth, daysInPrevMonth - i)
    days.push(createDayObject(date, false))
  }
  
  // Dias do mês atual
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i)
    days.push(createDayObject(date, true))
  }
  
  // Dias do próximo mês
  const remainingDays = 42 - days.length
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i)
    days.push(createDayObject(date, false))
  }
  
  return days
})

const weekDays = computed(() => {
  const startOfWeek = new Date(selectedDate.value)
  startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay())
  
  const days = []
  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)
    days.push({
      date,
      events: getEventsForDate(date)
    })
  }
  
  return days
})

const listPeriod = ref('week')

const groupedEvents = computed(() => {
  const filtered = filteredEvents.value
  const grouped = {}
  
  filtered.forEach(event => {
    const dateKey = event.date.toDateString()
    if (!grouped[dateKey]) {
      grouped[dateKey] = []
    }
    grouped[dateKey].push(event)
  })
  
  // Ordenar por data
  const sortedDates = Object.keys(grouped).sort((a, b) => new Date(a) - new Date(b))
  const result = {}
  sortedDates.forEach(date => {
    result[date] = grouped[date].sort((a, b) => {
      if (a.time && b.time) return a.time.localeCompare(b.time)
      return 0
    })
  })
  
  return result
})

const filteredEvents = computed(() => {
  let events = [...props.events]
  
  if (selectedTypes.value.length > 0) {
    events = events.filter(e => selectedTypes.value.includes(e.type))
  }
  
  if (selectedSubjects.value.length > 0) {
    events = events.filter(e => selectedSubjects.value.includes(e.subject))
  }
  
  if (selectedPriorities.value.length > 0) {
    events = events.filter(e => selectedPriorities.value.includes(e.priority))
  }
  
  return events
})

const activeFiltersCount = computed(() => {
  return selectedTypes.value.length + selectedSubjects.value.length + selectedPriorities.value.length
})

const miniMonthYear = computed(() => {
  const date = new Date()
  date.setMonth(date.getMonth() + miniMonth.value)
  return `${months[date.getMonth()].substring(0, 3)} ${date.getFullYear()}`
})

const miniCalendarDays = computed(() => {
  const date = new Date()
  date.setMonth(date.getMonth() + miniMonth.value)
  const year = date.getFullYear()
  const month = date.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startingDayOfWeek = firstDay.getDay()
  
  const days = []
  
  // Dias vazios no início
  for (let i = 0; i < startingDayOfWeek; i++) {
    days.push({ date: null, eventCount: 0 })
  }
  
  // Dias do mês
  for (let i = 1; i <= daysInMonth; i++) {
    const dayDate = new Date(year, month, i)
    const eventCount = getEventsForDate(dayDate).length
    days.push({ date: dayDate, eventCount })
  }
  
  return days
})

const monthStats = computed(() => {
  const events = getEventsForMonth(currentDate.value)
  const completed = events.filter(e => e.completed).length
  const total = events.length
  
  return {
    total,
    completed,
    pending: total - completed,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
  }
})

// Methods
const createDayObject = (date, isCurrentMonth) => {
  const events = getEventsForDate(date)
  const indicators = []
  
  if (events.some(e => e.priority === 'high')) indicators.push('high')
  if (events.some(e => e.type === 'exam')) indicators.push('exam')
  if (events.some(e => e.type === 'deadline')) indicators.push('deadline')
  
  return {
    date,
    isCurrentMonth,
    isToday: isToday(date),
    isSelected: isSameDay(date, selectedDate.value),
    isWeekend: date.getDay() === 0 || date.getDay() === 6,
    events,
    indicators
  }
}

const getEventsForDate = (date) => {
  return filteredEvents.value.filter(event => isSameDay(event.date, date))
}

const getEventsForMonth = (date) => {
  return filteredEvents.value.filter(event => 
    event.date.getMonth() === date.getMonth() && 
    event.date.getFullYear() === date.getFullYear()
  )
}

const getVisibleEvents = (events) => {
  return events.slice(0, 3)
}

const getEventsAtTime = (events, hour) => {
  return events.filter(event => {
    if (!event.time) return false
    const eventHour = parseInt(event.time.split(':')[0])
    return eventHour === hour
  })
}

const getEventStyle = (event) => {
  const type = eventTypes.find(t => t.id === event.type)
  return {
    backgroundColor: type?.color || '#667eea',
    color: 'white',
    height: `${(event.duration || 60) / 60 * 60}px`
  }
}

const isToday = (date) => {
  const today = new Date()
  return isSameDay(date, today)
}

const isSameDay = (date1, date2) => {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear()
}

const getDayClasses = (day) => {
  return [
    'calendar-day',
    {
      'other-month': !day.isCurrentMonth,
      'today': day.isToday,
      'selected': day.isSelected,
      'weekend': day.isWeekend,
      'has-events': day.events.length > 0
    }
  ]
}

const getMiniDayClass = (day) => {
  if (!day.date) return 'empty'
  
  const classes = ['mini-day']
  if (isToday(day.date)) classes.push('today')
  if (isSameDay(day.date, selectedDate.value)) classes.push('selected')
  if (day.eventCount > 0) classes.push('has-events')
  
  return classes
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

const goToToday = () => {
  currentDate.value = new Date()
  selectedDate.value = new Date()
}

const selectMonth = (monthIndex) => {
  const newDate = new Date(selectedYear.value, monthIndex, 1)
  currentDate.value = newDate
  showMonthPicker.value = false
}

const selectDate = (day) => {
  if (day.isCurrentMonth) {
    selectedDate.value = day.date
  }
}

const selectEvent = (event) => {
  selectedEvent.value = event
  emit('event-selected', event)
}

const quickAddEvent = (day) => {
  eventForm.value = {
    title: '',
    date: day.date.toISOString().split('T')[0],
    time: '',
    type: 'revision',
    subject: '',
    priority: 'medium',
    description: '',
    recurring: false,
    recurrence: {
      frequency: 'weekly',
      until: ''
    }
  }
  showQuickCreate.value = true
}

const editEvent = (event) => {
  editingEvent.value = event
  eventForm.value = {
    ...event,
    date: event.date.toISOString().split('T')[0]
  }
  showQuickCreate.value = true
  selectedEvent.value = null
}

const duplicateEvent = (event) => {
  const newEvent = {
    ...event,
    id: Date.now(),
    title: `${event.title} (cópia)`
  }
  emit('event-created', newEvent)
  selectedEvent.value = null
}

const deleteEvent = (event) => {
  if (confirm('Tem certeza que deseja excluir este evento?')) {
    emit('event-deleted', event)
    selectedEvent.value = null
  }
}

const toggleEventComplete = (event) => {
  event.completed = !event.completed
  emit('event-updated', event)
}

const saveEvent = () => {
  const eventData = {
    ...eventForm.value,
    date: new Date(eventForm.value.date),
    color: eventTypes.find(t => t.id === eventForm.value.type)?.color || '#667eea'
  }
  
  if (editingEvent.value) {
    emit('event-updated', { ...editingEvent.value, ...eventData })
  } else {
    emit('event-created', { ...eventData, id: Date.now(), completed: false })
  }
  
  closeQuickCreate()
}

const closeQuickCreate = () => {
  showQuickCreate.value = false
  editingEvent.value = null
  eventForm.value = {
    title: '',
    date: '',
    time: '',
    type: 'revision',
    subject: '',
    priority: 'medium',
    description: '',
    recurring: false,
    recurrence: {
      frequency: 'weekly',
      until: ''
    }
  }
}

const clearFilters = () => {
  selectedTypes.value = []
  selectedSubjects.value = []
  selectedPriorities.value = []
}

const showDayDetails = (day) => {
  dayDetailsModal.value = day
}

const goToDate = (date) => {
  if (date) {
    currentDate.value = new Date(date)
    selectedDate.value = new Date(date)
  }
}

const createEventAtTime = (date, hour) => {
  eventForm.value = {
    title: '',
    date: date.toISOString().split('T')[0],
    time: `${hour.toString().padStart(2, '0')}:00`,
    type: 'revision',
    subject: '',
    priority: 'medium',
    description: '',
    recurring: false,
    recurrence: {
      frequency: 'weekly',
      until: ''
    }
  }
  showQuickCreate.value = true
}

// Drag and Drop
const handleDragStart = (event, eventData) => {
  draggedEvent.value = eventData
  event.dataTransfer.effectAllowed = 'move'
}

const handleDrop = (event, day) => {
  event.preventDefault()
  if (draggedEvent.value && day.isCurrentMonth) {
    const updatedEvent = {
      ...draggedEvent.value,
      date: day.date
    }
    emit('event-updated', updatedEvent)
    draggedEvent.value = null
  }
}

const handleDropTime = (event, date, hour) => {
  event.preventDefault()
  if (draggedEvent.value) {
    const updatedEvent = {
      ...draggedEvent.value,
      date,
      time: `${hour.toString().padStart(2, '0')}:00`
    }
    emit('event-updated', updatedEvent)
    draggedEvent.value = null
  }
}

// Formatting
const formatDate = (date) => {
  return date.toLocaleDateString('pt-BR')
}

const formatEventDate = (date) => {
  return date.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

const formatWeekDay = (date) => {
  return date.toLocaleDateString('pt-BR', { weekday: 'short' })
}

const formatHour = (hour) => {
  return `${hour.toString().padStart(2, '0')}:00`
}

const formatListDate = (dateString) => {
  const date = new Date(dateString)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  if (isSameDay(date, today)) return 'Hoje'
  if (isSameDay(date, tomorrow)) return 'Amanhã'
  
  return date.toLocaleDateString('pt-BR', { weekday: 'long', day: 'numeric', month: 'long' })
}

const formatDayDetailsDate = (date) => {
  return date.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long', 
    year: 'numeric' 
  })
}

// Helpers
const getEventIcon = (type) => {
  const eventType = eventTypes.find(t => t.id === type)
  return eventType?.icon || 'fas fa-calendar'
}

const getEventTypeLabel = (type) => {
  const eventType = eventTypes.find(t => t.id === type)
  return eventType?.label || type
}

const getPriorityIcon = (priority) => {
  const p = priorities.find(pr => pr.value === priority)
  return p?.icon || 'fas fa-minus-circle'
}

const getFileIcon = (type) => {
  const icons = {
    pdf: 'fas fa-file-pdf',
    doc: 'fas fa-file-word',
    xls: 'fas fa-file-excel',
    ppt: 'fas fa-file-powerpoint',
    img: 'fas fa-file-image',
    video: 'fas fa-file-video',
    audio: 'fas fa-file-audio'
  }
  return icons[type] || 'fas fa-file'
}

const getDayCompletedCount = (day) => {
  return day.events.filter(e => e.completed).length
}

const toggleChecklistItem = (index) => {
  if (selectedEvent.value?.checklist) {
    selectedEvent.value.checklist[index].completed = !selectedEvent.value.checklist[index].completed
    emit('event-updated', selectedEvent.value)
  }
}

// Lifecycle
onMounted(() => {
  // Inicializar calendário
})

// Watchers
watch(() => props.selectedDate, (newDate) => {
  if (newDate) {
    selectedDate.value = newDate
    currentDate.value = new Date(newDate)
  }
})
</script>

<style scoped>
.enhanced-calendar {
  display: flex;
  gap: 2rem;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 16px;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.current-month {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-month h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.month-selector {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.month-selector:hover {
  color: #667eea;
}

.calendar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.today-btn {
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.today-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.view-selector {
  display: flex;
  gap: 0.25rem;
  background: white;
  padding: 0.25rem;
  border-radius: 8px;
  border: 2px solid var(--border-color, #e5e7eb);
}

.view-btn {
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-btn:hover {
  background: var(--surface-light, #f8f9fa);
}

.view-btn.active {
  background: #667eea;
  color: white;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Calendar Filters */
.calendar-filters {
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.filter-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.filter-option:hover {
  background: var(--surface-light, #f8f9fa);
}

.filter-option input[type="checkbox"] {
  cursor: pointer;
}

.filter-indicator {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.priority-indicator {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.priority-indicator.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.priority-indicator.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.priority-indicator.priority-low {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.clear-filters-btn {
  grid-column: 1 / -1;
  padding: 0.5rem 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.clear-filters-btn:hover {
  border-color: #ef4444;
  color: #ef4444;
}

/* Calendar Container */
.calendar-container {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Month View */
.month-view {
  width: 100%;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 0.5rem;
}

.weekday {
  text-align: center;
  font-weight: 600;
  color: var(--text-secondary);
  padding: 0.75rem 0;
  font-size: 0.875rem;
  text-transform: uppercase;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border-color, #e5e7eb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  overflow: hidden;
}

.calendar-day {
  background: white;
  min-height: 100px;
  padding: 0.5rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-day:hover {
  background: var(--surface-light, #f8f9fa);
}

.calendar-day.other-month {
  opacity: 0.4;
}

.calendar-day.today {
  background: rgba(102, 126, 234, 0.05);
}

.calendar-day.selected {
  background: rgba(102, 126, 234, 0.1);
  box-shadow: inset 0 0 0 2px #667eea;
}

.calendar-day.weekend {
  background: var(--surface-light, #f8f9fa);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 0.5rem;
}

.day-number {
  font-weight: 600;
  color: var(--text-primary);
}

.calendar-day.today .day-number {
  background: #667eea;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-indicators {
  display: flex;
  gap: 0.25rem;
}

.indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.indicator.high {
  background: #ef4444;
}

.indicator.exam {
  background: #f5576c;
}

.indicator.deadline {
  background: #f59e0b;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.event-item {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-item:hover {
  transform: translateX(2px);
}

.event-time {
  font-weight: 600;
  font-size: 0.7rem;
}

.event-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.completed-icon {
  color: #10b981;
  font-size: 0.75rem;
}

.more-events {
  background: none;
  border: none;
  color: #667eea;
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0.125rem 0.25rem;
  transition: color 0.2s ease;
}

.more-events:hover {
  color: #5a67d8;
}

.quick-add-btn {
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  background: #667eea;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
}

.calendar-day:hover .quick-add-btn,
.calendar-day.selected .quick-add-btn {
  opacity: 1;
}

.quick-add-btn:hover {
  transform: scale(1.1);
}

/* Week View */
.week-view {
  width: 100%;
  overflow-x: auto;
}

.week-header {
  display: grid;
  grid-template-columns: 60px repeat(7, 1fr);
  border-bottom: 2px solid var(--border-color, #e5e7eb);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.time-column {
  /* Empty */
}

.week-day-header {
  text-align: center;
  padding: 0.5rem;
}

.week-day-header.today {
  color: #667eea;
  font-weight: 600;
}

.week-day-name {
  display: block;
  font-size: 0.875rem;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
}

.week-day-number {
  font-size: 1.25rem;
  font-weight: 600;
}

.week-content {
  position: relative;
}

.time-slots {
  display: flex;
  flex-direction: column;
}

.time-slot {
  display: grid;
  grid-template-columns: 60px 1fr;
  height: 60px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.time-label {
  padding: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: right;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border-color, #e5e7eb);
}

.time-cell {
  background: white;
  position: relative;
  cursor: pointer;
  transition: background 0.2s ease;
}

.time-cell:hover {
  background: var(--surface-light, #f8f9fa);
}

.time-event {
  position: absolute;
  inset: 2px;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-event:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* List View */
.list-view {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.list-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.period-select {
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.event-group {
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.group-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.event-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.group-events {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.list-event {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.list-event:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-event.completed {
  opacity: 0.6;
}

.list-event.completed .event-details h5 {
  text-decoration: line-through;
}

.event-time-block {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  flex-shrink: 0;
}

.event-time-block i {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.event-details {
  flex: 1;
}

.event-details h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.event-details p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.event-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);
}

.meta-item i {
  font-size: 0.75rem;
}

.meta-item.priority {
  font-weight: 500;
}

.event-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: #667eea;
}

.action-btn.delete:hover {
  color: #ef4444;
}

/* Sidebar */
.sidebar-calendar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-calendar h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

/* Mini Calendar */
.mini-calendar {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mini-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mini-nav {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.mini-nav:hover {
  color: #667eea;
}

.mini-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 0.5rem;
  text-align: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.mini-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.mini-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.mini-day:hover {
  background: var(--surface-light, #f8f9fa);
}

.mini-day.empty {
  cursor: default;
}

.mini-day.today {
  background: #667eea;
  color: white;
  font-weight: 600;
}

.mini-day.selected {
  box-shadow: inset 0 0 0 2px #667eea;
}

.mini-day .event-dot {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #667eea;
  border-radius: 50%;
}

.mini-day.today .event-dot {
  background: white;
}

/* Calendar Stats */
.calendar-stats {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Calendar Legend */
.calendar-legend {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

/* Month Picker */
.month-picker-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.month-picker {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.year-display {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.year-nav {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s ease;
}

.year-nav:hover {
  color: #667eea;
}

.months-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.month-btn {
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.month-btn:hover {
  border-color: #667eea;
}

.month-btn.active {
  background: #667eea;
  color: white;
}

.month-btn.current {
  border-color: #667eea;
}

/* Event Modal */
.event-modal-overlay,
.quick-create-overlay,
.day-details-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.event-modal,
.quick-create-modal,
.day-details-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 2px solid var(--border-color, #e5e7eb);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.event-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
}

.info-item i {
  width: 20px;
  text-align: center;
  color: #667eea;
}

.event-description,
.event-checklist,
.event-attachments {
  margin-bottom: 1.5rem;
}

.event-description h4,
.event-checklist h4,
.event-attachments h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.event-description p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.checklist-item input[type="checkbox"] {
  cursor: pointer;
}

.checklist-item span {
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.checklist-item span.completed {
  text-decoration: line-through;
  color: var(--text-secondary);
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.attachment-item:hover {
  background: #667eea;
  color: white;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 2px solid var(--border-color, #e5e7eb);
}

.btn-edit,
.btn-duplicate,
.btn-delete {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-edit {
  background: #667eea;
  color: white;
}

.btn-edit:hover {
  background: #5a67d8;
}

.btn-duplicate {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-duplicate:hover {
  background: #667eea;
  color: white;
}

.btn-delete {
  background: white;
  color: #ef4444;
  border: 2px solid #ef4444;
}

.btn-delete:hover {
  background: #ef4444;
  color: white;
}

/* Event Form */
.event-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.priority-selector {
  display: flex;
  gap: 0.5rem;
}

.priority-btn {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid var(--border-color, #e5e7eb);
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.priority-btn:hover {
  transform: translateY(-1px);
}

.priority-btn.priority-high {
  border-color: #ef4444;
  color: #ef4444;
}

.priority-btn.priority-high.active {
  background: #ef4444;
  color: white;
}

.priority-btn.priority-medium {
  border-color: #f59e0b;
  color: #f59e0b;
}

.priority-btn.priority-medium.active {
  background: #f59e0b;
  color: white;
}

.priority-btn.priority-low {
  border-color: #3b82f6;
  color: #3b82f6;
}

.priority-btn.priority-low.active {
  background: #3b82f6;
  color: white;
}

.recurring-options {
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 2px solid var(--border-color, #e5e7eb);
}

.btn-cancel,
.btn-save {
  flex: 1;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
}

.btn-cancel:hover {
  border-color: #667eea;
  color: #667eea;
}

.btn-save {
  background: #667eea;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-save:hover {
  background: #5a67d8;
}

/* Day Details Modal */
.day-summary {
  display: flex;
  justify-content: space-around;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--border-color, #e5e7eb);
}

.summary-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.summary-stat i {
  color: #667eea;
}

.day-events-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.day-event-item {
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border-left: 4px solid;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.day-event-item:hover {
  transform: translateX(5px);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.day-event-item h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.event-subject {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.add-event-btn {
  width: 100%;
  padding: 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-event-btn:hover {
  background: #5a67d8;
}

/* Transitions */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .event-modal,
.modal-enter-active .quick-create-modal,
.modal-enter-active .day-details-modal,
.modal-enter-active .month-picker {
  transition: transform 0.3s ease;
}

.modal-enter-from .event-modal,
.modal-enter-from .quick-create-modal,
.modal-enter-from .day-details-modal,
.modal-enter-from .month-picker {
  transform: scale(0.9);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .enhanced-calendar {
    color: #ffffff;
  }

  .calendar-header,
  .calendar-container,
  .mini-calendar,
  .calendar-stats,
  .calendar-legend,
  .event-modal,
  .quick-create-modal,
  .day-details-modal,
  .month-picker {
    background: #1a1a1a;
  }

  .nav-btn,
  .view-btn,
  .filter-btn,
  .calendar-filters,
  .calendar-day,
  .time-cell,
  .event-group,
  .list-event,
  .form-group input,
  .form-group select,
  .form-group textarea,
  .priority-btn {
    background: #2a2a2a;
    border-color: #444444;
    color: #ffffff;
  }

  .calendar-day.weekend,
  .event-item,
  .day-event-item,
  .attachment-item {
    background: #333333;
  }

  .days-grid {
    background: #444444;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .enhanced-calendar {
    flex-direction: column;
  }

  .sidebar-calendar {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .sidebar-calendar > * {
    flex: 1;
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    gap: 1rem;
  }

  .calendar-nav {
    width: 100%;
    justify-content: space-between;
  }

  .calendar-actions {
    width: 100%;
    flex-wrap: wrap;
  }

  .view-selector {
    order: -1;
    width: 100%;
  }

  .calendar-day {
    min-height: 80px;
  }

  .day-events {
    display: none;
  }

  .calendar-day.selected .day-events {
    display: flex;
  }

  .week-view {
    display: none;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .priority-selector {
    flex-direction: column;
  }

  .recurring-options {
    grid-template-columns: 1fr;
  }
}
</style>