<template>
  <div class="quick-questions-system">
    <!-- Header com animação -->
    <div class="qqs-header">
      <div class="header-background">
        <div class="pulse-effect"></div>
        <div class="gradient-overlay"></div>
      </div>
      
      <div class="header-content">
        <div class="header-top">
          <h2 class="system-title">
            <i class="fas fa-bolt"></i>
            Sistema de Questões Rápidas
          </h2>
          <button @click="$emit('close')" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="session-stats">
          <div class="stat-item">
            <i class="fas fa-fire"></i>
            <span>{{ currentStreak }} dias</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-trophy"></i>
            <span>{{ totalPoints }} pontos</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-percentage"></i>
            <span>{{ sessionAccuracy }}% acerto</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Seleção de Modo -->
    <div v-if="!sessionActive" class="mode-selection">
      <h3 class="section-title">Escolha o Modo de Estudo</h3>
      
      <div class="modes-grid">
        <!-- Modo Sprint -->
        <div class="mode-card" @click="startMode('sprint')">
          <div class="mode-icon sprint">
            <i class="fas fa-running"></i>
          </div>
          <h4>Sprint</h4>
          <p>10 questões rápidas</p>
          <div class="mode-benefits">
            <span><i class="fas fa-clock"></i> 5-10 min</span>
            <span><i class="fas fa-star"></i> 100 pts</span>
          </div>
        </div>

        <!-- Modo Maratona -->
        <div class="mode-card" @click="startMode('marathon')">
          <div class="mode-icon marathon">
            <i class="fas fa-route"></i>
          </div>
          <h4>Maratona</h4>
          <p>50 questões completas</p>
          <div class="mode-benefits">
            <span><i class="fas fa-clock"></i> 30-45 min</span>
            <span><i class="fas fa-star"></i> 500 pts</span>
          </div>
        </div>

        <!-- Modo Revisão Espaçada -->
        <div class="mode-card" @click="startMode('spaced')">
          <div class="mode-icon spaced">
            <i class="fas fa-brain"></i>
          </div>
          <h4>Revisão Espaçada</h4>
          <p>Baseado no algoritmo SM-2</p>
          <div class="mode-benefits">
            <span><i class="fas fa-clock"></i> Variável</span>
            <span><i class="fas fa-star"></i> 2x pts</span>
          </div>
        </div>

        <!-- Modo Desafio -->
        <div class="mode-card premium" @click="startMode('challenge')">
          <div class="mode-icon challenge">
            <i class="fas fa-medal"></i>
          </div>
          <h4>Desafio Diário</h4>
          <p>Questões difíceis selecionadas</p>
          <div class="mode-benefits">
            <span><i class="fas fa-clock"></i> 15-20 min</span>
            <span><i class="fas fa-star"></i> 3x pts</span>
          </div>
          <div class="premium-badge">PRO</div>
        </div>
      </div>

      <!-- Filtros Avançados -->
      <div class="advanced-filters">
        <h3 class="section-title">Personalizar Sessão</h3>
        
        <div class="filters-grid">
          <div class="filter-group">
            <label>Matéria</label>
            <select v-model="selectedSubject" class="filter-select">
              <option value="">Todas as Matérias</option>
              <option v-for="subject in subjects" :key="subject">{{ subject }}</option>
            </select>
          </div>
          
          <div class="filter-group">
            <label>Dificuldade</label>
            <div class="difficulty-selector">
              <button 
                v-for="level in difficultyLevels" 
                :key="level.value"
                @click="selectedDifficulty = level.value"
                :class="['difficulty-btn', { active: selectedDifficulty === level.value }]"
              >
                <i :class="level.icon"></i>
                {{ level.label }}
              </button>
            </div>
          </div>
          
          <div class="filter-group">
            <label>Tipo de Questão</label>
            <div class="type-selector">
              <label v-for="type in questionTypes" :key="type.value" class="type-option">
                <input 
                  type="checkbox" 
                  :value="type.value" 
                  v-model="selectedTypes"
                />
                <span>{{ type.label }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Recomendações Inteligentes -->
      <div class="ai-recommendations">
        <h3 class="section-title">
          <i class="fas fa-magic"></i>
          Recomendações da IA
        </h3>
        
        <div class="recommendations-grid">
          <div 
            v-for="rec in aiRecommendations" 
            :key="rec.id"
            @click="applyRecommendation(rec)"
            class="recommendation-card"
            :class="rec.type"
          >
            <div class="rec-icon">
              <i :class="rec.icon"></i>
            </div>
            <div class="rec-content">
              <h4>{{ rec.title }}</h4>
              <p>{{ rec.description }}</p>
              <span class="rec-reason">{{ rec.reason }}</span>
            </div>
            <div class="rec-action">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sessão Ativa -->
    <div v-else class="active-session">
      <!-- Progress Bar -->
      <div class="session-progress">
        <div class="progress-info">
          <span>Questão {{ currentQuestionIndex + 1 }} de {{ totalQuestions }}</span>
          <span class="timer" :class="{ warning: timeRemaining < 30 }">
            <i class="fas fa-clock"></i>
            {{ formatTime(timeRemaining) }}
          </span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: progressPercentage + '%' }"
          ></div>
          <div class="progress-markers">
            <div 
              v-for="i in totalQuestions" 
              :key="i"
              class="marker"
              :class="{
                completed: i <= currentQuestionIndex,
                current: i === currentQuestionIndex + 1,
                correct: questionResults[i-1] === true,
                incorrect: questionResults[i-1] === false
              }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Questão Atual -->
      <div class="question-container" :key="currentQuestion.id">
        <div class="question-header">
          <div class="question-meta">
            <span class="subject-tag">
              <i :class="getSubjectIcon(currentQuestion.subject)"></i>
              {{ currentQuestion.subject }}
            </span>
            <span class="difficulty-tag" :class="`difficulty-${currentQuestion.difficulty}`">
              {{ getDifficultyLabel(currentQuestion.difficulty) }}
            </span>
            <span class="points-tag">
              <i class="fas fa-star"></i>
              {{ currentQuestion.points }} pts
            </span>
          </div>
          
          <div class="question-actions">
            <button @click="flagQuestion" class="action-btn" :class="{ active: currentQuestion.flagged }">
              <i class="fas fa-flag"></i>
            </button>
            <button @click="showHint" class="action-btn" :disabled="hintsUsed >= maxHints">
              <i class="fas fa-lightbulb"></i>
              {{ maxHints - hintsUsed }}
            </button>
          </div>
        </div>

        <div class="question-content">
          <h3 class="question-text">{{ currentQuestion.question }}</h3>
          
          <!-- Imagem da questão se houver -->
          <div v-if="currentQuestion.image" class="question-image">
            <img :src="currentQuestion.image" :alt="currentQuestion.imageAlt" />
          </div>

          <!-- Dica se ativada -->
          <transition name="slide-down">
            <div v-if="showHintContent" class="hint-box">
              <i class="fas fa-lightbulb"></i>
              <p>{{ currentQuestion.hint }}</p>
            </div>
          </transition>

          <!-- Opções de Resposta -->
          <div class="answer-options">
            <div 
              v-for="(option, index) in currentQuestion.options" 
              :key="index"
              @click="selectAnswer(index)"
              class="option-card"
              :class="{
                selected: selectedAnswer === index,
                correct: showResult && index === currentQuestion.correctAnswer,
                incorrect: showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer,
                disabled: showResult
              }"
            >
              <div class="option-letter">{{ String.fromCharCode(65 + index) }}</div>
              <div class="option-content">
                <p>{{ option.text }}</p>
                <transition name="fade">
                  <div v-if="showResult && index === currentQuestion.correctAnswer" class="explanation">
                    <i class="fas fa-info-circle"></i>
                    {{ option.explanation }}
                  </div>
                </transition>
              </div>
              <div class="option-feedback">
                <i v-if="showResult && index === currentQuestion.correctAnswer" class="fas fa-check-circle"></i>
                <i v-else-if="showResult && selectedAnswer === index" class="fas fa-times-circle"></i>
              </div>
            </div>
          </div>

          <!-- Botão de Confirmação -->
          <div class="question-footer">
            <button 
              v-if="!showResult"
              @click="confirmAnswer"
              :disabled="selectedAnswer === null"
              class="confirm-btn"
            >
              <i class="fas fa-check"></i>
              Confirmar Resposta
            </button>
            <button 
              v-else
              @click="nextQuestion"
              class="next-btn"
            >
              <i class="fas fa-arrow-right"></i>
              {{ currentQuestionIndex < totalQuestions - 1 ? 'Próxima Questão' : 'Finalizar' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Feedback Visual -->
      <transition name="bounce">
        <div v-if="showFeedback" class="feedback-overlay" :class="feedbackType">
          <div class="feedback-content">
            <i :class="feedbackIcon"></i>
            <h2>{{ feedbackMessage }}</h2>
            <p>{{ feedbackSubMessage }}</p>
            <div class="feedback-points">+{{ feedbackPoints }} pontos</div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Resultados da Sessão -->
    <div v-if="showResults" class="session-results">
      <div class="results-header">
        <h2>Sessão Concluída!</h2>
        <div class="results-summary">
          <div class="summary-stat">
            <i class="fas fa-percentage"></i>
            <span class="stat-value">{{ finalAccuracy }}%</span>
            <span class="stat-label">Taxa de Acerto</span>
          </div>
          <div class="summary-stat">
            <i class="fas fa-clock"></i>
            <span class="stat-value">{{ formatTime(sessionDuration) }}</span>
            <span class="stat-label">Tempo Total</span>
          </div>
          <div class="summary-stat">
            <i class="fas fa-star"></i>
            <span class="stat-value">{{ totalSessionPoints }}</span>
            <span class="stat-label">Pontos Ganhos</span>
          </div>
        </div>
      </div>

      <!-- Conquistas Desbloqueadas -->
      <div v-if="unlockedAchievements.length > 0" class="achievements-section">
        <h3>Conquistas Desbloqueadas!</h3>
        <div class="achievements-grid">
          <div 
            v-for="achievement in unlockedAchievements" 
            :key="achievement.id"
            class="achievement-card"
          >
            <div class="achievement-icon" :style="{ backgroundColor: achievement.color }">
              <i :class="achievement.icon"></i>
            </div>
            <div class="achievement-info">
              <h4>{{ achievement.name }}</h4>
              <p>{{ achievement.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Análise de Desempenho -->
      <div class="performance-analysis">
        <h3>Análise de Desempenho</h3>
        
        <div class="analysis-grid">
          <!-- Gráfico por Matéria -->
          <div class="analysis-card">
            <h4>Desempenho por Matéria</h4>
            <canvas ref="subjectPerformanceChart"></canvas>
          </div>
          
          <!-- Gráfico por Dificuldade -->
          <div class="analysis-card">
            <h4>Taxa de Acerto por Dificuldade</h4>
            <canvas ref="difficultyChart"></canvas>
          </div>
          
          <!-- Tempo Médio -->
          <div class="analysis-card">
            <h4>Tempo Médio por Questão</h4>
            <div class="time-analysis">
              <div 
                v-for="stat in timeStats" 
                :key="stat.label"
                class="time-stat"
              >
                <span class="time-label">{{ stat.label }}</span>
                <div class="time-bar">
                  <div 
                    class="time-fill" 
                    :style="{ width: stat.percentage + '%' }"
                    :class="stat.class"
                  ></div>
                </div>
                <span class="time-value">{{ stat.value }}s</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Questões para Revisar -->
      <div v-if="questionsToReview.length > 0" class="review-section">
        <h3>Questões para Revisar</h3>
        <div class="review-list">
          <div 
            v-for="question in questionsToReview" 
            :key="question.id"
            @click="reviewQuestion(question)"
            class="review-item"
          >
            <div class="review-icon" :class="question.flagged ? 'flagged' : 'incorrect'">
              <i :class="question.flagged ? 'fas fa-flag' : 'fas fa-times'"></i>
            </div>
            <div class="review-content">
              <p>{{ question.question }}</p>
              <span class="review-meta">{{ question.subject }} - {{ question.topic }}</span>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>

      <!-- Ações Finais -->
      <div class="results-actions">
        <button @click="startNewSession" class="action-btn primary">
          <i class="fas fa-redo"></i>
          Nova Sessão
        </button>
        <button @click="saveAndScheduleReview" class="action-btn secondary">
          <i class="fas fa-calendar-plus"></i>
          Agendar Revisão
        </button>
        <button @click="shareResults" class="action-btn">
          <i class="fas fa-share-alt"></i>
          Compartilhar
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

const store = useStore()
const emit = defineEmits(['close', 'complete', 'schedule-review'])

// Props
const props = defineProps({
  subject: String,
  mode: String,
  filters: Object
})

// Estado da Sessão
const sessionActive = ref(false)
const showResults = ref(false)
const currentMode = ref('')
const sessionStartTime = ref(null)
const sessionDuration = ref(0)

// Questões
const questions = ref([])
const currentQuestionIndex = ref(0)
const currentQuestion = computed(() => questions.value[currentQuestionIndex.value] || {})
const totalQuestions = computed(() => questions.value.length)
const selectedAnswer = ref(null)
const showResult = ref(false)
const questionResults = ref([])

// Timer
const timeRemaining = ref(0)
let timerInterval = null

// Feedback
const showFeedback = ref(false)
const feedbackType = ref('')
const feedbackMessage = ref('')
const feedbackSubMessage = ref('')
const feedbackPoints = ref(0)
const feedbackIcon = computed(() => {
  return feedbackType.value === 'correct' ? 'fas fa-check-circle' : 'fas fa-times-circle'
})

// Hints
const showHintContent = ref(false)
const hintsUsed = ref(0)
const maxHints = ref(3)

// Pontuação
const totalPoints = ref(0)
const totalSessionPoints = ref(0)
const currentStreak = ref(0)

// Filtros
const selectedSubject = ref(props.subject || '')
const selectedDifficulty = ref('')
const selectedTypes = ref([])

// Dados Mock
const subjects = ['Anatomia', 'Fisiologia', 'Patologia', 'Farmacologia', 'Bioquímica']
const difficultyLevels = [
  { value: 'easy', label: 'Fácil', icon: 'fas fa-smile' },
  { value: 'medium', label: 'Médio', icon: 'fas fa-meh' },
  { value: 'hard', label: 'Difícil', icon: 'fas fa-frown' }
]
const questionTypes = [
  { value: 'multiple', label: 'Múltipla Escolha' },
  { value: 'true-false', label: 'Verdadeiro/Falso' },
  { value: 'case', label: 'Caso Clínico' }
]

// Computed
const progressPercentage = computed(() => {
  return totalQuestions.value > 0 ? ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100 : 0
})

const sessionAccuracy = computed(() => {
  const correct = questionResults.value.filter(r => r === true).length
  const total = questionResults.value.length
  return total > 0 ? Math.round((correct / total) * 100) : 0
})

const finalAccuracy = computed(() => {
  const correct = questionResults.value.filter(r => r === true).length
  return Math.round((correct / totalQuestions.value) * 100)
})

const questionsToReview = computed(() => {
  return questions.value.filter((q, index) => {
    return questionResults.value[index] === false || q.flagged
  })
})

const timeStats = computed(() => {
  // Mock data - calcular baseado em dados reais
  return [
    { label: 'Rápidas (<30s)', value: 25, percentage: 60, class: 'fast' },
    { label: 'Normais (30-60s)', value: 45, percentage: 30, class: 'normal' },
    { label: 'Lentas (>60s)', value: 80, percentage: 10, class: 'slow' }
  ]
})

const unlockedAchievements = computed(() => {
  const achievements = []
  
  if (finalAccuracy.value === 100) {
    achievements.push({
      id: 'perfect',
      name: 'Perfeição!',
      description: '100% de acerto em uma sessão',
      icon: 'fas fa-star',
      color: '#f59e0b'
    })
  }
  
  if (currentStreak.value >= 7) {
    achievements.push({
      id: 'week-streak',
      name: 'Semana de Fogo',
      description: '7 dias consecutivos',
      icon: 'fas fa-fire',
      color: '#ef4444'
    })
  }
  
  return achievements
})

const aiRecommendations = computed(() => {
  return [
    {
      id: 1,
      type: 'weakness',
      icon: 'fas fa-chart-line',
      title: 'Foco em Pontos Fracos',
      description: '15 questões de Farmacologia',
      reason: 'Você teve 45% de acerto nesta matéria',
      config: { subject: 'Farmacologia', count: 15 }
    },
    {
      id: 2,
      type: 'review',
      icon: 'fas fa-history',
      title: 'Revisão Espaçada',
      description: 'Questões que você errou há 3 dias',
      reason: 'Momento ideal para revisar',
      config: { mode: 'spaced', days: 3 }
    },
    {
      id: 3,
      type: 'challenge',
      icon: 'fas fa-trophy',
      title: 'Desafio Semanal',
      description: 'Questões difíceis variadas',
      reason: 'Teste seus limites!',
      config: { difficulty: 'hard', mixed: true }
    }
  ]
})

// Chart refs
const subjectPerformanceChart = ref(null)
const difficultyChart = ref(null)
let charts = {}

// Methods
const startMode = (mode) => {
  currentMode.value = mode
  sessionActive.value = true
  sessionStartTime.value = Date.now()
  loadQuestions()
  startTimer()
}

const loadQuestions = () => {
  // Mock - carregar questões baseado no modo e filtros
  const mockQuestions = []
  const count = currentMode.value === 'sprint' ? 10 : currentMode.value === 'marathon' ? 50 : 20
  
  for (let i = 0; i < count; i++) {
    mockQuestions.push({
      id: `q${i + 1}`,
      subject: subjects[Math.floor(Math.random() * subjects.length)],
      topic: 'Tópico ' + (i + 1),
      difficulty: ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)],
      question: `Questão ${i + 1}: Esta é uma questão de exemplo sobre ${selectedSubject.value || 'medicina'}?`,
      options: [
        { text: 'Opção A - Resposta correta', explanation: 'Esta é a resposta correta porque...' },
        { text: 'Opção B - Resposta incorreta', explanation: '' },
        { text: 'Opção C - Resposta incorreta', explanation: '' },
        { text: 'Opção D - Resposta incorreta', explanation: '' }
      ],
      correctAnswer: 0,
      hint: 'Pense sobre os conceitos fundamentais...',
      points: currentMode.value === 'challenge' ? 30 : currentMode.value === 'spaced' ? 20 : 10,
      flagged: false
    })
  }
  
  questions.value = mockQuestions
}

const startTimer = () => {
  const timeLimit = currentMode.value === 'sprint' ? 600 : currentMode.value === 'marathon' ? 2700 : 1200
  timeRemaining.value = timeLimit
  
  timerInterval = setInterval(() => {
    if (timeRemaining.value > 0) {
      timeRemaining.value--
    } else {
      finishSession()
    }
  }, 1000)
}

const selectAnswer = (index) => {
  if (!showResult.value) {
    selectedAnswer.value = index
  }
}

const confirmAnswer = () => {
  if (selectedAnswer.value === null) return
  
  showResult.value = true
  const isCorrect = selectedAnswer.value === currentQuestion.value.correctAnswer
  questionResults.value.push(isCorrect)
  
  // Calcular pontos
  let points = currentQuestion.value.points
  if (isCorrect) {
    if (hintsUsed.value > 0) {
      points = Math.round(points * 0.7) // -30% se usou dica
    }
    totalSessionPoints.value += points
    
    // Mostrar feedback
    showFeedback.value = true
    feedbackType.value = 'correct'
    feedbackMessage.value = 'Excelente!'
    feedbackSubMessage.value = 'Continue assim!'
    feedbackPoints.value = points
  } else {
    showFeedback.value = true
    feedbackType.value = 'incorrect'
    feedbackMessage.value = 'Ops!'
    feedbackSubMessage.value = 'Revise este conteúdo'
    feedbackPoints.value = 0
  }
  
  setTimeout(() => {
    showFeedback.value = false
  }, 2000)
}

const nextQuestion = () => {
  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    currentQuestionIndex.value++
    selectedAnswer.value = null
    showResult.value = false
    showHintContent.value = false
  } else {
    finishSession()
  }
}

const finishSession = () => {
  clearInterval(timerInterval)
  sessionDuration.value = Math.round((Date.now() - sessionStartTime.value) / 1000)
  sessionActive.value = false
  showResults.value = true
  
  // Salvar resultados
  saveSessionResults()
  
  // Criar gráficos
  nextTick(() => {
    createResultCharts()
  })
}

const flagQuestion = () => {
  currentQuestion.value.flagged = !currentQuestion.value.flagged
}

const showHint = () => {
  if (hintsUsed.value < maxHints.value) {
    showHintContent.value = true
    hintsUsed.value++
  }
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getSubjectIcon = (subject) => {
  const icons = {
    'Anatomia': 'fas fa-user',
    'Fisiologia': 'fas fa-heartbeat',
    'Patologia': 'fas fa-virus',
    'Farmacologia': 'fas fa-pills',
    'Bioquímica': 'fas fa-flask'
  }
  return icons[subject] || 'fas fa-book'
}

const getDifficultyLabel = (difficulty) => {
  const labels = {
    'easy': 'Fácil',
    'medium': 'Médio',
    'hard': 'Difícil'
  }
  return labels[difficulty] || difficulty
}

const startNewSession = () => {
  // Reset everything
  showResults.value = false
  currentQuestionIndex.value = 0
  questionResults.value = []
  selectedAnswer.value = null
  showResult.value = false
  hintsUsed.value = 0
  totalSessionPoints.value = 0
}

const saveAndScheduleReview = () => {
  const incorrectQuestions = questionsToReview.value
  emit('schedule-review', {
    questions: incorrectQuestions,
    sessionData: {
      mode: currentMode.value,
      accuracy: finalAccuracy.value,
      duration: sessionDuration.value
    }
  })
}

const shareResults = () => {
  // Implementar compartilhamento
  console.log('Compartilhar resultados')
}

const reviewQuestion = (question) => {
  // Abrir modal de revisão da questão
  console.log('Revisar questão:', question)
}

const applyRecommendation = (rec) => {
  // Aplicar configuração da recomendação
  if (rec.config.subject) {
    selectedSubject.value = rec.config.subject
  }
  if (rec.config.difficulty) {
    selectedDifficulty.value = rec.config.difficulty
  }
  if (rec.config.mode) {
    startMode(rec.config.mode)
  }
}

const saveSessionResults = () => {
  // Salvar no store/backend
  const sessionData = {
    mode: currentMode.value,
    date: new Date(),
    duration: sessionDuration.value,
    totalQuestions: totalQuestions.value,
    correctAnswers: questionResults.value.filter(r => r === true).length,
    accuracy: finalAccuracy.value,
    points: totalSessionPoints.value,
    questions: questions.value.map((q, index) => ({
      ...q,
      userAnswer: questionResults.value[index],
      timeSpent: 0 // Calcular tempo real gasto
    }))
  }
  
  // store.dispatch('saveQuestionSession', sessionData)
}

const createResultCharts = () => {
  // Gráfico de desempenho por matéria
  if (subjectPerformanceChart.value) {
    const subjectData = {}
    questions.value.forEach((q, index) => {
      if (!subjectData[q.subject]) {
        subjectData[q.subject] = { correct: 0, total: 0 }
      }
      subjectData[q.subject].total++
      if (questionResults.value[index]) {
        subjectData[q.subject].correct++
      }
    })
    
    const labels = Object.keys(subjectData)
    const data = labels.map(s => Math.round((subjectData[s].correct / subjectData[s].total) * 100))
    
    charts.subjectChart = new Chart(subjectPerformanceChart.value, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Taxa de Acerto (%)',
          data,
          backgroundColor: '#3b82f6'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        }
      }
    })
  }
  
  // Gráfico por dificuldade
  if (difficultyChart.value) {
    const difficultyData = { easy: { correct: 0, total: 0 }, medium: { correct: 0, total: 0 }, hard: { correct: 0, total: 0 } }
    
    questions.value.forEach((q, index) => {
      difficultyData[q.difficulty].total++
      if (questionResults.value[index]) {
        difficultyData[q.difficulty].correct++
      }
    })
    
    charts.difficultyChart = new Chart(difficultyChart.value, {
      type: 'doughnut',
      data: {
        labels: ['Fácil', 'Médio', 'Difícil'],
        datasets: [{
          data: [
            difficultyData.easy.total ? Math.round((difficultyData.easy.correct / difficultyData.easy.total) * 100) : 0,
            difficultyData.medium.total ? Math.round((difficultyData.medium.correct / difficultyData.medium.total) * 100) : 0,
            difficultyData.hard.total ? Math.round((difficultyData.hard.correct / difficultyData.hard.total) * 100) : 0
          ],
          backgroundColor: ['#10b981', '#f59e0b', '#ef4444']
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}

// Lifecycle
onMounted(() => {
  // Carregar dados do usuário
  totalPoints.value = store.state.userPoints || 0
  currentStreak.value = store.state.currentStreak || 0
  
  // Se props indicam início automático
  if (props.mode) {
    startMode(props.mode)
  }
})

onUnmounted(() => {
  clearInterval(timerInterval)
  
  // Limpar gráficos
  Object.values(charts).forEach(chart => chart?.destroy())
})
</script>

<style scoped>
.quick-questions-system {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--surface-color, #ffffff);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Header */
.qqs-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  color: white;
}

.header-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.pulse-effect {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.gradient-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  animation: shimmer 3s linear infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.header-content {
  position: relative;
  z-index: 1;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.system-title {
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: rotate(90deg);
}

.session-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

/* Mode Selection */
.mode-selection {
  padding: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.mode-card {
  background: var(--surface-light, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.mode-card.premium {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mode-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.mode-icon.sprint {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.mode-icon.marathon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.mode-icon.spaced {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.mode-icon.challenge {
  background: rgba(255,255,255,0.2);
  color: white;
}

.mode-card h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.mode-card p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.mode-card.premium p {
  color: rgba(255,255,255,0.9);
}

.mode-benefits {
  display: flex;
  justify-content: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.mode-benefits span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);
}

.mode-card.premium .mode-benefits span {
  color: rgba(255,255,255,0.8);
}

.premium-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Advanced Filters */
.advanced-filters {
  background: var(--surface-light, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.filter-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: white;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.difficulty-selector {
  display: flex;
  gap: 0.5rem;
}

.difficulty-btn {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid var(--border-color, #e5e7eb);
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.difficulty-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.type-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.type-option input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* AI Recommendations */
.ai-recommendations {
  margin-bottom: 2rem;
}

.recommendations-grid {
  display: grid;
  gap: 1rem;
}

.recommendation-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.recommendation-card.weakness {
  border-left: 4px solid #ef4444;
}

.recommendation-card.review {
  border-left: 4px solid #f59e0b;
}

.recommendation-card.challenge {
  border-left: 4px solid #10b981;
}

.rec-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.recommendation-card.weakness .rec-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.recommendation-card.review .rec-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.recommendation-card.challenge .rec-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.rec-content p {
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.rec-reason {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  font-style: italic;
}

.rec-action {
  color: var(--text-secondary);
}

/* Active Session */
.active-session {
  padding: 2rem;
}

.session-progress {
  margin-bottom: 2rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.timer.warning {
  color: #ef4444;
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.progress-bar {
  position: relative;
  height: 8px;
  background: var(--border-color, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.progress-markers {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
}

.marker {
  flex: 1;
  height: 4px;
  background: transparent;
  position: relative;
}

.marker::after {
  content: '';
  position: absolute;
  right: -2px;
  top: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--border-color, #e5e7eb);
  transition: all 0.3s ease;
}

.marker.completed::after {
  background: #667eea;
  transform: scale(1.2);
}

.marker.current::after {
  background: #764ba2;
  transform: scale(1.5);
  box-shadow: 0 0 0 4px rgba(118, 75, 162, 0.2);
}

.marker.correct::after {
  background: #10b981;
}

.marker.incorrect::after {
  background: #ef4444;
}

/* Question Container */
.question-container {
  background: var(--surface-light, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.question-meta {
  display: flex;
  gap: 1rem;
}

.subject-tag,
.difficulty-tag,
.points-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.subject-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.difficulty-tag.difficulty-easy {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.difficulty-tag.difficulty-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.difficulty-tag.difficulty-hard {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.points-tag {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.question-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  color: var(--text-secondary);
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.action-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.question-content {
  margin-bottom: 1.5rem;
}

.question-text {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.question-image {
  margin-bottom: 1.5rem;
  text-align: center;
}

.question-image img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.hint-box {
  background: rgba(251, 191, 36, 0.1);
  border: 2px solid #fbbf24;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #92400e;
}

.hint-box i {
  font-size: 1.5rem;
  color: #fbbf24;
}

/* Answer Options */
.answer-options {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-card:hover:not(.disabled) {
  border-color: #667eea;
  transform: translateX(5px);
}

.option-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.option-card.correct {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.option-card.incorrect {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.option-card.disabled {
  cursor: default;
}

.option-letter {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--surface-light, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.option-card.selected .option-letter {
  background: #667eea;
  color: white;
}

.option-card.correct .option-letter {
  background: #10b981;
  color: white;
}

.option-card.incorrect .option-letter {
  background: #ef4444;
  color: white;
}

.option-content {
  flex: 1;
}

.option-content p {
  color: var(--text-primary);
  line-height: 1.5;
}

.explanation {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  font-size: 0.875rem;
  color: #065f46;
  display: flex;
  align-items: start;
  gap: 0.5rem;
}

.option-feedback {
  font-size: 1.5rem;
}

.option-feedback .fa-check-circle {
  color: #10b981;
}

.option-feedback .fa-times-circle {
  color: #ef4444;
}

/* Question Footer */
.question-footer {
  display: flex;
  justify-content: center;
}

.confirm-btn,
.next-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.confirm-btn:hover:not(:disabled),
.next-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Feedback Overlay */
.feedback-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedback-content {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  animation: bounceIn 0.5s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.feedback-content i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.feedback-overlay.correct .feedback-content i {
  color: #10b981;
}

.feedback-overlay.incorrect .feedback-content i {
  color: #ef4444;
}

.feedback-content h2 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.feedback-content p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.feedback-points {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fbbf24;
}

/* Session Results */
.session-results {
  padding: 2rem;
}

.results-header {
  text-align: center;
  margin-bottom: 3rem;
}

.results-header h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.results-summary {
  display: flex;
  justify-content: center;
  gap: 3rem;
}

.summary-stat {
  text-align: center;
}

.summary-stat i {
  font-size: 2.5rem;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Achievements */
.achievements-section {
  margin-bottom: 3rem;
}

.achievements-section h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.achievement-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
}

.achievement-info h4 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.achievement-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Performance Analysis */
.performance-analysis {
  margin-bottom: 3rem;
}

.performance-analysis h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.analysis-card {
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
}

.analysis-card h4 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.analysis-card canvas {
  max-height: 200px;
}

.time-analysis {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.time-stat {
  display: grid;
  grid-template-columns: 120px 1fr 60px;
  align-items: center;
  gap: 1rem;
}

.time-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.time-bar {
  height: 8px;
  background: var(--border-color, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
}

.time-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.time-fill.fast {
  background: #10b981;
}

.time-fill.normal {
  background: #3b82f6;
}

.time-fill.slow {
  background: #ef4444;
}

.time-value {
  font-weight: 600;
  text-align: right;
}

/* Review Section */
.review-section {
  margin-bottom: 2rem;
}

.review-section h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.review-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.review-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.review-item:hover {
  border-color: #667eea;
  transform: translateX(5px);
}

.review-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.review-icon.flagged {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.review-icon.incorrect {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.review-content {
  flex: 1;
}

.review-content p {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.review-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Results Actions */
.results-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.results-actions .action-btn {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Transitions */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.bounce-enter-active {
  animation: bounceIn 0.5s;
}

.bounce-leave-active {
  animation: bounceOut 0.5s;
}

@keyframes bounceOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .quick-questions-system {
    background: #1a1a1a;
    color: #ffffff;
  }
  
  .mode-card,
  .advanced-filters,
  .question-container,
  .analysis-card {
    background: #2a2a2a;
  }
  
  .filter-select,
  .difficulty-btn,
  .option-card,
  .review-item {
    background: #333333;
    border-color: #444444;
    color: #ffffff;
  }
  
  .option-letter {
    background: #444444;
  }
  
  .feedback-content {
    background: #2a2a2a;
    color: #ffffff;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .qqs-header {
    padding: 1.5rem;
  }
  
  .system-title {
    font-size: 1.5rem;
  }
  
  .session-stats {
    gap: 1rem;
    font-size: 0.9rem;
  }
  
  .modes-grid {
    grid-template-columns: 1fr;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .results-summary {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .results-actions {
    flex-direction: column;
  }
}
</style>