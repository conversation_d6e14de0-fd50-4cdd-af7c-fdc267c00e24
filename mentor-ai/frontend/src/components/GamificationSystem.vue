<template>
  <div class="gamification-system">
    <!-- Header com Nível e XP -->
    <div class="gamification-header">
      <div class="user-level-card">
        <div class="level-badge">
          <div class="level-icon">
            <i :class="currentLevelIcon"></i>
          </div>
          <div class="level-number">{{ userLevel }}</div>
        </div>
        
        <div class="level-info">
          <h3>{{ currentLevelTitle }}</h3>
          <div class="xp-progress">
            <div class="xp-bar">
              <div 
                class="xp-fill" 
                :style="{ width: xpProgress + '%' }"
              >
                <span class="xp-glow"></span>
              </div>
            </div>
            <div class="xp-text">
              <span>{{ currentXP }} / {{ nextLevelXP }} XP</span>
              <span class="xp-gain" v-if="recentXPGain > 0">+{{ recentXPGain }}</span>
            </div>
          </div>
          <p class="level-description">{{ currentLevelDescription }}</p>
        </div>
        
        <div class="level-rewards">
          <h4>Próximas Recompensas</h4>
          <div class="reward-preview">
            <div v-for="reward in nextRewards" :key="reward.id" class="reward-item">
              <i :class="reward.icon" :style="{ color: reward.color }"></i>
              <span>{{ reward.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas e Rankings -->
    <div class="stats-rankings">
      <div class="stats-card">
        <h3>
          <i class="fas fa-chart-line"></i>
          Suas Estatísticas
        </h3>
        <div class="stats-grid">
          <div class="stat-box">
            <i class="fas fa-fire"></i>
            <div class="stat-content">
              <span class="stat-value">{{ currentStreak }}</span>
              <span class="stat-label">Dias Consecutivos</span>
            </div>
          </div>
          <div class="stat-box">
            <i class="fas fa-trophy"></i>
            <div class="stat-content">
              <span class="stat-value">{{ totalAchievements }}</span>
              <span class="stat-label">Conquistas</span>
            </div>
          </div>
          <div class="stat-box">
            <i class="fas fa-star"></i>
            <div class="stat-content">
              <span class="stat-value">{{ totalPoints }}</span>
              <span class="stat-label">Pontos Total</span>
            </div>
          </div>
          <div class="stat-box">
            <i class="fas fa-medal"></i>
            <div class="stat-content">
              <span class="stat-value">#{{ userRank }}</span>
              <span class="stat-label">Ranking Global</span>
            </div>
          </div>
        </div>
      </div>

      <div class="leaderboard-card">
        <div class="leaderboard-header">
          <h3>
            <i class="fas fa-crown"></i>
            Ranking Semanal
          </h3>
          <select v-model="leaderboardType" class="leaderboard-filter">
            <option value="week">Esta Semana</option>
            <option value="month">Este Mês</option>
            <option value="all">Todos os Tempos</option>
          </select>
        </div>
        
        <div class="leaderboard-list">
          <div 
            v-for="(user, index) in leaderboard" 
            :key="user.id"
            class="leaderboard-item"
            :class="{ 
              'current-user': user.id === currentUserId,
              'top-three': index < 3
            }"
          >
            <div class="rank-badge" :class="`rank-${index + 1}`">
              <i v-if="index === 0" class="fas fa-crown"></i>
              <i v-else-if="index === 1" class="fas fa-medal"></i>
              <i v-else-if="index === 2" class="fas fa-award"></i>
              <span v-else>{{ index + 1 }}</span>
            </div>
            
            <div class="user-avatar">
              <img :src="user.avatar" :alt="user.name" />
              <div class="level-indicator">{{ user.level }}</div>
            </div>
            
            <div class="user-info">
              <span class="user-name">{{ user.name }}</span>
              <span class="user-title">{{ user.title }}</span>
            </div>
            
            <div class="user-score">
              <span class="score-value">{{ user.score }}</span>
              <span class="score-label">pontos</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sistema de Conquistas -->
    <div class="achievements-section">
      <div class="section-header">
        <h2>
          <i class="fas fa-trophy"></i>
          Conquistas
        </h2>
        <div class="achievement-filters">
          <button 
            v-for="filter in achievementFilters" 
            :key="filter.id"
            @click="selectedAchievementFilter = filter.id"
            :class="['filter-btn', { active: selectedAchievementFilter === filter.id }]"
          >
            {{ filter.label }}
            <span class="filter-count">{{ filter.count }}</span>
          </button>
        </div>
      </div>

      <div class="achievements-grid">
        <div 
          v-for="achievement in filteredAchievements" 
          :key="achievement.id"
          class="achievement-card"
          :class="{ 
            unlocked: achievement.unlocked,
            rare: achievement.rarity === 'rare',
            epic: achievement.rarity === 'epic',
            legendary: achievement.rarity === 'legendary'
          }"
          @click="showAchievementDetails(achievement)"
        >
          <div class="achievement-icon" :style="{ background: achievement.color }">
            <i :class="achievement.icon"></i>
            <div v-if="!achievement.unlocked" class="locked-overlay">
              <i class="fas fa-lock"></i>
            </div>
          </div>
          
          <div class="achievement-content">
            <h4>{{ achievement.name }}</h4>
            <p>{{ achievement.description }}</p>
            
            <div v-if="achievement.progress !== undefined" class="achievement-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: achievement.progress + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ achievement.current }} / {{ achievement.target }}</span>
            </div>
            
            <div class="achievement-meta">
              <span v-if="achievement.unlocked" class="unlock-date">
                <i class="fas fa-calendar-check"></i>
                {{ formatDate(achievement.unlockedAt) }}
              </span>
              <span class="achievement-xp">
                <i class="fas fa-star"></i>
                {{ achievement.xp }} XP
              </span>
              <span v-if="achievement.rarity" class="achievement-rarity" :class="achievement.rarity">
                {{ getRarityLabel(achievement.rarity) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Desafios e Missões -->
    <div class="challenges-section">
      <div class="section-header">
        <h2>
          <i class="fas fa-tasks"></i>
          Desafios Ativos
        </h2>
        <div class="challenges-timer">
          <i class="fas fa-clock"></i>
          Atualiza em {{ challengesTimeRemaining }}
        </div>
      </div>

      <div class="challenges-container">
        <!-- Desafios Diários -->
        <div class="challenge-group">
          <h3>
            <i class="fas fa-calendar-day"></i>
            Desafios Diários
          </h3>
          <div class="challenges-list">
            <div 
              v-for="challenge in dailyChallenges" 
              :key="challenge.id"
              class="challenge-item"
              :class="{ completed: challenge.completed }"
            >
              <div class="challenge-icon" :style="{ background: challenge.color }">
                <i :class="challenge.icon"></i>
              </div>
              
              <div class="challenge-info">
                <h4>{{ challenge.title }}</h4>
                <p>{{ challenge.description }}</p>
                <div class="challenge-progress">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: challenge.progress + '%' }"
                    ></div>
                  </div>
                  <span>{{ challenge.current }} / {{ challenge.target }}</span>
                </div>
              </div>
              
              <div class="challenge-reward">
                <div class="reward-amount">
                  <i class="fas fa-star"></i>
                  {{ challenge.xp }}
                </div>
                <button 
                  v-if="challenge.completed && !challenge.claimed"
                  @click="claimReward(challenge)"
                  class="claim-btn"
                >
                  Resgatar
                </button>
                <i v-else-if="challenge.claimed" class="fas fa-check-circle claimed-icon"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Desafios Semanais -->
        <div class="challenge-group">
          <h3>
            <i class="fas fa-calendar-week"></i>
            Desafios Semanais
          </h3>
          <div class="challenges-list">
            <div 
              v-for="challenge in weeklyChallenges" 
              :key="challenge.id"
              class="challenge-item weekly"
              :class="{ completed: challenge.completed }"
            >
              <div class="challenge-icon" :style="{ background: challenge.color }">
                <i :class="challenge.icon"></i>
              </div>
              
              <div class="challenge-info">
                <h4>{{ challenge.title }}</h4>
                <p>{{ challenge.description }}</p>
                <div class="challenge-stages">
                  <div 
                    v-for="(stage, index) in challenge.stages" 
                    :key="index"
                    class="stage"
                    :class="{ completed: index < challenge.currentStage }"
                  >
                    <div class="stage-marker"></div>
                    <span>{{ stage.target }}</span>
                  </div>
                </div>
              </div>
              
              <div class="challenge-rewards">
                <div v-for="(reward, index) in challenge.rewards" :key="index" class="reward-tier">
                  <i :class="reward.icon" :style="{ color: reward.color }"></i>
                  <span>{{ reward.amount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sistema de Badges e Títulos -->
    <div class="badges-section">
      <div class="section-header">
        <h2>
          <i class="fas fa-certificate"></i>
          Badges e Títulos
        </h2>
      </div>

      <div class="badges-showcase">
        <div class="showcase-group">
          <h3>Badges em Destaque</h3>
          <div class="badges-grid">
            <div 
              v-for="badge in featuredBadges" 
              :key="badge.id"
              class="badge-item"
              :class="{ 
                equipped: badge.equipped,
                animated: badge.animated
              }"
              @click="toggleBadge(badge)"
            >
              <div class="badge-frame" :class="badge.frameType">
                <div class="badge-icon">
                  <i :class="badge.icon"></i>
                </div>
                <div v-if="badge.animated" class="badge-animation"></div>
              </div>
              <span class="badge-name">{{ badge.name }}</span>
            </div>
          </div>
        </div>

        <div class="showcase-group">
          <h3>Títulos Desbloqueados</h3>
          <div class="titles-list">
            <div 
              v-for="title in unlockedTitles" 
              :key="title.id"
              @click="selectTitle(title)"
              class="title-item"
              :class="{ selected: title.id === selectedTitle }"
            >
              <div class="title-preview">{{ title.prefix }} <span class="name-placeholder">[Seu Nome]</span> {{ title.suffix }}</div>
              <p class="title-description">{{ title.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loja de Recompensas -->
    <div class="rewards-shop">
      <div class="section-header">
        <h2>
          <i class="fas fa-shopping-bag"></i>
          Loja de Recompensas
        </h2>
        <div class="shop-currency">
          <div class="currency-item">
            <i class="fas fa-coins"></i>
            <span>{{ userCoins }}</span>
          </div>
          <div class="currency-item">
            <i class="fas fa-gem"></i>
            <span>{{ userGems }}</span>
          </div>
        </div>
      </div>

      <div class="shop-categories">
        <button 
          v-for="category in shopCategories" 
          :key="category.id"
          @click="selectedShopCategory = category.id"
          :class="['category-btn', { active: selectedShopCategory === category.id }]"
        >
          <i :class="category.icon"></i>
          {{ category.label }}
        </button>
      </div>

      <div class="shop-items">
        <div 
          v-for="item in filteredShopItems" 
          :key="item.id"
          class="shop-item"
          :class="{ 
            owned: item.owned,
            limited: item.limited,
            featured: item.featured
          }"
        >
          <div v-if="item.featured" class="featured-badge">
            <i class="fas fa-star"></i>
            Destaque
          </div>
          
          <div class="item-preview">
            <div v-if="item.type === 'avatar-frame'" class="frame-preview" :class="item.frameClass">
              <img src="/placeholder-avatar.jpg" alt="Preview" />
            </div>
            <div v-else-if="item.type === 'theme'" class="theme-preview" :style="{ background: item.themeGradient }">
              <i class="fas fa-palette"></i>
            </div>
            <div v-else class="item-icon" :style="{ background: item.color }">
              <i :class="item.icon"></i>
            </div>
          </div>
          
          <div class="item-info">
            <h4>{{ item.name }}</h4>
            <p>{{ item.description }}</p>
            <div v-if="item.limited" class="limited-time">
              <i class="fas fa-clock"></i>
              Termina em {{ item.timeRemaining }}
            </div>
          </div>
          
          <div class="item-price">
            <button 
              v-if="!item.owned"
              @click="purchaseItem(item)"
              :disabled="!canAfford(item)"
              class="purchase-btn"
            >
              <i :class="item.currency === 'coins' ? 'fas fa-coins' : 'fas fa-gem'"></i>
              {{ item.price }}
            </button>
            <span v-else class="owned-label">
              <i class="fas fa-check"></i>
              Adquirido
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Detalhes da Conquista -->
    <transition name="modal">
      <div v-if="selectedAchievement" class="achievement-modal-overlay" @click.self="selectedAchievement = null">
        <div class="achievement-modal">
          <button @click="selectedAchievement = null" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
          
          <div class="modal-content">
            <div class="achievement-showcase">
              <div 
                class="achievement-icon-large" 
                :style="{ background: selectedAchievement.color }"
                :class="{ unlocked: selectedAchievement.unlocked }"
              >
                <i :class="selectedAchievement.icon"></i>
                <div v-if="!selectedAchievement.unlocked" class="locked-overlay-large">
                  <i class="fas fa-lock"></i>
                </div>
              </div>
              
              <h2>{{ selectedAchievement.name }}</h2>
              <p class="achievement-description-full">{{ selectedAchievement.fullDescription || selectedAchievement.description }}</p>
              
              <div v-if="selectedAchievement.unlocked" class="unlock-info">
                <i class="fas fa-trophy"></i>
                Desbloqueado em {{ formatDate(selectedAchievement.unlockedAt, true) }}
              </div>
              
              <div class="achievement-stats">
                <div class="stat">
                  <span class="stat-label">Raridade</span>
                  <span class="stat-value" :class="selectedAchievement.rarity">
                    {{ getRarityLabel(selectedAchievement.rarity) }}
                  </span>
                </div>
                <div class="stat">
                  <span class="stat-label">Jogadores com esta conquista</span>
                  <span class="stat-value">{{ selectedAchievement.percentage }}%</span>
                </div>
                <div class="stat">
                  <span class="stat-label">Recompensa XP</span>
                  <span class="stat-value">{{ selectedAchievement.xp }} XP</span>
                </div>
              </div>
              
              <div v-if="selectedAchievement.tips && !selectedAchievement.unlocked" class="achievement-tips">
                <h3>Dicas para desbloquear</h3>
                <ul>
                  <li v-for="(tip, index) in selectedAchievement.tips" :key="index">
                    {{ tip }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const emit = defineEmits(['achievement-unlocked', 'level-up', 'reward-claimed'])

// Estado do Usuário
const currentUserId = ref('user123')
const userLevel = ref(12)
const currentXP = ref(3450)
const nextLevelXP = ref(5000)
const recentXPGain = ref(0)
const currentStreak = ref(7)
const totalPoints = ref(24500)
const userRank = ref(42)
const userCoins = ref(1250)
const userGems = ref(50)
const selectedTitle = ref('scholar')

// Estado da Interface
const leaderboardType = ref('week')
const selectedAchievementFilter = ref('all')
const selectedAchievement = ref(null)
const selectedShopCategory = ref('avatars')

// Computed Properties
const xpProgress = computed(() => {
  return Math.round((currentXP.value / nextLevelXP.value) * 100)
})

const currentLevelIcon = computed(() => {
  const icons = [
    'fas fa-seedling',
    'fas fa-leaf',
    'fas fa-tree',
    'fas fa-mountain',
    'fas fa-crown'
  ]
  return icons[Math.min(Math.floor(userLevel.value / 10), 4)]
})

const currentLevelTitle = computed(() => {
  const titles = [
    'Iniciante Estudioso',
    'Aprendiz Dedicado',
    'Estudante Experiente',
    'Mestre do Conhecimento',
    'Sábio Iluminado'
  ]
  return titles[Math.min(Math.floor(userLevel.value / 10), 4)]
})

const currentLevelDescription = computed(() => {
  return `Continue estudando para alcançar o nível ${userLevel.value + 1}!`
})

const nextRewards = computed(() => [
  { id: 1, name: 'Badge Raro', icon: 'fas fa-medal', color: '#8b5cf6' },
  { id: 2, name: '500 Moedas', icon: 'fas fa-coins', color: '#fbbf24' },
  { id: 3, name: 'Título Exclusivo', icon: 'fas fa-crown', color: '#ef4444' }
])

// Dados Mock
const achievements = ref([
  {
    id: 'first-study',
    name: 'Primeiro Passo',
    description: 'Complete seu primeiro estudo',
    fullDescription: 'Você deu o primeiro passo em sua jornada de aprendizado. Continue assim!',
    icon: 'fas fa-book-open',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    xp: 100,
    unlocked: true,
    unlockedAt: new Date('2024-01-15'),
    progress: 100,
    current: 1,
    target: 1,
    rarity: 'common',
    percentage: 98.5
  },
  {
    id: 'week-streak',
    name: 'Semana de Fogo',
    description: 'Estude por 7 dias consecutivos',
    icon: 'fas fa-fire',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    xp: 500,
    unlocked: true,
    unlockedAt: new Date('2024-01-20'),
    progress: 100,
    current: 7,
    target: 7,
    rarity: 'rare',
    percentage: 45.2
  },
  {
    id: 'question-master',
    name: 'Mestre das Questões',
    description: 'Responda 1000 questões corretamente',
    icon: 'fas fa-brain',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    xp: 1000,
    unlocked: false,
    progress: 65,
    current: 650,
    target: 1000,
    rarity: 'epic',
    percentage: 12.8,
    tips: [
      'Pratique questões diariamente',
      'Foque em matérias que você tem dificuldade',
      'Use o modo de revisão espaçada'
    ]
  },
  {
    id: 'perfect-month',
    name: 'Mês Perfeito',
    description: 'Estude todos os dias por um mês',
    icon: 'fas fa-calendar-check',
    color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    xp: 2000,
    unlocked: false,
    progress: 23,
    current: 7,
    target: 30,
    rarity: 'legendary',
    percentage: 2.1,
    tips: [
      'Configure lembretes diários',
      'Estabeleça uma rotina de estudos',
      'Mesmo 5 minutos por dia contam!'
    ]
  }
])

const totalAchievements = computed(() => achievements.value.filter(a => a.unlocked).length)

const achievementFilters = computed(() => [
  { id: 'all', label: 'Todas', count: achievements.value.length },
  { id: 'unlocked', label: 'Desbloqueadas', count: achievements.value.filter(a => a.unlocked).length },
  { id: 'locked', label: 'Bloqueadas', count: achievements.value.filter(a => !a.unlocked).length },
  { id: 'rare', label: 'Raras+', count: achievements.value.filter(a => ['rare', 'epic', 'legendary'].includes(a.rarity)).length }
])

const filteredAchievements = computed(() => {
  switch (selectedAchievementFilter.value) {
    case 'unlocked':
      return achievements.value.filter(a => a.unlocked)
    case 'locked':
      return achievements.value.filter(a => !a.unlocked)
    case 'rare':
      return achievements.value.filter(a => ['rare', 'epic', 'legendary'].includes(a.rarity))
    default:
      return achievements.value
  }
})

const leaderboard = ref([
  { id: 'user456', name: 'Ana Silva', level: 15, score: 28900, avatar: '/avatar1.jpg', title: 'Mestra Dedicada' },
  { id: 'user789', name: 'Carlos Santos', level: 14, score: 27500, avatar: '/avatar2.jpg', title: 'Estudante Elite' },
  { id: 'user012', name: 'Maria Oliveira', level: 13, score: 26200, avatar: '/avatar3.jpg', title: 'Sábia Iniciante' },
  { id: 'user123', name: 'Você', level: 12, score: 24500, avatar: '/avatar4.jpg', title: 'Aprendiz Focado' },
  { id: 'user345', name: 'João Pedro', level: 11, score: 23100, avatar: '/avatar5.jpg', title: 'Novato Promissor' }
])

const dailyChallenges = ref([
  {
    id: 'daily-1',
    title: 'Maratonista',
    description: 'Complete 50 questões hoje',
    icon: 'fas fa-running',
    color: '#667eea',
    current: 35,
    target: 50,
    progress: 70,
    xp: 200,
    completed: false,
    claimed: false
  },
  {
    id: 'daily-2',
    title: 'Precisão Cirúrgica',
    description: 'Acerte 90% das questões',
    icon: 'fas fa-bullseye',
    color: '#10b981',
    current: 92,
    target: 90,
    progress: 100,
    xp: 150,
    completed: true,
    claimed: false
  },
  {
    id: 'daily-3',
    title: 'Estudante Matinal',
    description: 'Estude antes das 9h',
    icon: 'fas fa-sun',
    color: '#f59e0b',
    current: 1,
    target: 1,
    progress: 100,
    xp: 100,
    completed: true,
    claimed: true
  }
])

const weeklyChallenges = ref([
  {
    id: 'weekly-1',
    title: 'Mestre de Anatomia',
    description: 'Complete questões de Anatomia em diferentes níveis',
    icon: 'fas fa-user-md',
    color: '#8b5cf6',
    currentStage: 2,
    stages: [
      { target: '100 questões', completed: true },
      { target: '250 questões', completed: true },
      { target: '500 questões', completed: false }
    ],
    rewards: [
      { icon: 'fas fa-star', amount: '500 XP', color: '#fbbf24' },
      { icon: 'fas fa-coins', amount: '300', color: '#f59e0b' },
      { icon: 'fas fa-gem', amount: '10', color: '#8b5cf6' }
    ]
  },
  {
    id: 'weekly-2',
    title: 'Diversidade é Poder',
    description: 'Estude pelo menos 5 matérias diferentes',
    icon: 'fas fa-layer-group',
    color: '#ef4444',
    currentStage: 1,
    stages: [
      { target: '3 matérias', completed: true },
      { target: '5 matérias', completed: false },
      { target: '7 matérias', completed: false }
    ],
    rewards: [
      { icon: 'fas fa-star', amount: '300 XP', color: '#fbbf24' },
      { icon: 'fas fa-coins', amount: '200', color: '#f59e0b' },
      { icon: 'fas fa-certificate', amount: 'Badge', color: '#10b981' }
    ]
  }
])

const challengesTimeRemaining = ref('23h 45m')

const featuredBadges = ref([
  {
    id: 'fire-week',
    name: 'Semana em Chamas',
    icon: 'fas fa-fire',
    frameType: 'gold',
    equipped: true,
    animated: true
  },
  {
    id: 'perfect-score',
    name: 'Pontuação Perfeita',
    icon: 'fas fa-star',
    frameType: 'silver',
    equipped: false,
    animated: false
  },
  {
    id: 'early-bird',
    name: 'Madrugador',
    icon: 'fas fa-dove',
    frameType: 'bronze',
    equipped: false,
    animated: false
  }
])

const unlockedTitles = ref([
  {
    id: 'scholar',
    prefix: '',
    suffix: 'o Estudioso',
    description: 'Para aqueles que amam aprender'
  },
  {
    id: 'persistent',
    prefix: '',
    suffix: 'o Persistente',
    description: 'Nunca desiste de seus objetivos'
  },
  {
    id: 'wise',
    prefix: 'Sábio',
    suffix: '',
    description: 'Demonstra grande conhecimento'
  }
])

const shopCategories = [
  { id: 'avatars', label: 'Avatares', icon: 'fas fa-user-circle' },
  { id: 'themes', label: 'Temas', icon: 'fas fa-palette' },
  { id: 'boosters', label: 'Boosters', icon: 'fas fa-rocket' },
  { id: 'special', label: 'Especiais', icon: 'fas fa-sparkles' }
]

const shopItems = ref([
  {
    id: 'avatar-frame-gold',
    name: 'Moldura Dourada',
    description: 'Uma moldura dourada elegante para seu avatar',
    type: 'avatar-frame',
    category: 'avatars',
    frameClass: 'gold-frame',
    price: 500,
    currency: 'coins',
    owned: false,
    featured: true
  },
  {
    id: 'dark-theme',
    name: 'Tema Escuro Premium',
    description: 'Interface escura elegante e moderna',
    type: 'theme',
    category: 'themes',
    themeGradient: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
    price: 300,
    currency: 'coins',
    owned: false
  },
  {
    id: 'xp-booster',
    name: 'Booster de XP 2x',
    description: 'Dobre seus ganhos de XP por 24 horas',
    type: 'booster',
    category: 'boosters',
    icon: 'fas fa-bolt',
    color: '#fbbf24',
    price: 20,
    currency: 'gems',
    owned: false,
    limited: true,
    timeRemaining: '2d 14h'
  }
])

const filteredShopItems = computed(() => {
  return shopItems.value.filter(item => item.category === selectedShopCategory.value)
})

// Methods
const showAchievementDetails = (achievement) => {
  selectedAchievement.value = achievement
}

const claimReward = (challenge) => {
  if (challenge.completed && !challenge.claimed) {
    challenge.claimed = true
    currentXP.value += challenge.xp
    animateXPGain(challenge.xp)
    emit('reward-claimed', challenge)
  }
}

const animateXPGain = (amount) => {
  recentXPGain.value = amount
  setTimeout(() => {
    recentXPGain.value = 0
  }, 2000)
}

const toggleBadge = (badge) => {
  if (badge.equipped) {
    badge.equipped = false
  } else {
    featuredBadges.value.forEach(b => b.equipped = false)
    badge.equipped = true
  }
}

const selectTitle = (title) => {
  selectedTitle.value = title.id
}

const purchaseItem = (item) => {
  if (canAfford(item)) {
    if (item.currency === 'coins') {
      userCoins.value -= item.price
    } else {
      userGems.value -= item.price
    }
    item.owned = true
    
    // Animação de compra
    emit('item-purchased', item)
  }
}

const canAfford = (item) => {
  if (item.currency === 'coins') {
    return userCoins.value >= item.price
  } else {
    return userGems.value >= item.price
  }
}

const formatDate = (date, full = false) => {
  if (full) {
    return date.toLocaleDateString('pt-BR', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  return date.toLocaleDateString('pt-BR')
}

const getRarityLabel = (rarity) => {
  const labels = {
    common: 'Comum',
    rare: 'Raro',
    epic: 'Épico',
    legendary: 'Lendário'
  }
  return labels[rarity] || rarity
}

// Timer para desafios
let challengeTimer = null

const updateChallengeTimer = () => {
  const now = new Date()
  const tomorrow = new Date(now)
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(0, 0, 0, 0)
  
  const diff = tomorrow - now
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  challengesTimeRemaining.value = `${hours}h ${minutes}m`
}

// Lifecycle
onMounted(() => {
  updateChallengeTimer()
  challengeTimer = setInterval(updateChallengeTimer, 60000) // Atualiza a cada minuto
})

onUnmounted(() => {
  if (challengeTimer) {
    clearInterval(challengeTimer)
  }
})
</script>

<style scoped>
.gamification-system {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header com Nível */
.gamification-header {
  margin-bottom: 2rem;
}

.user-level-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 2rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 2rem;
  position: relative;
  overflow: hidden;
}

.user-level-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.level-badge {
  position: relative;
  z-index: 1;
}

.level-icon {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  position: relative;
}

.level-number {
  position: absolute;
  bottom: -10px;
  right: -10px;
  background: white;
  color: #667eea;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.level-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.level-info h3 {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.xp-progress {
  margin-bottom: 1rem;
}

.xp-bar {
  height: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.xp-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
  transition: width 0.5s ease;
  position: relative;
}

.xp-glow {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 50px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-50px); }
  100% { transform: translateX(50px); }
}

.xp-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.xp-gain {
  color: #fbbf24;
  font-weight: 600;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.level-description {
  opacity: 0.9;
}

.level-rewards {
  position: relative;
  z-index: 1;
}

.level-rewards h4 {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.reward-preview {
  display: flex;
  gap: 1rem;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

/* Stats e Rankings */
.stats-rankings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.stats-card,
.leaderboard-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-card h3,
.leaderboard-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-box {
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-box i {
  font-size: 2rem;
  color: #667eea;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.leaderboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.leaderboard-filter {
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.leaderboard-item:hover {
  background: var(--surface-light, #f8f9fa);
}

.leaderboard-item.current-user {
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid #667eea;
}

.leaderboard-item.top-three {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.rank-badge {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 50%;
  background: var(--surface-light, #f8f9fa);
}

.rank-badge.rank-1 {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.rank-badge.rank-2 {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  color: white;
}

.rank-badge.rank-3 {
  background: linear-gradient(135deg, #a16207 0%, #92400e 100%);
  color: white;
}

.user-avatar {
  position: relative;
}

.user-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.level-indicator {
  position: absolute;
  bottom: -4px;
  right: -4px;
  background: #667eea;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
}

.user-title {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.user-score {
  text-align: right;
}

.score-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.score-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Sistema de Conquistas */
.achievements-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.achievement-filters {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-btn:hover {
  border-color: #667eea;
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.filter-count {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.achievement-card.rare {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(139, 92, 246, 0.1) 100%);
}

.achievement-card.epic {
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.05) 0%, rgba(236, 72, 153, 0.1) 100%);
}

.achievement-card.legendary {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(251, 191, 36, 0.1) 100%);
  box-shadow: inset 0 0 0 2px rgba(251, 191, 36, 0.3);
}

.achievement-card.legendary::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  border-radius: 16px;
  opacity: 0;
  z-index: -1;
  animation: legendaryGlow 2s linear infinite;
}

@keyframes legendaryGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.5; }
}

.achievement-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  position: relative;
  flex-shrink: 0;
}

.locked-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.achievement-content {
  flex: 1;
}

.achievement-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.achievement-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
}

.achievement-progress {
  margin-bottom: 0.75rem;
}

.progress-bar {
  height: 8px;
  background: var(--border-color, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.achievement-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.unlock-date,
.achievement-xp {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);
}

.achievement-rarity {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.achievement-rarity.common {
  color: #64748b;
}

.achievement-rarity.rare {
  color: #8b5cf6;
}

.achievement-rarity.epic {
  color: #ec4899;
}

.achievement-rarity.legendary {
  color: #f59e0b;
}

/* Desafios e Missões */
.challenges-section {
  margin-bottom: 3rem;
}

.challenges-timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.challenges-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.challenge-group {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.challenge-group h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.challenges-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.challenge-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.challenge-item:hover {
  transform: translateX(5px);
}

.challenge-item.completed {
  background: rgba(16, 185, 129, 0.05);
  border: 2px solid #10b981;
}

.challenge-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.challenge-info {
  flex: 1;
}

.challenge-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.challenge-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.challenge-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.challenge-progress .progress-bar {
  flex: 1;
}

.challenge-reward {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.reward-amount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fbbf24;
}

.claim-btn {
  padding: 0.5rem 1rem;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.claim-btn:hover {
  background: #059669;
  transform: scale(1.05);
}

.claimed-icon {
  font-size: 1.5rem;
  color: #10b981;
}

.challenge-stages {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.stage {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: white;
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.stage.completed {
  background: #10b981;
  color: white;
}

.stage-marker {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.challenge-rewards {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reward-tier {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Badges e Títulos */
.badges-section {
  margin-bottom: 3rem;
}

.badges-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.showcase-group {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.showcase-group h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.badge-item:hover {
  transform: scale(1.05);
}

.badge-frame {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 4px;
}

.badge-frame.gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.badge-frame.silver {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.badge-frame.bronze {
  background: linear-gradient(135deg, #a16207 0%, #92400e 100%);
}

.badge-icon {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-primary);
}

.badge-item.equipped .badge-frame {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.badge-animation {
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.5), transparent);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.badge-name {
  font-size: 0.875rem;
  text-align: center;
  color: var(--text-secondary);
}

.titles-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.title-item {
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.title-item:hover {
  background: #667eea;
  color: white;
}

.title-item.selected {
  background: #667eea;
  color: white;
}

.title-preview {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.name-placeholder {
  opacity: 0.7;
}

.title-description {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Loja de Recompensas */
.rewards-shop {
  margin-bottom: 3rem;
}

.shop-currency {
  display: flex;
  gap: 1.5rem;
}

.currency-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 20px;
  font-weight: 600;
}

.currency-item i {
  font-size: 1.25rem;
}

.currency-item:first-child i {
  color: #f59e0b;
}

.currency-item:last-child i {
  color: #8b5cf6;
}

.shop-categories {
  display: flex;
  gap: 0.5rem;
  margin: 2rem 0;
}

.category-btn {
  padding: 0.75rem 1.5rem;
  background: white;
  border: 2px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.category-btn:hover {
  border-color: #667eea;
}

.category-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.shop-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.shop-item {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.shop-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.shop-item.limited {
  border-color: #ef4444;
}

.shop-item.featured {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(245, 158, 11, 0.05) 100%);
}

.featured-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.item-preview {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-preview {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-preview.gold-frame {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.frame-preview img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.theme-preview {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
}

.item-icon {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
}

.item-info {
  text-align: center;
}

.item-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.item-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.limited-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #ef4444;
  font-weight: 500;
}

.item-price {
  margin-top: auto;
}

.purchase-btn {
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.purchase-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: scale(1.05);
}

.purchase-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.owned-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-weight: 500;
}

/* Modal de Conquista */
.achievement-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.achievement-modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  position: relative;
  overflow: hidden;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.1);
  border: none;
  color: var(--text-secondary);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: var(--text-primary);
}

.modal-content {
  padding: 3rem;
}

.achievement-showcase {
  text-align: center;
}

.achievement-icon-large {
  width: 150px;
  height: 150px;
  margin: 0 auto 2rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  position: relative;
}

.achievement-icon-large.unlocked {
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.locked-overlay-large {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  font-size: 3rem;
}

.modal-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.achievement-description-full {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.unlock-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-radius: 12px;
  margin-bottom: 2rem;
  font-weight: 500;
}

.achievement-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.achievement-stats .stat {
  padding: 1rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
}

.achievement-stats .stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.achievement-stats .stat-value {
  font-size: 1.25rem;
  font-weight: 600;
}

.achievement-tips {
  text-align: left;
  padding: 1.5rem;
  background: var(--surface-light, #f8f9fa);
  border-radius: 12px;
}

.achievement-tips h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.achievement-tips ul {
  list-style: none;
  padding: 0;
}

.achievement-tips li {
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
  color: var(--text-secondary);
}

.achievement-tips li::before {
  content: '💡';
  position: absolute;
  left: 0;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .achievement-modal {
  transition: transform 0.3s ease;
}

.modal-enter-from .achievement-modal {
  transform: scale(0.9);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .stats-card,
  .leaderboard-card,
  .achievement-card,
  .challenge-group,
  .showcase-group,
  .shop-item,
  .achievement-modal {
    background: #1a1a1a;
    color: #ffffff;
  }

  .stat-box,
  .leaderboard-item,
  .challenge-item,
  .stage,
  .reward-tier,
  .title-item,
  .badge-icon,
  .achievement-stats .stat,
  .achievement-tips {
    background: #2a2a2a;
  }

  .filter-btn,
  .category-btn,
  .currency-item {
    background: #2a2a2a;
    border-color: #444444;
    color: #ffffff;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .stats-rankings {
    grid-template-columns: 1fr;
  }

  .challenges-container {
    grid-template-columns: 1fr;
  }

  .badges-showcase {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .user-level-card {
    flex-direction: column;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .shop-items {
    grid-template-columns: 1fr;
  }

  .achievement-stats {
    grid-template-columns: 1fr;
  }

  .badges-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>