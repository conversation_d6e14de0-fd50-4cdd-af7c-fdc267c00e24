/**
 * Sistema Ultra Avançado de Revisões Espaçadas
 * Baseado na metodologia científica de retenção de conhecimento
 */

/**
 * Calcula a data do primeiro contato com questões após estudo teórico
 * @param {Date} studyDate - Data do estudo teórico
 * @param {string} difficulty - 'easy' | 'medium' | 'hard'
 * @returns {Date} Data do primeiro contato
 */
export function calculateFirstContactDate(studyDate, difficulty) {
  const date = new Date(studyDate);
  const daysToAdd = difficulty === 'easy' ? 2 : 1;
  date.setDate(date.getDate() + daysToAdd);
  return date;
}

/**
 * Calcula o intervalo de revisão baseado no desempenho
 * @param {number} percentage - Percentual de acertos (0-100)
 * @returns {number} Dias até próxima revisão
 */
export function calculateRevisionInterval(percentage) {
  const perc = percentage / 100;
  
  if (perc <= 0.5) return 2;
  if (perc <= 0.55) return 7;
  if (perc <= 0.6) return 14;
  if (perc <= 0.65) return 18;
  if (perc <= 0.75) return 24;
  if (perc <= 0.8) return 30;
  return 35;
}

/**
 * Calcula a próxima data de revisão baseada no desempenho
 * @param {Date} currentDate - Data atual
 * @param {number} percentage - Percentual de acertos
 * @returns {Date} Data da próxima revisão
 */
export function calculateNextRevisionDate(currentDate, percentage) {
  const date = new Date(currentDate);
  const daysToAdd = calculateRevisionInterval(percentage);
  date.setDate(date.getDate() + daysToAdd);
  return date;
}

/**
 * Gera um bloco de questões com configurações ideais
 * @param {string} subject - Matéria
 * @param {string} topic - Tópico específico
 * @param {number} count - Número de questões (padrão: 30)
 * @returns {Object} Configuração do bloco
 */
export function generateQuestionBlock(subject, topic, count = 30) {
  return {
    id: Date.now(),
    subject,
    topic,
    questionCount: Math.min(Math.max(count, 20), 30), // Entre 20-30
    timeLimit: count * 2, // 2 minutos por questão
    createdAt: new Date(),
    status: 'pending'
  };
}

/**
 * Analisa o desempenho e sugere ações
 * @param {number} percentage - Percentual de acertos
 * @param {number} cycleNumber - Número do ciclo de revisão
 * @returns {Object} Análise e recomendações
 */
export function analyzePerformance(percentage, cycleNumber = 1) {
  const analysis = {
    percentage,
    cycleNumber,
    level: '',
    recommendation: '',
    nextAction: '',
    retentionProbability: 0,
    suggestedStudyTime: 0
  };

  if (percentage <= 50) {
    analysis.level = 'critical';
    analysis.recommendation = 'Revisar conceitos básicos antes de continuar';
    analysis.nextAction = 'theory_review';
    analysis.retentionProbability = 20;
    analysis.suggestedStudyTime = 90;
  } else if (percentage <= 60) {
    analysis.level = 'low';
    analysis.recommendation = 'Focar em pontos fracos identificados';
    analysis.nextAction = 'focused_practice';
    analysis.retentionProbability = 40;
    analysis.suggestedStudyTime = 60;
  } else if (percentage <= 75) {
    analysis.level = 'medium';
    analysis.recommendation = 'Bom progresso! Continue praticando';
    analysis.nextAction = 'regular_practice';
    analysis.retentionProbability = 65;
    analysis.suggestedStudyTime = 45;
  } else if (percentage <= 85) {
    analysis.level = 'good';
    analysis.recommendation = 'Excelente! Manter ritmo de revisões';
    analysis.nextAction = 'maintenance';
    analysis.retentionProbability = 80;
    analysis.suggestedStudyTime = 30;
  } else {
    analysis.level = 'excellent';
    analysis.recommendation = 'Domínio do conteúdo! Expandir para tópicos relacionados';
    analysis.nextAction = 'advanced_topics';
    analysis.retentionProbability = 95;
    analysis.suggestedStudyTime = 20;
  }

  // Ajustar baseado no ciclo
  if (cycleNumber > 3) {
    analysis.retentionProbability = Math.min(analysis.retentionProbability + (cycleNumber * 2), 99);
  }

  return analysis;
}

/**
 * Calcula a curva de esquecimento de Ebbinghaus
 * @param {Date} lastReviewDate - Data da última revisão
 * @param {number} retentionRate - Taxa de retenção inicial (%)
 * @returns {number} Taxa de retenção estimada atual
 */
export function calculateForgettingCurve(lastReviewDate, retentionRate) {
  const now = new Date();
  const daysSinceReview = Math.floor((now - lastReviewDate) / (1000 * 60 * 60 * 24));
  
  // Fórmula de Ebbinghaus modificada
  const forgettingRate = Math.exp(-daysSinceReview / 5);
  const currentRetention = retentionRate * forgettingRate;
  
  return Math.max(Math.round(currentRetention), 20); // Mínimo 20%
}

/**
 * Determina a prioridade de revisão
 * @param {Object} revision - Objeto de revisão
 * @returns {string} 'urgent' | 'high' | 'medium' | 'low'
 */
export function calculateRevisionPriority(revision) {
  const daysUntilRevision = Math.floor((new Date(revision.date) - new Date()) / (1000 * 60 * 60 * 24));
  const lastPerformance = revision.lastPerformance || 0;
  
  if (daysUntilRevision < 0) return 'urgent'; // Atrasada
  if (daysUntilRevision === 0) return 'high'; // Hoje
  if (daysUntilRevision <= 2 || lastPerformance < 60) return 'high';
  if (daysUntilRevision <= 7 || lastPerformance < 75) return 'medium';
  return 'low';
}

/**
 * Gera estatísticas de estudo
 * @param {Array} studies - Array de estudos
 * @param {Array} performances - Array de performances
 * @returns {Object} Estatísticas detalhadas
 */
export function generateStudyStatistics(studies, performances) {
  const stats = {
    totalStudies: studies.length,
    totalRevisions: performances.length,
    averagePerformance: 0,
    bestSubject: null,
    worstSubject: null,
    studyStreak: 0,
    predictedRetention: {},
    recommendedFocus: []
  };

  if (performances.length > 0) {
    // Calcular média geral
    stats.averagePerformance = Math.round(
      performances.reduce((sum, p) => sum + p.percentage, 0) / performances.length
    );

    // Agrupar por matéria
    const subjectPerformance = {};
    performances.forEach(p => {
      if (!subjectPerformance[p.subject]) {
        subjectPerformance[p.subject] = { total: 0, count: 0, scores: [] };
      }
      subjectPerformance[p.subject].total += p.percentage;
      subjectPerformance[p.subject].count++;
      subjectPerformance[p.subject].scores.push(p.percentage);
    });

    // Calcular médias por matéria
    Object.keys(subjectPerformance).forEach(subject => {
      const avg = subjectPerformance[subject].total / subjectPerformance[subject].count;
      subjectPerformance[subject].average = Math.round(avg);
      
      // Calcular tendência
      const scores = subjectPerformance[subject].scores;
      if (scores.length > 1) {
        const trend = scores[scores.length - 1] - scores[0];
        subjectPerformance[subject].trend = trend > 0 ? 'improving' : trend < 0 ? 'declining' : 'stable';
      }
    });

    // Identificar melhor e pior matéria
    const subjects = Object.entries(subjectPerformance);
    if (subjects.length > 0) {
      subjects.sort((a, b) => b[1].average - a[1].average);
      stats.bestSubject = { name: subjects[0][0], average: subjects[0][1].average };
      stats.worstSubject = { name: subjects[subjects.length - 1][0], average: subjects[subjects.length - 1][1].average };
      
      // Recomendar foco
      subjects.forEach(([subject, data]) => {
        if (data.average < 70) {
          stats.recommendedFocus.push({
            subject,
            reason: 'Baixo desempenho',
            priority: 'high'
          });
        } else if (data.trend === 'declining') {
          stats.recommendedFocus.push({
            subject,
            reason: 'Tendência de queda',
            priority: 'medium'
          });
        }
      });
    }

    // Calcular retenção prevista
    stats.predictedRetention = calculateOverallRetention(studies, performances);
  }

  // Calcular sequência de estudos
  stats.studyStreak = calculateStudyStreak(studies);

  return stats;
}

/**
 * Calcula a retenção geral prevista
 * @param {Array} studies - Estudos realizados
 * @param {Array} performances - Performances registradas
 * @returns {Object} Previsão de retenção
 */
function calculateOverallRetention(studies, performances) {
  const retention = {
    oneWeek: 0,
    oneMonth: 0,
    threeMonths: 0,
    sixMonths: 0
  };

  if (performances.length === 0) return retention;

  const avgPerformance = performances.reduce((sum, p) => sum + p.percentage, 0) / performances.length;
  const reviewFrequency = performances.length / Math.max(studies.length, 1);
  
  // Modelo simplificado de retenção baseado em performance e frequência
  const baseRetention = avgPerformance * 0.8 + reviewFrequency * 20;
  
  retention.oneWeek = Math.round(baseRetention * 0.9);
  retention.oneMonth = Math.round(baseRetention * 0.7);
  retention.threeMonths = Math.round(baseRetention * 0.5);
  retention.sixMonths = Math.round(baseRetention * 0.3);

  return retention;
}

/**
 * Calcula a sequência atual de dias de estudo
 * @param {Array} studies - Array de estudos
 * @returns {number} Número de dias consecutivos
 */
function calculateStudyStreak(studies) {
  if (studies.length === 0) return 0;

  const sortedDates = studies
    .map(s => new Date(s.date).toDateString())
    .sort((a, b) => new Date(b) - new Date(a));

  let streak = 0;
  const today = new Date();
  
  for (let i = 0; i < 30; i++) {
    const checkDate = new Date(today);
    checkDate.setDate(checkDate.getDate() - i);
    
    if (sortedDates.includes(checkDate.toDateString())) {
      streak++;
    } else if (i > 0) {
      break;
    }
  }

  return streak;
}

/**
 * Sugere o melhor horário para estudar baseado no histórico
 * @param {Array} performances - Array de performances com timestamps
 * @returns {Object} Sugestão de horário
 */
export function suggestOptimalStudyTime(performances) {
  if (performances.length < 5) {
    return {
      timeRange: '14:00 - 18:00',
      reason: 'Horário padrão recomendado',
      confidence: 'low'
    };
  }

  // Agrupar performances por hora do dia
  const hourlyPerformance = {};
  performances.forEach(p => {
    const hour = new Date(p.date).getHours();
    if (!hourlyPerformance[hour]) {
      hourlyPerformance[hour] = { total: 0, count: 0 };
    }
    hourlyPerformance[hour].total += p.percentage;
    hourlyPerformance[hour].count++;
  });

  // Encontrar melhor horário
  let bestHour = 14;
  let bestAverage = 0;
  
  Object.entries(hourlyPerformance).forEach(([hour, data]) => {
    const avg = data.total / data.count;
    if (avg > bestAverage) {
      bestAverage = avg;
      bestHour = parseInt(hour);
    }
  });

  const startHour = Math.max(bestHour - 1, 6);
  const endHour = Math.min(bestHour + 2, 22);

  return {
    timeRange: `${startHour}:00 - ${endHour}:00`,
    reason: `Melhor desempenho histórico (${Math.round(bestAverage)}%)`,
    confidence: performances.length > 20 ? 'high' : 'medium'
  };
}

export default {
  calculateFirstContactDate,
  calculateRevisionInterval,
  calculateNextRevisionDate,
  generateQuestionBlock,
  analyzePerformance,
  calculateForgettingCurve,
  calculateRevisionPriority,
  generateStudyStatistics,
  suggestOptimalStudyTime
};