/* FontAwesome Background Fix */
/* Remove any backgrounds from FontAwesome components and their containers */

/* Target the Vue component directly */
font-awesome-icon {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Target the rendered SVG */
.svg-inline--fa {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  fill: currentColor;
}

/* Target any wrapper elements */
.fa-layers,
.fa-stack {
  background: none !important;
  background-color: transparent !important;
}

/* Specific fix for revision scheduler icons */
.revision-scheduler .detail-item font-awesome-icon,
.revision-scheduler .detail-item .svg-inline--fa,
.revision-scheduler .detail-item svg {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  fill: currentColor;
}

/* Remove any pseudo-elements that might create backgrounds */
.svg-inline--fa::before,
.svg-inline--fa::after,
font-awesome-icon::before,
font-awesome-icon::after {
  content: none !important;
  background: none !important;
}

/* Ensure no parent element is adding backgrounds to icon containers */
.detail-item > *:has(.svg-inline--fa),
.detail-item > *:has(svg) {
  background: none !important;
  background-color: transparent !important;
}

/* Modern CSS - if browser supports :has() */
@supports selector(:has(*)) {
  *:has(> .svg-inline--fa) {
    background: none !important;
    background-color: transparent !important;
  }
}