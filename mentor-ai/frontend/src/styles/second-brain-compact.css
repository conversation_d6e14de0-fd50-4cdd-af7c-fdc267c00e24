/* Second Brain Compact Styles */

/* Compact Layout Variables */
:root {
  --sb-compact-spacing: 1rem;
  --sb-compact-gap: 0.75rem;
  --sb-compact-radius: 12px;
  --sb-compact-font-size: 0.875rem;
}

/* Compact Container */
.second-brain-compact {
  padding: var(--sb-compact-spacing);
}

/* Compact Header */
.sb-compact-header {
  padding: var(--sb-compact-spacing);
  margin-bottom: var(--sb-compact-spacing);
}

.sb-compact-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

/* Compact Grid */
.sb-compact-grid {
  display: grid;
  gap: var(--sb-compact-gap);
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Compact Cards */
.sb-compact-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: var(--sb-compact-radius);
  padding: var(--sb-compact-spacing);
  transition: all 0.2s ease;
}

.sb-compact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sb-compact-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #e4e6eb;
}

.sb-compact-card-content {
  font-size: var(--sb-compact-font-size);
  color: #94a3b8;
  line-height: 1.5;
}

/* Compact Lists */
.sb-compact-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sb-compact-list-item {
  padding: 0.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  font-size: var(--sb-compact-font-size);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sb-compact-list-item:last-child {
  border-bottom: none;
}

/* Compact Buttons */
.sb-compact-button {
  padding: 0.5rem 1rem;
  font-size: var(--sb-compact-font-size);
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sb-compact-button-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.sb-compact-button-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #e4e6eb;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Compact Tags */
.sb-compact-tag {
  display: inline-flex;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 6px;
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  margin: 0.125rem;
}

/* Compact Search */
.sb-compact-search {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: var(--sb-compact-font-size);
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  color: #e4e6eb;
}

.sb-compact-search:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* Compact Sidebar */
.sb-compact-sidebar {
  width: 250px;
  padding: var(--sb-compact-spacing);
  background: rgba(15, 23, 42, 0.5);
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

/* Compact Icons */
.sb-compact-icon {
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Compact */
@media (max-width: 768px) {
  .sb-compact-grid {
    grid-template-columns: 1fr;
  }
  
  .sb-compact-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  }
}