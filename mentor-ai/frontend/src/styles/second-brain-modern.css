/* Second Brain Modern Design System */

/* Color Palette */
:root {
  --second-brain-primary: #6366f1;
  --second-brain-secondary: #8b5cf6;
  --second-brain-accent: #06b6d4;
  --second-brain-success: #10b981;
  --second-brain-warning: #f59e0b;
  --second-brain-error: #ef4444;
  
  /* Dark theme colors */
  --sb-bg-primary: #0f1419;
  --sb-bg-secondary: #1e293b;
  --sb-bg-tertiary: #334155;
  --sb-text-primary: #e4e6eb;
  --sb-text-secondary: #94a3b8;
  --sb-border: rgba(148, 163, 184, 0.1);
  
  /* Gradients */
  --sb-gradient-primary: linear-gradient(135deg, var(--second-brain-primary), var(--second-brain-secondary));
  --sb-gradient-accent: linear-gradient(135deg, var(--second-brain-accent), #0891b2);
  --sb-gradient-success: linear-gradient(135deg, var(--second-brain-success), #059669);
}

/* Base Styles */
.second-brain-container {
  background: var(--sb-bg-primary);
  color: var(--sb-text-primary);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Glass Morphism Effects */
.glass-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid var(--sb-border);
  border-radius: 16px;
  padding: 1.5rem;
}

/* Neon Effects */
.neon-glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.5),
              0 0 40px rgba(99, 102, 241, 0.3),
              0 0 60px rgba(99, 102, 241, 0.1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Button Styles */
.sb-button {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.sb-button-primary {
  background: var(--sb-gradient-primary);
  color: white;
}

.sb-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.sb-button-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: var(--sb-text-primary);
  border: 1px solid var(--sb-border);
}

.sb-button-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  border-color: var(--second-brain-primary);
}

/* Input Styles */
.sb-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--sb-border);
  border-radius: 10px;
  color: var(--sb-text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.sb-input:focus {
  outline: none;
  border-color: var(--second-brain-primary);
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Card Styles */
.sb-card {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid var(--sb-border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.sb-card:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
}

/* Section Headers */
.sb-section-header {
  margin-bottom: 2rem;
  position: relative;
}

.sb-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--sb-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sb-section-subtitle {
  font-size: 0.875rem;
  color: var(--sb-text-secondary);
  margin-top: 0.5rem;
}

/* Grid Layouts */
.sb-grid {
  display: grid;
  gap: 1.5rem;
}

.sb-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.sb-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.sb-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Loading States */
.sb-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.sb-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--sb-border);
  border-top-color: var(--second-brain-primary);
  border-radius: 50%;
  animation: sb-spin 0.8s linear infinite;
}

@keyframes sb-spin {
  to { transform: rotate(360deg); }
}

/* Empty States */
.sb-empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--sb-text-secondary);
}

.sb-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Badges */
.sb-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.sb-badge-primary {
  background: rgba(99, 102, 241, 0.2);
  color: var(--second-brain-primary);
}

.sb-badge-success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--second-brain-success);
}

.sb-badge-warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--second-brain-warning);
}

/* Tooltips */
.sb-tooltip {
  position: relative;
}

.sb-tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  margin-bottom: 0.5rem;
}

.sb-tooltip:hover .sb-tooltip-content {
  opacity: 1;
}

/* Animations */
@keyframes sb-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes sb-slide-in {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes sb-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Utility Classes */
.sb-fade-in {
  animation: sb-fade-in 0.3s ease;
}

.sb-slide-in {
  animation: sb-slide-in 0.3s ease;
}

.sb-pulse {
  animation: sb-pulse 2s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sb-grid-2,
  .sb-grid-3,
  .sb-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .sb-section-title {
    font-size: 1.25rem;
  }
  
  .glass-card {
    padding: 1rem;
  }
}