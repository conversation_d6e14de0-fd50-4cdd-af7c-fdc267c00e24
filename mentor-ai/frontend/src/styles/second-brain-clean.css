/* Second Brain Clean Styles */

/* Clean Design Variables */
:root {
  --sb-clean-bg: #0f1419;
  --sb-clean-surface: #1e293b;
  --sb-clean-border: rgba(148, 163, 184, 0.1);
  --sb-clean-text: #e4e6eb;
  --sb-clean-text-secondary: #94a3b8;
  --sb-clean-primary: #6366f1;
  --sb-clean-primary-hover: #818cf8;
  --sb-clean-success: #10b981;
  --sb-clean-warning: #f59e0b;
  --sb-clean-error: #ef4444;
}

/* Clean Container */
.second-brain-clean {
  background: var(--sb-clean-bg);
  color: var(--sb-clean-text);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Clean Header */
.sb-clean-header {
  background: var(--sb-clean-surface);
  border-bottom: 1px solid var(--sb-clean-border);
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
}

.sb-clean-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--sb-clean-text);
}

/* Clean Layout */
.sb-clean-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  padding: 0 2rem 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

/* Clean Sidebar */
.sb-clean-sidebar {
  background: var(--sb-clean-surface);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--sb-clean-border);
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.sb-clean-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sb-clean-nav-item {
  margin-bottom: 0.5rem;
}

.sb-clean-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--sb-clean-text-secondary);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.sb-clean-nav-link:hover {
  background: rgba(148, 163, 184, 0.1);
  color: var(--sb-clean-text);
}

.sb-clean-nav-link.active {
  background: rgba(99, 102, 241, 0.1);
  color: var(--sb-clean-primary);
}

/* Clean Content */
.sb-clean-content {
  background: var(--sb-clean-surface);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--sb-clean-border);
}

/* Clean Cards */
.sb-clean-card {
  background: var(--sb-clean-bg);
  border: 1px solid var(--sb-clean-border);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.sb-clean-card:hover {
  border-color: rgba(99, 102, 241, 0.3);
}

.sb-clean-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.75rem;
  color: var(--sb-clean-text);
}

.sb-clean-card-content {
  color: var(--sb-clean-text-secondary);
  line-height: 1.6;
}

/* Clean Forms */
.sb-clean-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sb-clean-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sb-clean-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--sb-clean-text);
}

.sb-clean-input,
.sb-clean-textarea {
  background: var(--sb-clean-bg);
  border: 1px solid var(--sb-clean-border);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  color: var(--sb-clean-text);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.sb-clean-input:focus,
.sb-clean-textarea:focus {
  outline: none;
  border-color: var(--sb-clean-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Clean Buttons */
.sb-clean-button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.sb-clean-button-primary {
  background: var(--sb-clean-primary);
  color: white;
}

.sb-clean-button-primary:hover {
  background: var(--sb-clean-primary-hover);
}

.sb-clean-button-secondary {
  background: transparent;
  color: var(--sb-clean-text);
  border: 1px solid var(--sb-clean-border);
}

.sb-clean-button-secondary:hover {
  background: rgba(148, 163, 184, 0.1);
  border-color: var(--sb-clean-text-secondary);
}

/* Clean Tags */
.sb-clean-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.sb-clean-tag {
  padding: 0.375rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  color: var(--sb-clean-primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Clean Search */
.sb-clean-search {
  position: relative;
  margin-bottom: 1.5rem;
}

.sb-clean-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  background: var(--sb-clean-bg);
  border: 1px solid var(--sb-clean-border);
  border-radius: 8px;
  color: var(--sb-clean-text);
  font-size: 0.875rem;
}

.sb-clean-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--sb-clean-text-secondary);
}

/* Clean Empty State */
.sb-clean-empty {
  text-align: center;
  padding: 3rem;
  color: var(--sb-clean-text-secondary);
}

.sb-clean-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.sb-clean-empty-text {
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sb-clean-layout {
    grid-template-columns: 1fr;
  }
  
  .sb-clean-sidebar {
    position: static;
    margin-bottom: 2rem;
  }
}

@media (max-width: 640px) {
  .sb-clean-header {
    padding: 1rem;
  }
  
  .sb-clean-content {
    padding: 1rem;
  }
  
  .sb-clean-layout {
    padding: 0 1rem 1rem;
  }
}