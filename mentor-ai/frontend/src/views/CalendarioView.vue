<template>
  <div class="calendario-view">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="calendario-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Calendário de Estudos</h1>
            <p class="page-subtitle">Visualize e organize suas revisões</p>
          </div>
        </div>

        <div class="header-right">
          <div class="view-controls">
            <button 
              v-for="view in viewOptions" 
              :key="view.id"
              @click="currentView = view.id"
              :class="['view-btn', { active: currentView === view.id }]"
            >
              <i :class="view.icon"></i>
              {{ view.name }}
            </button>
          </div>
          
          <div class="calendar-actions">
            <button @click="goToToday" class="action-button">
              <i class="fas fa-calendar-day"></i>
              Hoje
            </button>
            <button @click="showFilters = !showFilters" class="action-button">
              <i class="fas fa-filter"></i>
              Filtros
            </button>
            <button @click="showRevisionSystem = true" class="action-button primary">
              <i class="fas fa-plus"></i>
              Nova Revisão
            </button>
          </div>
        </div>
      </div>

      <!-- Date Navigation -->
      <div class="date-navigation">
        <button @click="previousPeriod" class="nav-button">
          <i class="fas fa-chevron-left"></i>
        </button>
        <div class="current-period">
          <i class="far fa-calendar"></i>
          {{ formattedPeriod }}
        </div>
        <button @click="nextPeriod" class="nav-button">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Filters (Collapsible) -->
    <transition name="slide-fade">
      <div v-if="showFilters" class="filters-section">
        <div class="filters-content">
          <div class="filter-group">
            <label>Matérias</label>
            <div class="filter-chips">
              <button 
                v-for="subject in subjects" 
                :key="subject"
                @click="toggleFilter('subject', subject)"
                :class="['filter-chip', { active: filters.subjects.includes(subject) }]"
              >
                {{ subject }}
              </button>
            </div>
          </div>
          
          <div class="filter-group">
            <label>Tipo</label>
            <div class="filter-chips">
              <button 
                v-for="type in eventTypes" 
                :key="type.id"
                @click="toggleFilter('type', type.id)"
                :class="['filter-chip', { active: filters.types.includes(type.id) }]"
              >
                <i :class="type.icon"></i>
                {{ type.name }}
              </button>
            </div>
          </div>
          
          <div class="filter-group">
            <label>Prioridade</label>
            <div class="filter-chips">
              <button 
                v-for="priority in ['high', 'medium', 'low']" 
                :key="priority"
                @click="toggleFilter('priority', priority)"
                :class="['filter-chip', `priority-${priority}`, { active: filters.priorities.includes(priority) }]"
              >
                {{ priority }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Main Content -->
    <div class="calendario-content">
      <!-- Month View -->
      <div v-if="currentView === 'month'" class="calendar-month">
        <div class="weekdays">
          <div v-for="day in weekDays" :key="day" class="weekday">
            {{ day }}
          </div>
        </div>
        
        <div class="calendar-grid">
          <div 
            v-for="day in calendarDays" 
            :key="day.date"
            class="calendar-day"
            :class="{
              'other-month': !day.isCurrentMonth,
              'today': day.isToday,
              'weekend': day.isWeekend
            }"
            @click="selectDate(day)"
          >
            <div class="day-header">
              <span class="day-number">{{ day.day }}</span>
              <span v-if="day.events.length > 0" class="event-count">
                {{ day.events.length }}
              </span>
            </div>
            
            <div class="day-events">
              <div 
                v-for="(event, idx) in day.events.slice(0, 3)" 
                :key="event.id"
                class="event-item"
                :class="`event-${event.type}`"
                @click.stop="showEventDetail(event)"
              >
                <span class="event-time">{{ formatTime(event.time) }}</span>
                <span class="event-title">{{ event.title }}</span>
              </div>
              <div v-if="day.events.length > 3" class="more-events">
                +{{ day.events.length - 3 }} mais
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Week View -->
      <div v-if="currentView === 'week'" class="calendar-week">
        <div class="week-header">
          <div class="time-column"></div>
          <div 
            v-for="day in weekViewDays" 
            :key="day.date"
            class="week-day-header"
            :class="{ today: day.isToday }"
          >
            <div class="week-day-name">{{ day.dayName }}</div>
            <div class="week-day-number">{{ day.day }}</div>
          </div>
        </div>
        
        <div class="week-body">
          <div class="time-slots">
            <div v-for="hour in 24" :key="hour" class="time-slot">
              <div class="time-label">{{ formatHour(hour - 1) }}</div>
              <div class="week-grid-row">
                <div 
                  v-for="day in weekViewDays" 
                  :key="`${day.date}-${hour}`"
                  class="week-cell"
                  :class="{ today: day.isToday }"
                >
                  <div 
                    v-for="event in getEventsForHour(day.date, hour - 1)" 
                    :key="event.id"
                    class="week-event"
                    :class="`event-${event.type}`"
                    :style="getEventStyle(event)"
                    @click="showEventDetail(event)"
                  >
                    <div class="week-event-time">{{ formatTime(event.time) }}</div>
                    <div class="week-event-title">{{ event.title }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-if="currentView === 'list'" class="calendar-list">
        <div v-if="filteredEvents.length === 0" class="empty-state">
          <i class="fas fa-calendar-times"></i>
          <h3>Nenhum evento encontrado</h3>
          <p>Ajuste os filtros ou adicione novos eventos</p>
        </div>
        
        <div v-else class="events-list">
          <div 
            v-for="(group, date) in groupedEvents" 
            :key="date"
            class="date-group"
          >
            <h3 class="date-header">
              <i class="fas fa-calendar"></i>
              {{ formatDateHeader(date) }}
              <span class="event-count">{{ group.length }} eventos</span>
            </h3>
            
            <div class="events-container">
              <div 
                v-for="event in group" 
                :key="event.id"
                class="list-event"
                :class="`event-${event.type}`"
                @click="showEventDetail(event)"
              >
                <div class="event-time-block">
                  <div class="event-time">{{ formatTime(event.time) }}</div>
                  <div class="event-duration">{{ event.duration || 60 }} min</div>
                </div>
                
                <div class="event-content">
                  <h4 class="event-title">{{ event.title }}</h4>
                  <div class="event-meta">
                    <span class="event-subject">
                      <i class="fas fa-tag"></i>
                      {{ event.subject }}
                    </span>
                    <span class="event-type">
                      <i :class="getEventIcon(event.type)"></i>
                      {{ getEventTypeName(event.type) }}
                    </span>
                    <span v-if="event.priority" class="event-priority" :class="`priority-${event.priority}`">
                      {{ event.priority }}
                    </span>
                  </div>
                </div>
                
                <div class="event-actions">
                  <button @click.stop="startEvent(event)" class="event-action">
                    <i class="fas fa-play"></i>
                  </button>
                  <button @click.stop="editEvent(event)" class="event-action">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Detail Modal -->
    <transition name="modal">
      <div v-if="selectedEvent" class="event-modal-overlay" @click="selectedEvent = null">
        <div class="event-modal" @click.stop>
          <div class="modal-header">
            <h2>{{ selectedEvent.title }}</h2>
            <button @click="selectedEvent = null" class="close-button">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <div class="event-detail">
              <i class="fas fa-calendar"></i>
              <span>{{ formatFullDate(selectedEvent.date) }}</span>
            </div>
            <div class="event-detail">
              <i class="fas fa-clock"></i>
              <span>{{ formatTime(selectedEvent.time) }} - {{ selectedEvent.duration || 60 }} minutos</span>
            </div>
            <div class="event-detail">
              <i class="fas fa-tag"></i>
              <span>{{ selectedEvent.subject }}</span>
            </div>
            <div v-if="selectedEvent.description" class="event-detail">
              <i class="fas fa-align-left"></i>
              <p>{{ selectedEvent.description }}</p>
            </div>
            <div v-if="selectedEvent.previousPerformance" class="event-detail">
              <i class="fas fa-chart-line"></i>
              <span>Desempenho anterior: {{ selectedEvent.previousPerformance }}%</span>
            </div>
          </div>
          
          <div class="modal-footer">
            <button @click="startEvent(selectedEvent)" class="modal-action primary">
              <i class="fas fa-play"></i>
              Iniciar
            </button>
            <button @click="editEvent(selectedEvent)" class="modal-action">
              <i class="fas fa-edit"></i>
              Editar
            </button>
            <button @click="deleteEvent(selectedEvent)" class="modal-action danger">
              <i class="fas fa-trash"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- RevisionSystemIntegrated - Sistema inteligente de revisões -->
    <RevisionSystemIntegrated 
      v-if="showRevisionSystem"
      @close="showRevisionSystem = false"
      @estudo-registrado="handleEstudoRegistrado"
      @performance-registrada="handlePerformanceRegistrada"
      @revisao-agendada="handleRevisaoAgendada"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import RevisionSystemIntegrated from '@/components/RevisionSystemIntegrated.vue'

export default {
  name: 'CalendarioView',
  components: {
    RevisionSystemIntegrated
  },
  setup() {
    const router = useRouter()
    
    // State
    const currentView = ref('month')
    const currentDate = ref(new Date())
    const selectedDate = ref(null)
    const selectedEvent = ref(null)
    const showFilters = ref(false)
    const showRevisionSystem = ref(false)
    
    // Data
    const events = ref([])
    const filters = ref({
      subjects: [],
      types: [],
      priorities: []
    })
    
    // View Options
    const viewOptions = ref([
      { id: 'month', name: 'Mês', icon: 'fas fa-calendar' },
      { id: 'week', name: 'Semana', icon: 'fas fa-calendar-week' },
      { id: 'list', name: 'Lista', icon: 'fas fa-list' }
    ])
    
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
    
    const eventTypes = ref([
      { id: 'study', name: 'Estudo', icon: 'fas fa-book' },
      { id: 'questions', name: 'Questões', icon: 'fas fa-question-circle' },
      { id: 'revision', name: 'Revisão', icon: 'fas fa-redo' },
      { id: 'exam', name: 'Prova', icon: 'fas fa-file-alt' }
    ])
    
    // Computed
    const subjects = computed(() => {
      const uniqueSubjects = [...new Set(events.value.map(e => e.subject).filter(Boolean))]
      return uniqueSubjects.sort()
    })
    
    const formattedPeriod = computed(() => {
      if (currentView.value === 'month') {
        return currentDate.value.toLocaleDateString('pt-BR', { 
          month: 'long', 
          year: 'numeric' 
        })
      } else if (currentView.value === 'week') {
        const weekStart = getWeekStart(currentDate.value)
        const weekEnd = getWeekEnd(currentDate.value)
        return `${weekStart.getDate()} - ${weekEnd.getDate()} de ${weekStart.toLocaleDateString('pt-BR', { month: 'long' })}`
      }
      return ''
    })
    
    const filteredEvents = computed(() => {
      return events.value.filter(event => {
        if (filters.value.subjects.length > 0 && !filters.value.subjects.includes(event.subject)) {
          return false
        }
        if (filters.value.types.length > 0 && !filters.value.types.includes(event.type)) {
          return false
        }
        if (filters.value.priorities.length > 0 && !filters.value.priorities.includes(event.priority)) {
          return false
        }
        return true
      })
    })
    
    const calendarDays = computed(() => {
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const dateStr = date.toISOString().split('T')[0]
        const dayEvents = filteredEvents.value.filter(e => e.date === dateStr)
        
        days.push({
          date: dateStr,
          day: date.getDate(),
          isCurrentMonth: date.getMonth() === month,
          isToday: date.getTime() === today.getTime(),
          isWeekend: date.getDay() === 0 || date.getDay() === 6,
          events: dayEvents
        })
      }
      
      return days
    })
    
    const weekViewDays = computed(() => {
      const weekStart = getWeekStart(currentDate.value)
      const days = []
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(weekStart)
        date.setDate(weekStart.getDate() + i)
        
        days.push({
          date: date.toISOString().split('T')[0],
          day: date.getDate(),
          dayName: weekDays[date.getDay()],
          isToday: date.getTime() === today.getTime()
        })
      }
      
      return days
    })
    
    const groupedEvents = computed(() => {
      const groups = {}
      
      filteredEvents.value
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .forEach(event => {
          if (!groups[event.date]) {
            groups[event.date] = []
          }
          groups[event.date].push(event)
        })
      
      return groups
    })
    
    // Methods
    const loadEvents = () => {
      // Load from all sources
      const studies = JSON.parse(localStorage.getItem('studies') || '[]')
      const performances = JSON.parse(localStorage.getItem('performances') || '[]')
      const revisions = JSON.parse(localStorage.getItem('revisions') || '[]')
      
      const allEvents = []
      
      // Convert studies to events
      studies.forEach(study => {
        if (study.scheduling && study.scheduling.firstRevisionDate) {
          allEvents.push({
            id: `study-${study.id}`,
            title: `Questões: ${study.title}`,
            subject: study.subject,
            date: new Date(study.scheduling.firstRevisionDate).toISOString().split('T')[0],
            time: '09:00',
            type: 'questions',
            priority: 'medium',
            studyId: study.id
          })
        }
      })
      
      // Add revisions
      revisions.forEach(revision => {
        allEvents.push({
          id: revision.id,
          title: revision.title,
          subject: revision.subject,
          date: new Date(revision.date).toISOString().split('T')[0],
          time: revision.time || '09:00',
          type: revision.type || 'revision',
          priority: revision.priority || 'medium',
          previousPerformance: revision.previousPerformance
        })
      })
      
      events.value = allEvents
    }
    
    const getWeekStart = (date) => {
      const d = new Date(date)
      const day = d.getDay()
      const diff = d.getDate() - day
      return new Date(d.setDate(diff))
    }
    
    const getWeekEnd = (date) => {
      const d = new Date(date)
      const day = d.getDay()
      const diff = d.getDate() - day + 6
      return new Date(d.setDate(diff))
    }
    
    const previousPeriod = () => {
      if (currentView.value === 'month') {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
      } else if (currentView.value === 'week') {
        currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() - 7))
      }
    }
    
    const nextPeriod = () => {
      if (currentView.value === 'month') {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
      } else if (currentView.value === 'week') {
        currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() + 7))
      }
    }
    
    const goToToday = () => {
      currentDate.value = new Date()
    }
    
    const selectDate = (day) => {
      selectedDate.value = day.date
      if (day.events.length === 1) {
        showEventDetail(day.events[0])
      }
    }
    
    const showEventDetail = (event) => {
      selectedEvent.value = event
    }
    
    const startEvent = (event) => {
      if (event.type === 'questions') {
        router.push(`/revisoes?start=${event.id}`)
      } else {
        console.log('Starting event:', event)
      }
    }
    
    const editEvent = (event) => {
      console.log('Editing event:', event)
    }
    
    const deleteEvent = (event) => {
      if (confirm('Tem certeza que deseja excluir este evento?')) {
        events.value = events.value.filter(e => e.id !== event.id)
        selectedEvent.value = null
      }
    }
    
    const toggleFilter = (type, value) => {
      const filterArray = filters.value[type + 's']
      const index = filterArray.indexOf(value)
      if (index > -1) {
        filterArray.splice(index, 1)
      } else {
        filterArray.push(value)
      }
    }
    
    const formatTime = (time) => {
      if (!time) return ''
      return time.substring(0, 5)
    }
    
    const formatHour = (hour) => {
      return `${hour.toString().padStart(2, '0')}:00`
    }
    
    const formatDateHeader = (date) => {
      const d = new Date(date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (d.getTime() === today.getTime()) {
        return 'Hoje'
      }
      
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      if (d.getTime() === tomorrow.getTime()) {
        return 'Amanhã'
      }
      
      return d.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      })
    }
    
    const formatFullDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      })
    }
    
    const getEventsForHour = (date, hour) => {
      return filteredEvents.value.filter(e => {
        if (e.date !== date) return false
        const eventHour = parseInt(e.time?.split(':')[0] || 9)
        return eventHour === hour
      })
    }
    
    const getEventStyle = (event) => {
      const duration = event.duration || 60
      const height = (duration / 60) * 100
      return {
        height: `${height}%`
      }
    }
    
    const getEventIcon = (type) => {
      const typeObj = eventTypes.value.find(t => t.id === type)
      return typeObj?.icon || 'fas fa-calendar'
    }
    
    const getEventTypeName = (type) => {
      const typeObj = eventTypes.value.find(t => t.id === type)
      return typeObj?.name || type
    }
    
    const handleEstudoRegistrado = (estudo) => {
      // Adicionar estudo ao calendário
      const newEvent = {
        id: `est-${estudo.id}`,
        title: `Estudo: ${estudo.titulo}`,
        subject: estudo.materia,
        date: estudo.data,
        time: '08:00',
        type: 'study',
        priority: estudo.importancia || 'medium',
        description: estudo.notas || 'Estudo teórico'
      }
      
      events.value.push(newEvent)
      localStorage.setItem('calendar_events', JSON.stringify(events.value))
      
      // Adicionar primeira revisão
      if (estudo.proximaRevisao) {
        const revisaoEvent = {
          id: `rev-${Date.now()}`,
          title: `Primeira Revisão: ${estudo.titulo}`,
          subject: estudo.materia,
          date: estudo.proximaRevisao,
          time: '09:00',
          type: 'questions',
          priority: 'high',
          description: 'Primeiro contato com questões'
        }
        
        events.value.push(revisaoEvent)
        localStorage.setItem('calendar_events', JSON.stringify(events.value))
      }
      
      loadEvents()
    }
    
    const handlePerformanceRegistrada = (performance) => {
      // Atualizar métricas no calendário se necessário
      console.log('Performance registrada:', performance)
      loadEvents()
    }
    
    const handleRevisaoAgendada = (revisao) => {
      // Adicionar revisão ao calendário
      const newEvent = {
        id: `rev-${revisao.id}`,
        title: revisao.titulo || revisao.title,
        subject: revisao.materia || revisao.subject,
        date: revisao.data || revisao.date,
        time: '09:00',
        type: revisao.tipo || revisao.type || 'revision',
        priority: revisao.prioridade || revisao.priority || 'medium',
        description: 'Revisão agendada automaticamente'
      }
      
      events.value.push(newEvent)
      localStorage.setItem('calendar_events', JSON.stringify(events.value))
      loadEvents()
    }
    
    onMounted(() => {
      loadEvents()
    })
    
    return {
      // State
      currentView,
      currentDate,
      selectedDate,
      selectedEvent,
      showFilters,
      showRevisionSystem,
      filters,
      
      // Data
      viewOptions,
      weekDays,
      eventTypes,
      subjects,
      
      // Computed
      formattedPeriod,
      filteredEvents,
      calendarDays,
      weekViewDays,
      groupedEvents,
      
      // Methods
      previousPeriod,
      nextPeriod,
      goToToday,
      selectDate,
      showEventDetail,
      startEvent,
      editEvent,
      deleteEvent,
      toggleFilter,
      formatTime,
      formatHour,
      formatDateHeader,
      formatFullDate,
      getEventsForHour,
      getEventStyle,
      getEventIcon,
      getEventTypeName,
      handleEstudoRegistrado,
      handlePerformanceRegistrada,
      handleRevisaoAgendada
    }
  }
}
</script>

<style scoped>
/* Base Layout */
.calendario-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0e17 0%, #1a1a2e 40%, #16213e 100%);
  color: #fffffe;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #3b82f6 0%, transparent 70%);
  top: -200px;
  right: -200px;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #8b5cf6 0%, transparent 70%);
  bottom: -100px;
  left: -100px;
  animation-delay: -5s;
}

.orb-3 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #06b6d4 0%, transparent 70%);
  top: 50%;
  right: 20%;
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.05); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Header */
.calendario-header {
  position: relative;
  z-index: 10;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 2rem 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1rem;
  color: #94a3b8;
  margin: 0.25rem 0 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.view-controls {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 0.25rem;
}

.view-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 8px;
}

.view-btn:hover {
  color: #e2e8f0;
}

.view-btn.active {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.calendar-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.action-button.primary:hover {
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

/* Date Navigation */
.date-navigation {
  max-width: 1400px;
  margin: 1.5rem auto 0;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.nav-button {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.current-period {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f1f5f9;
  text-transform: capitalize;
}

/* Filters */
.filters-section {
  position: relative;
  z-index: 10;
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  backdrop-filter: blur(10px);
}

.filters-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #94a3b8;
  margin-bottom: 0.75rem;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-chip:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.filter-chip.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.filter-chip.priority-high {
  border-color: rgba(239, 68, 68, 0.3);
}

.filter-chip.priority-high.active {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.filter-chip.priority-medium {
  border-color: rgba(245, 158, 11, 0.3);
}

.filter-chip.priority-medium.active {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.filter-chip.priority-low {
  border-color: rgba(34, 197, 94, 0.3);
}

.filter-chip.priority-low.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

/* Main Content */
.calendario-content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Month View */
.calendar-month {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 1rem;
}

.weekday {
  text-align: center;
  font-weight: 600;
  color: #94a3b8;
  padding: 0.75rem 0;
  font-size: 0.875rem;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: rgba(148, 163, 184, 0.1);
}

.calendar-day {
  background: rgba(15, 23, 42, 0.5);
  min-height: 120px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.calendar-day:hover {
  background: rgba(30, 41, 59, 0.8);
}

.calendar-day.other-month {
  opacity: 0.4;
}

.calendar-day.today {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.calendar-day.weekend {
  background: rgba(99, 102, 241, 0.05);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.day-number {
  font-weight: 600;
  color: #e2e8f0;
}

.event-count {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.event-item {
  background: rgba(255, 255, 255, 0.05);
  border-left: 3px solid;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
}

.event-item:hover {
  transform: translateX(2px);
}

.event-study {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.event-questions {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.event-revision {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.event-exam {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.event-time {
  font-weight: 600;
  color: #94a3b8;
  margin-right: 0.25rem;
}

.event-title {
  color: #e2e8f0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-events {
  text-align: center;
  color: #94a3b8;
  font-size: 0.75rem;
  padding: 0.25rem;
}

/* Week View */
.calendar-week {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.week-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  background: rgba(15, 23, 42, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.time-column {
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

.week-day-header {
  text-align: center;
  padding: 1rem;
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

.week-day-header.today {
  background: rgba(59, 130, 246, 0.1);
}

.week-day-name {
  font-weight: 600;
  color: #94a3b8;
  font-size: 0.875rem;
}

.week-day-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #e2e8f0;
  margin-top: 0.25rem;
}

.week-body {
  max-height: 600px;
  overflow-y: auto;
}

.time-slot {
  display: grid;
  grid-template-columns: 80px 1fr;
  border-bottom: 1px solid rgba(148, 163, 184, 0.05);
}

.time-label {
  padding: 0.5rem;
  font-size: 0.75rem;
  color: #94a3b8;
  text-align: center;
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

.week-grid-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.week-cell {
  min-height: 60px;
  border-right: 1px solid rgba(148, 163, 184, 0.05);
  padding: 0.25rem;
  position: relative;
}

.week-cell.today {
  background: rgba(59, 130, 246, 0.05);
}

.week-event {
  position: absolute;
  left: 0.25rem;
  right: 0.25rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s;
}

.week-event:hover {
  transform: scale(1.02);
  z-index: 10;
}

.week-event-time {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.week-event-title {
  font-size: 0.813rem;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* List View */
.calendar-list {
  min-height: 400px;
}

.date-group {
  margin-bottom: 2rem;
}

.date-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.date-header .event-count {
  margin-left: auto;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.events-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.list-event {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  gap: 1.5rem;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.list-event:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.event-time-block {
  text-align: center;
  min-width: 80px;
}

.event-time-block .event-time {
  font-size: 1.25rem;
  font-weight: 700;
  color: #3b82f6;
}

.event-duration {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.event-content {
  flex: 1;
}

.event-content .event-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  white-space: normal;
}

.event-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.event-subject,
.event-type {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.event-priority {
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.event-actions {
  display: flex;
  gap: 0.5rem;
}

.event-action {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.event-action:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border-color: rgba(255, 255, 255, 0.2);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #94a3b8;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #e2e8f0;
}

/* Event Modal */
.event-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.event-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #f1f5f9;
}

.close-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 2rem;
}

.event-detail {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.event-detail i {
  color: #94a3b8;
  margin-top: 0.125rem;
}

.event-detail span,
.event-detail p {
  color: #e2e8f0;
  margin: 0;
}

.modal-footer {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.3);
}

.modal-action {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.modal-action:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.modal-action.primary {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border: none;
  color: white;
}

.modal-action.primary:hover {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.modal-action.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.modal-action.danger:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
}

/* Animations */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(10px);
  opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Scrollbar */
.week-body::-webkit-scrollbar,
.calendario-view::-webkit-scrollbar {
  width: 8px;
}

.week-body::-webkit-scrollbar-track,
.calendario-view::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.week-body::-webkit-scrollbar-thumb,
.calendario-view::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.week-body::-webkit-scrollbar-thumb:hover,
.calendario-view::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-right {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }
  
  .view-controls {
    width: 100%;
  }
  
  .calendar-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .filters-content {
    flex-direction: column;
  }
  
  .calendar-day {
    min-height: 80px;
    padding: 0.5rem;
  }
  
  .event-item {
    font-size: 0.688rem;
  }
  
  .week-header {
    grid-template-columns: 60px repeat(7, 1fr);
  }
  
  .list-event {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .event-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>