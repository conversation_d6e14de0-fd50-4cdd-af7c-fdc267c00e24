<template>
  <div class="revisoes-unified">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="revisoes-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Sistema de Revisões Inteligente</h1>
            <p class="page-subtitle">Metodologia científica de repetição espaçada</p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="showNewRevisionSection = !showNewRevisionSection" class="action-button primary">
            <i class="fas fa-plus"></i>
            <span>Nova Revisão</span>
            <div class="button-glow"></div>
          </button>
          <button @click="showQuickQuestionsSection = !showQuickQuestionsSection" class="action-button">
            <i class="fas fa-question-circle"></i>
            <span>Questões Rápidas</span>
          </button>
          <button @click="showPerformanceAnalytics = true" class="action-button">
            <i class="fas fa-chart-line"></i>
            <span>Análise</span>
          </button>
          <button @click="showEnhancedCalendar = true" class="action-button">
            <i class="fas fa-calendar-alt"></i>
            <span>Calendário</span>
          </button>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon studies">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalStudies }}</div>
            <div class="stat-label">Estudos Registrados</div>
          </div>
          <div class="stat-trend" :class="studiesTrend.class">
            <i :class="studiesTrend.icon"></i>
            {{ studiesTrend.value }}%
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon questions">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalQuestions }}</div>
            <div class="stat-label">Questões Resolvidas</div>
          </div>
          <div class="stat-trend" :class="questionsTrend.class">
            <i :class="questionsTrend.icon"></i>
            {{ questionsTrend.value }}%
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon performance">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ averagePerformance }}%</div>
            <div class="stat-label">Taxa de Acerto</div>
          </div>
          <div class="stat-trend" :class="performanceTrend.class">
            <i :class="performanceTrend.icon"></i>
            {{ performanceTrend.value }}%
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon upcoming">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ upcomingRevisions }}</div>
            <div class="stat-label">Revisões Pendentes</div>
          </div>
          <div class="stat-badge" v-if="todayRevisions > 0">
            Hoje: {{ todayRevisions }}
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="revisoes-content">
      <!-- Analytics Section (Collapsible) -->
      <transition name="slide-fade">
        <div v-if="showAnalytics" class="analytics-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-analytics"></i>
              Análise de Desempenho
            </h2>
            <button @click="showAnalytics = false" class="collapse-btn">
              <i class="fas fa-chevron-up"></i>
            </button>
          </div>
          <div class="analytics-grid">
            <div class="chart-container">
              <h3>Progresso por Matéria</h3>
              <canvas ref="subjectChart"></canvas>
            </div>
            <div class="chart-container">
              <h3>Curva de Retenção</h3>
              <canvas ref="retentionChart"></canvas>
            </div>
            <div class="chart-container">
              <h3>Atividade Semanal</h3>
              <canvas ref="activityChart"></canvas>
            </div>
            <div class="chart-container">
              <h3>Taxa de Acerto por Tipo</h3>
              <canvas ref="performanceChart"></canvas>
            </div>
          </div>
        </div>
      </transition>

      <!-- Calendar Section (Collapsible) -->
      <transition name="slide-fade">
        <div v-if="showCalendar" class="calendar-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-calendar-alt"></i>
              Calendário de Revisões
            </h2>
            <button @click="showCalendar = false" class="collapse-btn">
              <i class="fas fa-chevron-up"></i>
            </button>
          </div>
          <SimpleCalendar 
            :events="calendarEvents"
            @date-click="handleDateClick"
            @event-click="handleEventClick"
          />
        </div>
      </transition>

      <!-- New Revision Section (Collapsible) -->
      <transition name="slide-fade">
        <div v-if="showNewRevisionSection" class="new-revision-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-plus-circle"></i>
              Sistema de Agendamento de Revisões
            </h2>
            <button @click="showNewRevisionSection = false" class="collapse-btn">
              <i class="fas fa-chevron-up"></i>
            </button>
          </div>
          <div class="revision-options">
            <div class="option-card" @click="openRevisionScheduler">
              <div class="option-icon">
                <i class="fas fa-calendar-plus"></i>
              </div>
              <h3>Agendar Revisão Simples</h3>
              <p>Agende revisões de forma rápida e prática</p>
            </div>
            <div class="option-card" @click="openRevisionTool">
              <div class="option-icon">
                <i class="fas fa-brain"></i>
              </div>
              <h3>Sistema Inteligente</h3>
              <p>Registro detalhado com análise preditiva</p>
            </div>
            <div class="option-card" @click="openBatchScheduling">
              <div class="option-icon">
                <i class="fas fa-layer-group"></i>
              </div>
              <h3>Agendamento em Lote</h3>
              <p>Agende múltiplas revisões de uma vez</p>
            </div>
          </div>
        </div>
      </transition>
      
      <!-- Quick Questions Section (Collapsible) -->
      <transition name="slide-fade">
        <div v-if="showQuickQuestionsSection" class="quick-questions-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-question-circle"></i>
              Central de Questões
            </h2>
            <button @click="showQuickQuestionsSection = false" class="collapse-btn">
              <i class="fas fa-chevron-up"></i>
            </button>
          </div>
          <div class="questions-content">
            <div class="questions-stats">
              <div class="stat-box">
                <i class="fas fa-tasks"></i>
                <div>
                  <span class="stat-number">{{ todayQuestions }}</span>
                  <span class="stat-label">Questões Hoje</span>
                </div>
              </div>
              <div class="stat-box">
                <i class="fas fa-fire"></i>
                <div>
                  <span class="stat-number">{{ currentStreak }}</span>
                  <span class="stat-label">Dias Consecutivos</span>
                </div>
              </div>
              <div class="stat-box">
                <i class="fas fa-trophy"></i>
                <div>
                  <span class="stat-number">{{ weeklyGoalProgress }}%</span>
                  <span class="stat-label">Meta Semanal</span>
                </div>
              </div>
            </div>
            <div class="subject-selection">
              <h3>Selecione a Matéria</h3>
              <div class="subject-grid">
                <button 
                  v-for="subject in availableSubjects" 
                  :key="subject"
                  @click="startQuickQuestions(subject)"
                  class="subject-card"
                >
                  <i :class="getSubjectIcon(subject)"></i>
                  <span class="subject-name">{{ subject }}</span>
                  <span class="subject-count">{{ getSubjectQuestionCount(subject) }} questões</span>
                </button>
              </div>
            </div>
            <div class="quick-actions-bottom">
              <button @click="startRandomQuestions" class="action-btn primary">
                <i class="fas fa-random"></i>
                Questões Aleatórias
              </button>
              <button @click="startWeakestTopics" class="action-btn secondary">
                <i class="fas fa-chart-line"></i>
                Tópicos Fracos
              </button>
            </div>
          </div>
        </div>
      </transition>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <div class="action-card animated fadeInUp" @click="showRevisionScheduler = true" style="animation-delay: 0.1s">
          <div class="action-icon">
            <i class="fas fa-calendar-plus"></i>
            <div class="icon-pulse"></div>
          </div>
          <h3>Agendar Revisão</h3>
          <p>Agende suas revisões de forma inteligente</p>
          <div class="card-glow"></div>
        </div>
        <div class="action-card animated fadeInUp" @click="showQuickQuestions = true" style="animation-delay: 0.2s">
          <div class="action-icon">
            <i class="fas fa-tasks"></i>
            <div class="icon-pulse"></div>
          </div>
          <h3>Resolver Questões</h3>
          <p>Pratique com questões</p>
          <div class="card-glow"></div>
        </div>
        <div class="action-card animated fadeInUp" @click="openFirstContact" style="animation-delay: 0.3s">
          <div class="action-icon">
            <i class="fas fa-star"></i>
            <div class="icon-pulse"></div>
          </div>
          <h3>Primeiro Contato</h3>
          <p>{{ pendingFirstContacts }} pendentes</p>
          <div class="card-glow"></div>
        </div>
        <div class="action-card animated fadeInUp" @click="openOverdueRevisions" style="animation-delay: 0.4s">
          <div class="action-icon">
            <i class="fas fa-exclamation-triangle"></i>
            <div class="icon-pulse"></div>
          </div>
          <h3>Revisões Atrasadas</h3>
          <p>{{ overdueRevisions }} para revisar</p>
          <div class="card-glow"></div>
        </div>
      </div>

      <!-- Gamification Button -->
      <div class="gamification-trigger" @click="showGamification = true">
        <i class="fas fa-trophy"></i>
        <span>Gamificação</span>
        <div class="level-badge">Nível 12</div>
      </div>

      <!-- Tabs -->
      <div class="content-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
          <span v-if="tab.count" class="tab-badge">{{ tab.count }}</span>
        </button>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- Próximas Revisões -->
        <div v-if="activeTab === 'upcoming'" class="revisions-list">
          <div class="list-header">
            <h3>Próximas Revisões</h3>
            <div class="list-filters">
              <select v-model="filterSubject" class="filter-select">
                <option value="">Todas as matérias</option>
                <option v-for="subject in subjects" :key="subject">{{ subject }}</option>
              </select>
              <select v-model="filterPriority" class="filter-select">
                <option value="">Todas as prioridades</option>
                <option value="high">Alta</option>
                <option value="medium">Média</option>
                <option value="low">Baixa</option>
              </select>
            </div>
          </div>

          <div v-if="filteredUpcomingRevisions.length === 0" class="empty-state">
            <i class="fas fa-calendar-check"></i>
            <h3>Nenhuma revisão pendente</h3>
            <p>Registre novos estudos para começar</p>
            <button @click="showRevisionTool = true" class="empty-action-btn">
              <i class="fas fa-plus"></i>
              Registrar Estudo/Agendar Revisão
            </button>
          </div>

          <transition-group name="list" tag="div" v-else>
            <div 
              v-for="revision in filteredUpcomingRevisions" 
              :key="revision.id"
              class="revision-card"
              :class="getRevisionClass(revision)"
            >
              <div class="revision-header">
                <div class="revision-info">
                  <h3>{{ revision.title }}</h3>
                  <div class="revision-meta">
                    <span class="subject-tag">
                      <i class="fas fa-tag"></i>
                      {{ revision.subject }}
                    </span>
                    <span class="date-tag">
                      <i class="fas fa-calendar"></i>
                      {{ formatDate(revision.date) }}
                    </span>
                    <span class="type-tag" v-if="revision.type">
                      <i :class="getTypeIcon(revision.type)"></i>
                      {{ getTypeName(revision.type) }}
                    </span>
                  </div>
                </div>
                <div class="revision-priority" :class="`priority-${revision.priority}`">
                  <i :class="getPriorityIcon(revision.priority)"></i>
                  {{ revision.priority }}
                </div>
              </div>
              
              <div class="revision-progress" v-if="revision.previousPerformance">
                <div class="progress-label">Desempenho anterior: {{ revision.previousPerformance }}%</div>
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    :style="{ width: revision.previousPerformance + '%' }"
                    :class="getPerformanceClass(revision.previousPerformance)"
                  ></div>
                </div>
              </div>

              <div class="revision-actions">
                <button @click="startRevision(revision)" class="action-btn primary">
                  <i class="fas fa-play"></i>
                  Iniciar
                </button>
                <button @click="postponeRevision(revision)" class="action-btn">
                  <i class="fas fa-clock"></i>
                  Adiar
                </button>
                <button @click="editRevision(revision)" class="action-btn">
                  <i class="fas fa-edit"></i>
                  Editar
                </button>
              </div>
            </div>
          </transition-group>
        </div>

        <!-- Histórico -->
        <div v-if="activeTab === 'history'" class="history-list">
          <div class="list-header">
            <h3>Histórico de Revisões</h3>
            <div class="list-actions">
              <button @click="exportHistory" class="export-btn">
                <i class="fas fa-download"></i>
                Exportar
              </button>
            </div>
          </div>

          <div v-if="historyList.length === 0" class="empty-state">
            <i class="fas fa-history"></i>
            <h3>Nenhum histórico ainda</h3>
            <p>Complete algumas revisões para ver seu progresso</p>
          </div>

          <div v-else class="history-timeline">
            <div 
              v-for="item in historyList" 
              :key="item.id"
              class="history-item"
              @click="showHistoryDetails(item)"
            >
              <div class="timeline-marker" :class="getHistoryMarkerClass(item)">
                <i :class="getHistoryIcon(item)"></i>
              </div>
              <div class="history-content">
                <div class="history-header">
                  <h4>{{ item.title }}</h4>
                  <span class="history-date">{{ formatDateTime(item.completedAt || item.date) }}</span>
                </div>
                <div class="history-details">
                  <span class="performance" :class="getPerformanceClass(item.performance || item.percentage)">
                    <i class="fas fa-percentage"></i>
                    {{ item.performance || item.percentage }}% de acerto
                  </span>
                  <span class="duration" v-if="item.duration">
                    <i class="fas fa-clock"></i>
                    {{ formatDuration(item.duration) }}
                  </span>
                  <span class="questions" v-if="item.totalQuestions">
                    <i class="fas fa-question-circle"></i>
                    {{ item.correctAnswers || item.acertos }}/{{ item.totalQuestions }} questões
                  </span>
                </div>
                <div v-if="item.recommendations && item.recommendations.length" class="recommendations">
                  <p v-for="(rec, idx) in item.recommendations" :key="idx">
                    <i class="fas fa-lightbulb"></i> {{ rec }}
                  </p>
                </div>
                <div v-if="item.nextRevisionDate" class="next-revision-info">
                  <i class="fas fa-calendar-plus"></i>
                  Próxima revisão: {{ formatDate(item.nextRevisionDate) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Estatísticas -->
        <div v-if="activeTab === 'stats'" class="stats-dashboard">
          <div class="stats-period-selector">
            <button 
              v-for="period in ['7d', '30d', '90d', 'all']" 
              :key="period"
              @click="statsPeriod = period"
              :class="['period-btn', { active: statsPeriod === period }]"
            >
              {{ getPeriodLabel(period) }}
            </button>
          </div>

          <div class="stats-grid">
            <div class="stat-box">
              <div class="stat-header">
                <h3>Taxa de Retenção</h3>
                <i class="fas fa-brain stat-icon"></i>
              </div>
              <div class="big-number">{{ retentionRate }}%</div>
              <div class="stat-chart">
                <canvas ref="retentionMiniChart"></canvas>
              </div>
              <p>{{ retentionTrend }}</p>
            </div>

            <div class="stat-box">
              <div class="stat-header">
                <h3>Streak Atual</h3>
                <i class="fas fa-fire stat-icon"></i>
              </div>
              <div class="big-number">{{ currentStreak }}</div>
              <div class="streak-calendar">
                <div 
                  v-for="day in streakCalendar" 
                  :key="day.date"
                  class="streak-day"
                  :class="{ active: day.hasActivity, today: day.isToday }"
                  :title="day.date"
                ></div>
              </div>
              <p>Melhor streak: {{ bestStreak }} dias</p>
            </div>

            <div class="stat-box">
              <div class="stat-header">
                <h3>Tempo Total</h3>
                <i class="fas fa-hourglass-half stat-icon"></i>
              </div>
              <div class="big-number">{{ totalHours }}h</div>
              <div class="time-breakdown">
                <div class="time-item">
                  <span class="time-label">Teoria:</span>
                  <span class="time-value">{{ theoryHours }}h</span>
                </div>
                <div class="time-item">
                  <span class="time-label">Questões:</span>
                  <span class="time-value">{{ questionsHours }}h</span>
                </div>
              </div>
              <p>Média diária: {{ dailyAverage }}h</p>
            </div>

            <div class="stat-box">
              <div class="stat-header">
                <h3>Melhor Matéria</h3>
                <i class="fas fa-trophy stat-icon"></i>
              </div>
              <div class="big-number">{{ bestSubject }}</div>
              <div class="subject-ranking">
                <div 
                  v-for="(subject, idx) in topSubjects" 
                  :key="subject.name"
                  class="subject-rank"
                >
                  <span class="rank-number">{{ idx + 1 }}</span>
                  <span class="rank-name">{{ subject.name }}</span>
                  <span class="rank-score">{{ subject.score }}%</span>
                </div>
              </div>
              <p>{{ bestSubjectPerformance }}% de acerto médio</p>
            </div>

            <div class="stat-box wide">
              <div class="stat-header">
                <h3>Matriz de Desempenho</h3>
                <i class="fas fa-th stat-icon"></i>
              </div>
              <div class="performance-matrix">
                <div 
                  v-for="cell in performanceMatrix" 
                  :key="`${cell.subject}-${cell.type}`"
                  class="matrix-cell"
                  :style="{ backgroundColor: getMatrixColor(cell.score) }"
                  :title="`${cell.subject} - ${cell.type}: ${cell.score}%`"
                >
                  {{ cell.score }}%
                </div>
              </div>
              <div class="matrix-legend">
                <span class="legend-item">
                  <span class="legend-color" style="background: #10b981"></span>
                  Excelente (>80%)
                </span>
                <span class="legend-item">
                  <span class="legend-color" style="background: #3b82f6"></span>
                  Bom (60-80%)
                </span>
                <span class="legend-item">
                  <span class="legend-color" style="background: #f59e0b"></span>
                  Regular (<60%)
                </span>
              </div>
            </div>

            <div class="stat-box wide">
              <div class="stat-header">
                <h3>Insights e Recomendações</h3>
                <i class="fas fa-lightbulb stat-icon"></i>
              </div>
              <div class="insights-list">
                <div v-for="insight in insights" :key="insight.id" class="insight-item">
                  <i :class="insight.icon" :style="{ color: insight.color }"></i>
                  <div class="insight-content">
                    <h4>{{ insight.title }}</h4>
                    <p>{{ insight.description }}</p>
                    <button v-if="insight.action" @click="insight.action.handler" class="insight-action">
                      {{ insight.action.label }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- RevisionSchedulerModal - Novo agendador de revisões -->
    <RevisionSchedulerModal 
      v-if="showRevisionScheduler"
      @close="showRevisionScheduler = false"
      @revision-scheduled="handleRevisionScheduled"
    />
    
    <!-- RevisionSystemIntegrated - Sistema inteligente de revisões com análise detalhada -->
    <RevisionSystemIntegrated 
      v-if="showRevisionTool"
      @close="showRevisionTool = false"
      @estudo-registrado="handleEstudoRegistrado"
      @performance-registrada="handlePerformanceRegistrada"
      @revisao-agendada="handleRevisaoAgendada"
    />

    <!-- QuestionsModal - Para resolver questões -->
    <QuestionsModal
      v-if="showQuestionsModal"
      :contact="selectedQuestionContact"
      @close="showQuestionsModal = false"
      @complete="handleQuestionsComplete"
    />

    <!-- Quick Questions System -->
    <QuickQuestionsSystem 
      v-if="showQuickQuestions"
      @close="showQuickQuestions = false"
      @complete="handleQuestionsComplete"
      @schedule-review="handleScheduleReview"
    />
    
    <!-- Performance Analytics Modal -->
    <transition name="modal-fade">
      <div v-if="showPerformanceAnalytics" class="modal-overlay" @click.self="showPerformanceAnalytics = false">
        <div class="analytics-modal">
          <PerformanceAnalytics @close="showPerformanceAnalytics = false" />
        </div>
      </div>
    </transition>
    
    <!-- Enhanced Calendar Modal -->
    <transition name="modal-fade">
      <div v-if="showEnhancedCalendar" class="modal-overlay" @click.self="showEnhancedCalendar = false">
        <div class="calendar-modal">
          <EnhancedCalendar 
            :events="calendarEvents"
            @event-selected="handleEventSelected"
            @event-created="handleEventCreated"
            @event-updated="handleEventUpdated"
            @event-deleted="handleEventDeleted"
          />
        </div>
      </div>
    </transition>
    
    <!-- Gamification System -->
    <transition name="slide-up">
      <GamificationSystem 
        v-if="showGamification"
        @achievement-unlocked="handleAchievementUnlocked"
        @level-up="handleLevelUp"
        @reward-claimed="handleRewardClaimed"
      />
    </transition>

    <!-- History Details Modal -->
    <div v-if="selectedHistoryItem" class="modal-overlay" @click.self="selectedHistoryItem = null">
      <div class="history-details-modal">
        <div class="modal-header">
          <h2>
            <i class="fas fa-history"></i>
            Detalhes da Revisão
          </h2>
          <button @click="selectedHistoryItem = null" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="detail-section">
            <h3>{{ selectedHistoryItem.title }}</h3>
            <p class="detail-date">{{ formatDateTime(selectedHistoryItem.completedAt || selectedHistoryItem.date) }}</p>
          </div>

          <div class="detail-stats">
            <div class="detail-stat">
              <span class="stat-label">Desempenho</span>
              <span class="stat-value">{{ selectedHistoryItem.performance || selectedHistoryItem.percentage }}%</span>
            </div>
            <div class="detail-stat" v-if="selectedHistoryItem.totalQuestions">
              <span class="stat-label">Questões</span>
              <span class="stat-value">{{ selectedHistoryItem.correctAnswers || selectedHistoryItem.acertos }}/{{ selectedHistoryItem.totalQuestions }}</span>
            </div>
            <div class="detail-stat" v-if="selectedHistoryItem.duration">
              <span class="stat-label">Duração</span>
              <span class="stat-value">{{ formatDuration(selectedHistoryItem.duration) }}</span>
            </div>
          </div>

          <div v-if="selectedHistoryItem.questionDetails" class="question-analysis">
            <h4>Análise por Tipo de Questão</h4>
            <div class="question-type-stats">
              <div 
                v-for="(stat, type) in selectedHistoryItem.questionDetails" 
                :key="type"
                class="type-stat"
              >
                <span class="type-name">{{ type }}</span>
                <div class="type-bar">
                  <div 
                    class="type-fill" 
                    :style="{ width: stat.percentage + '%' }"
                    :class="getPerformanceClass(stat.percentage)"
                  ></div>
                </div>
                <span class="type-score">{{ stat.correct }}/{{ stat.total }}</span>
              </div>
            </div>
          </div>

          <div v-if="selectedHistoryItem.topics" class="topics-covered">
            <h4>Tópicos Abordados</h4>
            <div class="topic-tags">
              <span v-for="topic in selectedHistoryItem.topics" :key="topic" class="topic-tag">
                {{ topic }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Action Button -->
    <div class="fab-container">
      <button @click="toggleFab" class="fab-main">
        <i :class="fabOpen ? 'fas fa-times' : 'fas fa-plus'"></i>
      </button>
      <transition name="fab-menu">
        <div v-if="fabOpen" class="fab-menu">
          <button @click="showRevisionScheduler = true; fabOpen = false" class="fab-item" title="Agendar Revisão">
            <i class="fas fa-calendar-plus"></i>
          </button>
          <button @click="showRevisionTool = true; fabOpen = false" class="fab-item" title="Estudo Teórico">
            <i class="fas fa-book"></i>
          </button>
          <button @click="showQuickQuestions = true; fabOpen = false" class="fab-item" title="Questões Rápidas">
            <i class="fas fa-question"></i>
          </button>
          <button @click="showCalendar = !showCalendar; fabOpen = false" class="fab-item" title="Calendário">
            <i class="fas fa-calendar"></i>
          </button>
          <button @click="showAnalytics = !showAnalytics; fabOpen = false" class="fab-item" title="Analytics">
            <i class="fas fa-chart-line"></i>
          </button>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

// Componentes
import RevisionToolWindow from '@/components/RevisionToolWindow.vue'
import RevisionSchedulerModal from '@/components/RevisionSchedulerModal.vue'
import RevisionSystemIntegrated from '@/components/RevisionSystemIntegrated.vue'
import QuestionsModal from '@/components/QuestionsModal.vue'
import SimpleCalendar from '@/components/SimpleCalendar.vue'
import QuickQuestionsSystem from '@/components/QuickQuestionsSystem.vue'
import PerformanceAnalytics from '@/components/PerformanceAnalytics.vue'
import EnhancedCalendar from '@/components/EnhancedCalendar.vue'
import GamificationSystem from '@/components/GamificationSystem.vue'

// Composables
const router = useRouter()
const store = useStore()

// Estado Reativo
const showRevisionTool = ref(false)
const showRevisionScheduler = ref(false)
const showQuestionsModal = ref(false)
const showQuickQuestions = ref(false)
const showAnalytics = ref(false)
const showCalendar = ref(false)
const showNewRevisionSection = ref(false)
const showQuickQuestionsSection = ref(false)
const activeTab = ref('upcoming')
const selectedQuestionContact = ref(null)
const selectedHistoryItem = ref(null)
const fabOpen = ref(false)
const showGamification = ref(false)
const showEnhancedCalendar = ref(false)
const showPerformanceAnalytics = ref(false)

// Filtros
const filterSubject = ref('')
const filterPriority = ref('')
const statsPeriod = ref('30d')

// Dados
const studies = ref([])
const performances = ref([])
const revisions = ref([])
const calendarEvents = ref([])

// Chart refs
const subjectChart = ref(null)
const retentionChart = ref(null)
const activityChart = ref(null)
const performanceChart = ref(null)
const retentionMiniChart = ref(null)

// Charts instances
let charts = {}

// Tabs Configuration
const tabs = computed(() => [
  { 
    id: 'upcoming', 
    name: 'Próximas Revisões', 
    icon: 'fas fa-calendar-alt', 
    count: filteredUpcomingRevisions.value.length 
  },
  { 
    id: 'history', 
    name: 'Histórico', 
    icon: 'fas fa-history', 
    count: performances.value.length 
  },
  { 
    id: 'stats', 
    name: 'Estatísticas', 
    icon: 'fas fa-chart-bar' 
  }
])

// Computed Properties - Stats
const totalStudies = computed(() => studies.value.length)
const totalQuestions = computed(() => {
  return performances.value.reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
})
const averagePerformance = computed(() => {
  if (performances.value.length === 0) return 0
  const avg = performances.value.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / performances.value.length
  return Math.round(avg)
})
const upcomingRevisions = computed(() => {
  const now = new Date()
  return revisions.value.filter(r => new Date(r.date) > now && !r.completed).length
})
const todayRevisions = computed(() => {
  const today = new Date().toDateString()
  return revisions.value.filter(r => new Date(r.date).toDateString() === today && !r.completed).length
})
const overdueRevisions = computed(() => {
  const now = new Date()
  return revisions.value.filter(r => new Date(r.date) < now && !r.completed).length
})
const pendingFirstContacts = computed(() => {
  return revisions.value.filter(r => r.type === 'questions' && !r.completed).length
})

// Trends
const studiesTrend = computed(() => calculateTrend('studies'))
const questionsTrend = computed(() => calculateTrend('questions'))
const performanceTrend = computed(() => calculateTrend('performance'))

// Filtered Lists
const filteredUpcomingRevisions = computed(() => {
  const now = new Date()
  let filtered = revisions.value.filter(r => new Date(r.date) >= now && !r.completed)
  
  if (filterSubject.value) {
    filtered = filtered.filter(r => r.subject === filterSubject.value)
  }
  
  if (filterPriority.value) {
    filtered = filtered.filter(r => r.priority === filterPriority.value)
  }
  
  return filtered.sort((a, b) => new Date(a.date) - new Date(b.date))
})

const historyList = computed(() => {
  return [...performances.value]
    .sort((a, b) => new Date(b.date || b.completedAt) - new Date(a.date || a.completedAt))
    .slice(0, 50)
})

// Stats Computed
const retentionRate = computed(() => {
  const periodPerformances = getPerformancesByPeriod(statsPeriod.value)
  if (periodPerformances.length === 0) return 0
  const avg = periodPerformances.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / periodPerformances.length
  return Math.round(avg)
})

const retentionTrend = computed(() => {
  const current = retentionRate.value
  const previous = calculatePreviousPeriodRetention()
  const diff = current - previous
  if (diff > 0) return `+${diff}% vs período anterior`
  if (diff < 0) return `${diff}% vs período anterior`
  return 'Estável'
})

const currentStreak = computed(() => {
  let streak = 0
  const today = new Date()
  const sortedDates = getAllActivityDates().sort((a, b) => b - a)
  
  for (let i = 0; i < sortedDates.length; i++) {
    const date = sortedDates[i]
    const diffDays = Math.floor((today - date) / (1000 * 60 * 60 * 24))
    
    if (diffDays === i) {
      streak++
    } else {
      break
    }
  }
  
  return streak
})

const bestStreak = computed(() => {
  // Calculate best streak from history
  return Math.max(currentStreak.value, 14) // Placeholder
})

const streakCalendar = computed(() => {
  const days = []
  const today = new Date()
  const activityDates = getAllActivityDates()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toDateString()
    
    days.push({
      date: dateStr,
      hasActivity: activityDates.some(d => d.toDateString() === dateStr),
      isToday: i === 0
    })
  }
  
  return days
})

const totalHours = computed(() => {
  const studyHours = studies.value.reduce((sum, s) => sum + (s.tempo || s.duration || 0), 0) / 60
  const questionHours = performances.value.reduce((sum, p) => sum + (p.duration || 30), 0) / 60
  return Math.round(studyHours + questionHours)
})

const theoryHours = computed(() => {
  return Math.round(studies.value.reduce((sum, s) => sum + (s.tempo || s.duration || 0), 0) / 60)
})

const questionsHours = computed(() => {
  return Math.round(performances.value.reduce((sum, p) => sum + (p.duration || 30), 0) / 60)
})

const dailyAverage = computed(() => {
  const days = new Set(getAllActivityDates().map(d => d.toDateString())).size || 1
  return (totalHours.value / days).toFixed(1)
})

const bestSubject = computed(() => {
  const subjectStats = calculateSubjectStats()
  if (subjectStats.length === 0) return 'N/A'
  return subjectStats[0].name
})

const bestSubjectPerformance = computed(() => {
  const subjectStats = calculateSubjectStats()
  if (subjectStats.length === 0) return 0
  return Math.round(subjectStats[0].score)
})

const topSubjects = computed(() => {
  return calculateSubjectStats().slice(0, 3)
})

const subjects = computed(() => {
  const subjectSet = new Set()
  studies.value.forEach(s => s.materia && subjectSet.add(s.materia))
  performances.value.forEach(p => p.subject && subjectSet.add(p.subject))
  revisions.value.forEach(r => r.subject && subjectSet.add(r.subject))
  return Array.from(subjectSet).sort()
})

const availableSubjects = computed(() => {
  return subjects.value.length > 0 ? subjects.value : [
    'Anatomia', 'Fisiologia', 'Patologia', 'Farmacologia', 
    'Bioquímica', 'Microbiologia', 'Imunologia'
  ]
})

const performanceMatrix = computed(() => {
  const matrix = []
  const types = ['Teoria', 'Questões', 'Revisão']
  
  subjects.value.forEach(subject => {
    types.forEach(type => {
      const performances = getPerformancesBySubjectAndType(subject, type)
      const score = performances.length > 0
        ? Math.round(performances.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / performances.length)
        : 0
      
      matrix.push({ subject, type, score })
    })
  })
  
  return matrix
})

const insights = computed(() => {
  const insights = []
  
  // Insight 1: Desempenho baixo
  const lowPerformanceSubjects = calculateSubjectStats().filter(s => s.score < 60)
  if (lowPerformanceSubjects.length > 0) {
    insights.push({
      id: 'low-performance',
      icon: 'fas fa-exclamation-triangle',
      color: '#f59e0b',
      title: 'Atenção Necessária',
      description: `${lowPerformanceSubjects.length} matéria(s) com desempenho abaixo de 60%. Considere revisar: ${lowPerformanceSubjects.map(s => s.name).join(', ')}.`,
      action: {
        label: 'Ver Matérias',
        handler: () => {
          filterSubject.value = lowPerformanceSubjects[0].name
          activeTab.value = 'upcoming'
        }
      }
    })
  }
  
  // Insight 2: Revisões atrasadas
  if (overdueRevisions.value > 0) {
    insights.push({
      id: 'overdue',
      icon: 'fas fa-clock',
      color: '#ef4444',
      title: 'Revisões Atrasadas',
      description: `Você tem ${overdueRevisions.value} revisão(ões) atrasada(s). Priorize estas revisões para manter o aprendizado efetivo.`,
      action: {
        label: 'Ver Atrasadas',
        handler: () => openOverdueRevisions()
      }
    })
  }
  
  // Insight 3: Streak
  if (currentStreak.value >= 7) {
    insights.push({
      id: 'streak',
      icon: 'fas fa-fire',
      color: '#10b981',
      title: 'Excelente Consistência!',
      description: `Parabéns! Você está estudando há ${currentStreak.value} dias consecutivos. Continue assim!`,
    })
  }
  
  // Insight 4: Melhoria de desempenho
  const improvement = performanceTrend.value.value
  if (improvement > 5) {
    insights.push({
      id: 'improvement',
      icon: 'fas fa-chart-line',
      color: '#3b82f6',
      title: 'Desempenho em Alta',
      description: `Sua taxa de acerto melhorou ${improvement}% em relação ao período anterior. Ótimo trabalho!`,
    })
  }
  
  // Insight 5: Sugestão de estudo
  const lastStudyDate = studies.value.length > 0 
    ? new Date(Math.max(...studies.value.map(s => new Date(s.data || s.date))))
    : null
  
  if (lastStudyDate) {
    const daysSinceLastStudy = Math.floor((new Date() - lastStudyDate) / (1000 * 60 * 60 * 24))
    if (daysSinceLastStudy > 3) {
      insights.push({
        id: 'study-suggestion',
        icon: 'fas fa-book-reader',
        color: '#8b5cf6',
        title: 'Hora de Estudar Novo Conteúdo',
        description: `Faz ${daysSinceLastStudy} dias desde seu último estudo teórico. Que tal aprender algo novo hoje?`,
        action: {
          label: 'Novo Estudo',
          handler: () => { showRevisionTool.value = true }
        }
      })
    }
  }
  
  return insights
})

// Methods
const loadData = () => {
  // Load from localStorage
  studies.value = JSON.parse(localStorage.getItem('estudos_teoricos') || '[]')
  performances.value = JSON.parse(localStorage.getItem('performances') || localStorage.getItem('revisoes_praticas') || '[]')
  revisions.value = JSON.parse(localStorage.getItem('revisions') || '[]')
  
  // Generate calendar events
  updateCalendarEvents()
  
  // Update charts if analytics is open
  if (showAnalytics.value) {
    nextTick(() => updateCharts())
  }
}

const updateCalendarEvents = () => {
  calendarEvents.value = [
    ...studies.value.map(s => ({
      id: `study-${s.id}`,
      title: s.titulo || s.title,
      date: s.data || s.date,
      type: 'study',
      color: '#3b82f6'
    })),
    ...revisions.value.map(r => ({
      id: `revision-${r.id}`,
      title: r.title,
      date: r.date,
      type: 'revision',
      color: r.priority === 'high' ? '#ef4444' : r.priority === 'medium' ? '#f59e0b' : '#10b981',
      completed: r.completed
    }))
  ]
}

const calculateTrend = (type) => {
  // Calculate trend based on last 7 days vs previous 7 days
  const now = new Date()
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const last14Days = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)
  
  let current = 0
  let previous = 0
  
  switch (type) {
    case 'studies':
      current = studies.value.filter(s => new Date(s.data || s.date) >= last7Days).length
      previous = studies.value.filter(s => {
        const date = new Date(s.data || s.date)
        return date >= last14Days && date < last7Days
      }).length
      break
    case 'questions':
      current = performances.value.filter(p => new Date(p.date) >= last7Days)
        .reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
      previous = performances.value.filter(p => {
        const date = new Date(p.date)
        return date >= last14Days && date < last7Days
      }).reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
      break
    case 'performance':
      const currentPerfs = performances.value.filter(p => new Date(p.date) >= last7Days)
      const previousPerfs = performances.value.filter(p => {
        const date = new Date(p.date)
        return date >= last14Days && date < last7Days
      })
      
      current = currentPerfs.length > 0 
        ? currentPerfs.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / currentPerfs.length
        : 0
      previous = previousPerfs.length > 0
        ? previousPerfs.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / previousPerfs.length
        : 0
      break
  }
  
  const diff = previous > 0 ? Math.round(((current - previous) / previous) * 100) : 0
  
  return {
    value: Math.abs(diff),
    class: diff >= 0 ? 'positive' : 'negative',
    icon: diff >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
  }
}

const getPerformancesByPeriod = (period) => {
  const now = new Date()
  let startDate
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    default:
      return performances.value
  }
  
  return performances.value.filter(p => new Date(p.date || p.completedAt) >= startDate)
}

const calculatePreviousPeriodRetention = () => {
  // Calculate retention for the previous period of the same length
  const now = new Date()
  let days
  
  switch (statsPeriod.value) {
    case '7d':
      days = 7
      break
    case '30d':
      days = 30
      break
    case '90d':
      days = 90
      break
    default:
      return retentionRate.value
  }
  
  const startDate = new Date(now.getTime() - (days * 2) * 24 * 60 * 60 * 1000)
  const endDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
  
  const periodPerfs = performances.value.filter(p => {
    const date = new Date(p.date || p.completedAt)
    return date >= startDate && date < endDate
  })
  
  if (periodPerfs.length === 0) return 0
  
  const avg = periodPerfs.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / periodPerfs.length
  return Math.round(avg)
}

const getAllActivityDates = () => {
  const dates = []
  
  studies.value.forEach(s => {
    dates.push(new Date(s.data || s.date))
  })
  
  performances.value.forEach(p => {
    dates.push(new Date(p.date || p.completedAt))
  })
  
  return dates
}

const calculateSubjectStats = () => {
  const stats = {}
  
  performances.value.forEach(p => {
    const subject = p.subject || p.materia
    if (!subject) return
    
    if (!stats[subject]) {
      stats[subject] = { total: 0, count: 0 }
    }
    
    stats[subject].total += (p.percentage || p.performance || 0)
    stats[subject].count++
  })
  
  return Object.entries(stats)
    .map(([name, data]) => ({
      name,
      score: data.total / data.count
    }))
    .sort((a, b) => b.score - a.score)
}

const getPerformancesBySubjectAndType = (subject, type) => {
  return performances.value.filter(p => {
    const matchSubject = (p.subject || p.materia) === subject
    const matchType = type === 'Teoria' ? p.type === 'study' :
                     type === 'Questões' ? p.type === 'questions' :
                     p.type === 'revision'
    return matchSubject && matchType
  })
}

const getPeriodLabel = (period) => {
  const labels = {
    '7d': 'Última Semana',
    '30d': 'Último Mês',
    '90d': 'Últimos 3 Meses',
    'all': 'Todo Período'
  }
  return labels[period] || period
}

const getSubjectIcon = (subject) => {
  const icons = {
    'Anatomia': 'fas fa-bone',
    'Fisiologia': 'fas fa-heartbeat',
    'Patologia': 'fas fa-virus',
    'Farmacologia': 'fas fa-pills',
    'Bioquímica': 'fas fa-flask',
    'Microbiologia': 'fas fa-bacterium',
    'Imunologia': 'fas fa-shield-virus'
  }
  return icons[subject] || 'fas fa-book'
}

const getRevisionClass = (revision) => {
  const daysUntil = Math.ceil((new Date(revision.date) - new Date()) / (1000 * 60 * 60 * 24))
  if (daysUntil < 0) return 'overdue'
  if (daysUntil === 0) return 'today'
  if (daysUntil <= 2) return 'urgent'
  if (daysUntil <= 7) return 'soon'
  return ''
}

const getTypeIcon = (type) => {
  const icons = {
    'study': 'fas fa-book',
    'questions': 'fas fa-question-circle',
    'revision': 'fas fa-redo',
    'theoretical': 'fas fa-graduation-cap',
    'practical': 'fas fa-tools'
  }
  return icons[type] || 'fas fa-circle'
}

const getTypeName = (type) => {
  const names = {
    'study': 'Estudo',
    'questions': 'Questões',
    'revision': 'Revisão',
    'theoretical': 'Teórico',
    'practical': 'Prático'
  }
  return names[type] || type
}

const getPriorityIcon = (priority) => {
  const icons = {
    'high': 'fas fa-exclamation-circle',
    'medium': 'fas fa-minus-circle',
    'low': 'fas fa-check-circle'
  }
  return icons[priority] || 'fas fa-circle'
}

const getPerformanceClass = (performance) => {
  if (performance >= 80) return 'excellent'
  if (performance >= 60) return 'good'
  return 'needs-improvement'
}

const getHistoryIcon = (item) => {
  const perf = item.performance || item.percentage || 0
  if (perf >= 80) return 'fas fa-star'
  if (perf >= 60) return 'fas fa-check-circle'
  return 'fas fa-exclamation-circle'
}

const getHistoryMarkerClass = (item) => {
  const perf = item.performance || item.percentage || 0
  if (perf >= 80) return 'excellent'
  if (perf >= 60) return 'good'
  return 'needs-improvement'
}

const getMatrixColor = (score) => {
  if (score >= 80) return 'rgba(16, 185, 129, 0.3)'
  if (score >= 60) return 'rgba(59, 130, 246, 0.3)'
  return 'rgba(245, 158, 11, 0.3)'
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('pt-BR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('pt-BR', {
    day: 'numeric',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (minutes) => {
  if (!minutes) return '0min'
  if (minutes < 60) return `${minutes}min`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return mins > 0 ? `${hours}h ${mins}min` : `${hours}h`
}

// Actions
const startRevision = (revision) => {
  selectedQuestionContact.value = {
    id: revision.id,
    title: revision.title,
    subject: revision.subject,
    type: revision.type,
    studyId: revision.studyId,
    metadata: revision.metadata
  }
  showQuestionsModal.value = true
}

const postponeRevision = (revision) => {
  const newDate = new Date(revision.date)
  newDate.setDate(newDate.getDate() + 1)
  
  revision.date = newDate.toISOString()
  revision.postponed = true
  
  // Update localStorage
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  loadData()
}

const editRevision = (revision) => {
  // TODO: Implement edit functionality
  console.log('Edit revision:', revision)
}

const startQuickQuestions = (subject) => {
  selectedQuestionContact.value = {
    title: `Questões Rápidas - ${subject}`,
    subject: subject,
    type: 'quick',
    isQuickSession: true
  }
  showQuickQuestions.value = false
  showQuestionsModal.value = true
}

const openFirstContact = () => {
  filterPriority.value = ''
  activeTab.value = 'upcoming'
  // Filter to show only first contact revisions
}

const openOverdueRevisions = () => {
  filterPriority.value = 'high'
  activeTab.value = 'upcoming'
}

const showHistoryDetails = (item) => {
  selectedHistoryItem.value = item
}

const exportHistory = () => {
  const data = historyList.value.map(item => ({
    Data: formatDateTime(item.date || item.completedAt),
    Título: item.title,
    Matéria: item.subject || item.materia,
    Desempenho: `${item.performance || item.percentage}%`,
    Questões: item.totalQuestions ? `${item.correctAnswers || item.acertos}/${item.totalQuestions}` : 'N/A',
    Duração: formatDuration(item.duration)
  }))
  
  const csv = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csv], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `historico_revisoes_${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  URL.revokeObjectURL(url)
}

const toggleFab = () => {
  fabOpen.value = !fabOpen.value
}

const handleDateClick = (date) => {
  // Handle calendar date click
  console.log('Date clicked:', date)
}

const handleEventClick = (event) => {
  // Handle calendar event click
  if (event.type === 'revision') {
    const revision = revisions.value.find(r => `revision-${r.id}` === event.id)
    if (revision) startRevision(revision)
  }
}

// Event Handlers
const handleEstudoRegistrado = (estudo) => {
  studies.value.push(estudo)
  localStorage.setItem('estudos_teoricos', JSON.stringify(studies.value))
  
  // Criar primeira revisão com questões baseada na dificuldade
  const firstRevision = {
    id: `revision-${Date.now()}`,
    title: `Primeiro Contato - Questões: ${estudo.titulo}`,
    subject: estudo.materia,
    date: estudo.dataPrimeiroContato,
    type: 'questions',
    priority: estudo.dificuldade === 'Difícil' ? 'high' : 'medium',
    studyId: estudo.id,
    metadata: {
      difficulty: estudo.dificuldade,
      studyDuration: estudo.tempo,
      notes: estudo.notas
    }
  }
  
  revisions.value.push(firstRevision)
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  
  loadData()
  showRevisionTool.value = false
}

const handleRevisionScheduled = (data) => {
  // Atualizar dados após agendamento
  loadData()
  
  // Mostrar notificação de sucesso
  if (data.type === 'batch') {
    console.log(`${data.count} revisões agendadas em lote`)
  } else if (data.type === 'import') {
    console.log(`${data.count} revisões importadas`)
  } else {
    console.log('Revisão agendada com sucesso')
  }
  
  // Fechar modal
  showRevisionScheduler.value = false
}

const handleRevisaoRegistrada = (revisao) => {
  performances.value.push(revisao)
  localStorage.setItem('performances', JSON.stringify(performances.value))
  
  // Criar próxima revisão baseada no desempenho
  const nextRevision = {
    id: `revision-${Date.now()}`,
    title: `Revisão Espaçada: ${revisao.titulo}`,
    subject: revisao.materia,
    date: revisao.proximaRevisaoData,
    type: 'revision',
    priority: revisao.percentual < 60 ? 'high' : revisao.percentual < 75 ? 'medium' : 'low',
    previousPerformance: revisao.percentual,
    metadata: {
      previousRevisionsCount: performances.value.filter(p => p.estudoId === revisao.estudoId).length,
      totalQuestions: revisao.totalQuestoes,
      correctAnswers: revisao.acertos
    }
  }
  
  revisions.value.push(nextRevision)
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  
  loadData()
  showRevisionTool.value = false
}

const handleScheduleReview = (reviewData) => {
  // Criar revisão baseada nas questões erradas
  const revision = {
    id: `revision-${Date.now()}`,
    title: `Revisão Focada - ${reviewData.questions.length} questões`,
    subject: reviewData.sessionData.subject || 'Múltiplas Matérias',
    date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Amanhã
    type: 'focused-review',
    priority: 'high',
    questions: reviewData.questions,
    metadata: {
      sessionMode: reviewData.sessionData.mode,
      originalAccuracy: reviewData.sessionData.accuracy
    }
  }
  
  revisions.value.push(revision)
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  loadData()
}

const handleEventSelected = (event) => {
  // Lidar com seleção de evento no calendário
  if (event.type === 'revision') {
    const revision = revisions.value.find(r => `revision-${r.id}` === event.id)
    if (revision) startRevision(revision)
  }
}

const handleEventCreated = (event) => {
  // Criar novo evento no calendário
  const revision = {
    id: `revision-${Date.now()}`,
    title: event.title,
    subject: event.subject,
    date: event.date,
    type: event.type,
    priority: event.priority || 'medium',
    description: event.description
  }
  
  revisions.value.push(revision)
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  updateCalendarEvents()
}

const handleEventUpdated = (event) => {
  // Atualizar evento existente
  const index = revisions.value.findIndex(r => `revision-${r.id}` === event.id)
  if (index > -1) {
    revisions.value[index] = {
      ...revisions.value[index],
      ...event,
      id: revisions.value[index].id // Manter ID original
    }
    localStorage.setItem('revisions', JSON.stringify(revisions.value))
    updateCalendarEvents()
  }
}

const handleEventDeleted = (event) => {
  // Deletar evento
  const index = revisions.value.findIndex(r => `revision-${r.id}` === event.id)
  if (index > -1) {
    revisions.value.splice(index, 1)
    localStorage.setItem('revisions', JSON.stringify(revisions.value))
    updateCalendarEvents()
  }
}

const handleAchievementUnlocked = (achievement) => {
  // Notificar conquista desbloqueada
  console.log('Conquista desbloqueada:', achievement)
  // Implementar notificação visual
}

const handleLevelUp = (newLevel) => {
  // Notificar aumento de nível
  console.log('Parabéns! Você alcançou o nível', newLevel)
  // Implementar notificação visual especial
}

const handleRewardClaimed = (reward) => {
  // Processar recompensa reclamada
  console.log('Recompensa reclamada:', reward)
  // Atualizar pontos/moedas do usuário
}

const handleQuestionsComplete = (result) => {
  // Mark current revision as completed
  if (selectedQuestionContact.value && selectedQuestionContact.value.id) {
    const revision = revisions.value.find(r => r.id === selectedQuestionContact.value.id)
    if (revision) {
      revision.completed = true
      revision.completedAt = new Date().toISOString()
    }
  }
  
  // Save performance
  const performance = {
    ...result,
    id: Date.now(),
    date: new Date().toISOString(),
    completedAt: new Date().toISOString()
  }
  
  performances.value.push(performance)
  localStorage.setItem('performances', JSON.stringify(performances.value))
  
  // Calculate next revision based on performance
  let nextDays
  const percentage = result.percentage || result.performance
  
  if (percentage <= 50) nextDays = 2
  else if (percentage <= 55) nextDays = 7
  else if (percentage <= 60) nextDays = 14
  else if (percentage <= 65) nextDays = 18
  else if (percentage <= 75) nextDays = 24
  else if (percentage <= 80) nextDays = 30
  else nextDays = 35
  
  // Create next revision
  const nextDate = new Date()
  nextDate.setDate(nextDate.getDate() + nextDays)
  
  const nextRevision = {
    id: `revision-${Date.now()}`,
    title: `Revisão: ${result.title || result.scheduling?.topic || selectedQuestionContact.value.title}`,
    subject: result.subject || result.scheduling?.subject || selectedQuestionContact.value.subject,
    date: nextDate.toISOString(),
    type: 'revision',
    priority: percentage < 60 ? 'high' : percentage < 75 ? 'medium' : 'low',
    previousPerformance: percentage,
    metadata: {
      previousRevisionId: selectedQuestionContact.value.id,
      questionCount: result.totalQuestions,
      correctCount: result.correctAnswers || result.correctQuestions
    }
  }
  
  revisions.value.push(nextRevision)
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  
  loadData()
  showQuestionsModal.value = false
  selectedQuestionContact.value = null
}

const handlePerformanceRegistrada = (performance) => {
  // Salvar performance detalhada
  const performances = JSON.parse(localStorage.getItem('performances_detalhadas') || '[]')
  performances.push(performance)
  localStorage.setItem('performances_detalhadas', JSON.stringify(performances))
  
  // Atualizar métricas
  loadData()
}

const handleRevisaoAgendada = (revisao) => {
  // Adicionar revisão agendada
  revisions.value.push({
    ...revisao,
    id: revisao.id || Date.now(),
    status: 'pendente',
    createdAt: new Date().toISOString()
  })
  
  localStorage.setItem('revisions', JSON.stringify(revisions.value))
  
  loadData()
}

// Charts
const updateCharts = () => {
  // Destroy existing charts
  Object.values(charts).forEach(chart => chart.destroy())
  charts = {}
  
  // Subject Progress Chart
  if (subjectChart.value) {
    const ctx = subjectChart.value.getContext('2d')
    const subjectStats = calculateSubjectStats()
    
    charts.subject = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: subjectStats.map(s => s.name),
        datasets: [{
          label: 'Taxa de Acerto',
          data: subjectStats.map(s => s.score),
          backgroundColor: subjectStats.map(s => 
            s.score >= 80 ? 'rgba(16, 185, 129, 0.5)' :
            s.score >= 60 ? 'rgba(59, 130, 246, 0.5)' :
            'rgba(245, 158, 11, 0.5)'
          ),
          borderColor: subjectStats.map(s => 
            s.score >= 80 ? 'rgb(16, 185, 129)' :
            s.score >= 60 ? 'rgb(59, 130, 246)' :
            'rgb(245, 158, 11)'
          ),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }
  
  // Retention Curve Chart
  if (retentionChart.value) {
    const ctx = retentionChart.value.getContext('2d')
    const retentionData = calculateRetentionCurve()
    
    charts.retention = new Chart(ctx, {
      type: 'line',
      data: {
        labels: retentionData.labels,
        datasets: [{
          label: 'Taxa de Retenção',
          data: retentionData.values,
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: value => value + '%'
            }
          }
        }
      }
    })
  }
  
  // Activity Chart
  if (activityChart.value) {
    const ctx = activityChart.value.getContext('2d')
    const activityData = calculateWeeklyActivity()
    
    charts.activity = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
        datasets: [
          {
            label: 'Estudos',
            data: activityData.studies,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
          },
          {
            label: 'Questões',
            data: activityData.questions,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false
        }
      }
    })
  }
  
  // Performance by Type Chart
  if (performanceChart.value) {
    const ctx = performanceChart.value.getContext('2d')
    const perfData = calculatePerformanceByType()
    
    charts.performance = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: perfData.labels,
        datasets: [{
          data: perfData.values,
          backgroundColor: [
            'rgba(59, 130, 246, 0.5)',
            'rgba(16, 185, 129, 0.5)',
            'rgba(245, 158, 11, 0.5)'
          ],
          borderColor: [
            'rgb(59, 130, 246)',
            'rgb(16, 185, 129)',
            'rgb(245, 158, 11)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    })
  }
  
  // Mini Retention Chart
  if (retentionMiniChart.value) {
    const ctx = retentionMiniChart.value.getContext('2d')
    const miniData = calculateMiniRetention()
    
    charts.retentionMini = new Chart(ctx, {
      type: 'line',
      data: {
        labels: miniData.labels,
        datasets: [{
          data: miniData.values,
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          borderWidth: 2,
          pointRadius: 0,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          x: { display: false },
          y: { display: false }
        }
      }
    })
  }
}

const calculateRetentionCurve = () => {
  // Calculate retention over time periods
  const periods = [1, 3, 7, 14, 30]
  const labels = periods.map(d => `${d}d`)
  const values = periods.map(days => {
    const date = new Date()
    date.setDate(date.getDate() - days)
    
    const perfs = performances.value.filter(p => {
      const perfDate = new Date(p.date || p.completedAt)
      return perfDate >= date
    })
    
    if (perfs.length === 0) return 0
    
    return Math.round(
      perfs.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / perfs.length
    )
  })
  
  return { labels, values }
}

const calculateWeeklyActivity = () => {
  const studies = new Array(7).fill(0)
  const questions = new Array(7).fill(0)
  
  const today = new Date()
  const startOfWeek = new Date(today)
  startOfWeek.setDate(today.getDate() - today.getDay() + 1) // Monday
  
  // Count activities for each day of the week
  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)
    const dateStr = date.toDateString()
    
    studies[i] = this.studies.filter(s => 
      new Date(s.data || s.date).toDateString() === dateStr
    ).length
    
    questions[i] = performances.value.filter(p => 
      new Date(p.date || p.completedAt).toDateString() === dateStr
    ).reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
  }
  
  return { studies, questions }
}

const calculatePerformanceByType = () => {
  const types = {
    'Primeira Revisão': 0,
    'Revisão Regular': 0,
    'Questões Rápidas': 0
  }
  
  performances.value.forEach(p => {
    if (p.type === 'questions' || p.isFirstContact) {
      types['Primeira Revisão'] += (p.percentage || p.performance || 0)
    } else if (p.type === 'quick' || p.isQuickSession) {
      types['Questões Rápidas'] += (p.percentage || p.performance || 0)
    } else {
      types['Revisão Regular'] += (p.percentage || p.performance || 0)
    }
  })
  
  // Calculate averages
  const counts = {
    'Primeira Revisão': performances.value.filter(p => p.type === 'questions' || p.isFirstContact).length,
    'Revisão Regular': performances.value.filter(p => !p.type || p.type === 'revision').length,
    'Questões Rápidas': performances.value.filter(p => p.type === 'quick' || p.isQuickSession).length
  }
  
  const labels = []
  const values = []
  
  Object.entries(types).forEach(([label, total]) => {
    if (counts[label] > 0) {
      labels.push(label)
      values.push(Math.round(total / counts[label]))
    }
  })
  
  return { labels, values }
}

const calculateMiniRetention = () => {
  // Last 7 days retention
  const days = 7
  const labels = []
  const values = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.getDate())
    
    const dayPerfs = performances.value.filter(p => 
      new Date(p.date || p.completedAt).toDateString() === date.toDateString()
    )
    
    if (dayPerfs.length > 0) {
      values.push(
        Math.round(dayPerfs.reduce((sum, p) => sum + (p.percentage || p.performance || 0), 0) / dayPerfs.length)
      )
    } else {
      values.push(null)
    }
  }
  
  return { labels, values }
}

// New Section Methods
const openRevisionScheduler = () => {
  showNewRevisionSection.value = false
  showRevisionScheduler.value = true
}

const openRevisionTool = () => {
  showNewRevisionSection.value = false
  showRevisionTool.value = true
}

const openBatchScheduling = () => {
  showNewRevisionSection.value = false
  // Abrir modal de agendamento em lote
  showRevisionScheduler.value = true
  // TODO: Definir modo batch no modal
}

const startRandomQuestions = () => {
  showQuickQuestionsSection.value = false
  // Iniciar questões aleatórias no novo sistema
  showQuickQuestions.value = true
}

const startWeakestTopics = () => {
  showQuickQuestionsSection.value = false
  // Iniciar questões focadas em tópicos fracos
  const weakSubjects = calculateSubjectStats().filter(s => s.score < 60)
  if (weakSubjects.length > 0) {
    selectedQuestionContact.value = {
      title: 'Reforço - Tópicos Fracos',
      subject: weakSubjects[0].name,
      type: 'reinforcement',
      isWeakTopics: true
    }
    showQuickQuestions.value = true
  } else {
    // Se não há tópicos fracos, inicia questões aleatórias
    showQuickQuestions.value = true
  }
}

const findWeakestSubject = () => {
  // Análise de performance por matéria
  const subjectPerformance = {}
  performances.value.forEach(perf => {
    if (perf.subject) {
      if (!subjectPerformance[perf.subject]) {
        subjectPerformance[perf.subject] = { total: 0, correct: 0 }
      }
      subjectPerformance[perf.subject].total += perf.totalQuestions || 0
      subjectPerformance[perf.subject].correct += perf.correctAnswers || 0
    }
  })
  
  let weakest = availableSubjects.value[0]
  let lowestScore = 100
  
  Object.entries(subjectPerformance).forEach(([subject, data]) => {
    const score = data.total > 0 ? (data.correct / data.total) * 100 : 100
    if (score < lowestScore) {
      lowestScore = score
      weakest = subject
    }
  })
  
  return weakest
}

const getSubjectQuestionCount = (subject) => {
  // Conta questões disponíveis por matéria
  return revisions.value.filter(r => r.subject === subject && r.type === 'questions').length * 10
}

// Computed values for quick questions stats
const todayQuestions = computed(() => {
  const today = new Date().toDateString()
  return performances.value.filter(p => new Date(p.date || p.completedAt).toDateString() === today)
    .reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
})

// currentStreak já está definido anteriormente no arquivo

const weeklyGoalProgress = computed(() => {
  const weekStart = new Date()
  weekStart.setDate(weekStart.getDate() - weekStart.getDay())
  
  const weekQuestions = performances.value
    .filter(p => new Date(p.date || p.completedAt) >= weekStart)
    .reduce((sum, p) => sum + (p.totalQuestions || 0), 0)
  
  const weeklyGoal = 200 // Meta de 200 questões por semana
  return Math.min(100, Math.round((weekQuestions / weeklyGoal) * 100))
})

// Animações e transições
const animateElements = () => {
  // Adicionar classes de animação dinamicamente
  const elements = document.querySelectorAll('.animated')
  elements.forEach((el, index) => {
    setTimeout(() => {
      el.classList.add('fadeInUp')
    }, index * 100)
  })
}

// Lifecycle
onMounted(() => {
  // Importar arquivo de animações
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  link.href = '/src/styles/animations.css'
  document.head.appendChild(link)
  
  loadData()
  
  // Watch for analytics visibility to update charts
  watch(showAnalytics, (newVal) => {
    if (newVal) {
      nextTick(() => updateCharts())
    }
  })
  
  // Update charts when stats period changes
  watch(statsPeriod, () => {
    if (showAnalytics.value) {
      nextTick(() => updateCharts())
    }
  })
})

onUnmounted(() => {
  // Cleanup charts
  Object.values(charts).forEach(chart => chart.destroy())
})
</script>

<style scoped>
/* Base Layout */
.revisoes-unified {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0e17 0%, #1a1a2e 40%, #16213e 100%);
  color: #fffffe;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #667eea 0%, transparent 70%);
  top: -200px;
  left: -200px;
}

.orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #764ba2 0%, transparent 70%);
  bottom: -100px;
  right: -100px;
  animation-delay: -5s;
}

.orb-3 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #f093fb 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.05); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Header */
.revisoes-header {
  position: relative;
  z-index: 10;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 2rem 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1rem;
  color: #94a3b8;
  margin: 0.25rem 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 1rem 2rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.button-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.action-button.primary:hover .button-glow {
  width: 300%;
  height: 300%;
}

.action-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.action-button.primary:hover {
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

.action-button.primary:hover::before {
  left: 100%;
}

/* Stats Overview */
.stats-overview {
  max-width: 1400px;
  margin: 2rem auto 0;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: rgba(148, 163, 184, 0.2);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-icon.studies {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.stat-icon.questions {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stat-icon.performance {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.stat-icon.upcoming {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #f1f5f9;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-trend.positive {
  color: #10b981;
}

.stat-trend.negative {
  color: #ef4444;
}

.stat-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Main Content */
.revisoes-content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Sections */
.analytics-section,
.calendar-section,
.new-revision-section,
.quick-questions-section {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #f1f5f9;
}

.collapse-btn {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.chart-container {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  min-height: 300px;
}

.chart-container h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #e2e8f0;
}

.chart-container canvas {
  max-height: 250px;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.action-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.action-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
}

.action-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.action-card p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

/* Tabs */
.content-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding-bottom: 1rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  white-space: nowrap;
}

.tab-button:hover {
  color: #e2e8f0;
}

.tab-button.active {
  color: #667eea;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.tab-badge {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Tab Content */
.tab-content {
  min-height: 400px;
}

/* List Header */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.list-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #f1f5f9;
}

.list-filters {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-select:hover,
.filter-select:focus {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(148, 163, 184, 0.2);
  outline: none;
}

.export-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

/* Revisions List */
.revision-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.revision-card:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.revision-card.overdue {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.05);
}

.revision-card.today {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.revision-card.urgent {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.revision-card.soon {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.revision-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.revision-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.revision-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.subject-tag,
.date-tag,
.type-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.revision-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.priority-high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.priority-medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.priority-low {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.revision-progress {
  margin-bottom: 1rem;
}

.progress-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill.excellent {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-fill.good {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.progress-fill.needs-improvement {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.revision-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
}

.action-btn.primary:hover {
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #94a3b8;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #e2e8f0;
}

.empty-state p {
  margin: 0 0 2rem;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.empty-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

/* History Timeline */
.history-timeline {
  position: relative;
  padding-left: 2rem;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #667eea, transparent);
}

.history-item {
  position: relative;
  padding-bottom: 2rem;
  cursor: pointer;
}

.timeline-marker {
  position: absolute;
  left: -1.25rem;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: #1e293b;
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #667eea;
  transition: all 0.3s;
}

.timeline-marker.excellent {
  border-color: #10b981;
  color: #10b981;
}

.timeline-marker.good {
  border-color: #3b82f6;
  color: #3b82f6;
}

.timeline-marker.needs-improvement {
  border-color: #f59e0b;
  color: #f59e0b;
}

.history-content {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  margin-left: 1rem;
  transition: all 0.3s;
}

.history-item:hover .history-content {
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.history-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #f1f5f9;
}

.history-date {
  font-size: 0.875rem;
  color: #64748b;
}

.history-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.history-details span {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
}

.performance {
  font-weight: 600;
}

.performance.excellent {
  color: #10b981;
}

.performance.good {
  color: #3b82f6;
}

.performance.needs-improvement {
  color: #f59e0b;
}

.duration,
.questions {
  color: #94a3b8;
}

.recommendations {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.recommendations p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendations i {
  color: #f59e0b;
}

.next-revision-info {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  font-size: 0.875rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Stats Dashboard */
.stats-period-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.period-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.period-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.period-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: transparent;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.stat-box {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.stat-box.wide {
  grid-column: span 2;
}

@media (max-width: 768px) {
  .stat-box.wide {
    grid-column: span 1;
  }
}

.stat-box:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stat-box h3 {
  font-size: 1rem;
  font-weight: 500;
  color: #94a3b8;
  margin: 0;
}

.stat-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.big-number {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-chart {
  height: 60px;
  margin: 1rem 0;
}

.stat-box > p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.streak-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin: 1rem 0;
  padding: 0 2rem;
}

.streak-day {
  aspect-ratio: 1;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  transition: all 0.2s;
}

.streak-day.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.streak-day.today {
  border: 2px solid #667eea;
}

.time-breakdown {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 1rem 0;
}

.time-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time-label {
  font-size: 0.875rem;
  color: #64748b;
}

.time-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e2e8f0;
}

.subject-ranking {
  margin: 1rem 0;
}

.subject-rank {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(148, 163, 184, 0.05);
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.rank-name {
  flex: 1;
  font-size: 0.875rem;
  color: #e2e8f0;
}

.rank-score {
  font-weight: 600;
  color: #667eea;
}

.performance-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
  margin: 1.5rem 0;
}

.matrix-cell {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.matrix-cell:hover {
  transform: scale(1.05);
}

.matrix-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.insights-list {
  text-align: left;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(148, 163, 184, 0.05);
  border-radius: 12px;
  margin-bottom: 1rem;
  transition: all 0.2s;
}

.insight-item:hover {
  background: rgba(148, 163, 184, 0.1);
}

.insight-item i {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: #f1f5f9;
}

.insight-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.75rem;
  line-height: 1.5;
}

.insight-action {
  padding: 0.375rem 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.insight-action:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.quick-questions-modal,
.history-details-modal {
  background: #0f172a;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  border: 1px solid #1e293b;
}

.modal-header {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  padding: 24px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #1e293b;
}

.modal-header h2 {
  color: #f1f5f9;
  font-size: 24px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.close-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

.subject-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.subject-btn {
  padding: 2rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.subject-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

.subject-btn i {
  font-size: 2rem;
  color: #667eea;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.detail-date {
  font-size: 0.875rem;
  color: #64748b;
}

.detail-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-stat {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #667eea;
}

.question-analysis {
  margin-bottom: 2rem;
}

.question-analysis h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #f1f5f9;
}

.question-type-stats {
  space-y: 1rem;
}

.type-stat {
  margin-bottom: 1rem;
}

.type-name {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.type-bar {
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.type-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.type-score {
  font-size: 0.875rem;
  color: #e2e8f0;
  font-weight: 500;
}

.topics-covered h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #f1f5f9;
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.topic-tag {
  padding: 0.375rem 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  color: #667eea;
  font-size: 0.875rem;
}

/* Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.fab-main {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.5);
}

.fab-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fab-item {
  width: 48px;
  height: 48px;
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 50%;
  color: #e2e8f0;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.fab-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateX(-4px);
}

/* Animations */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(10px);
  opacity: 0;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.fab-menu-enter-active,
.fab-menu-leave-active {
  transition: all 0.3s ease;
}

.fab-menu-enter-from,
.fab-menu-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .time-breakdown {
    gap: 1rem;
  }
  
  .performance-matrix {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .fab-container {
    bottom: 1rem;
    right: 1rem;
  }
}

/* New Revision Section */
.revision-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.option-card {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.option-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.option-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
}

.option-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #f1f5f9;
}

.option-card p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

/* Quick Questions Section */
.questions-content {
  margin-top: 2rem;
}

.questions-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-box {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-box i {
  font-size: 2rem;
  color: #3b82f6;
}

.stat-box div {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: #f1f5f9;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.subject-selection {
  margin: 2rem 0;
}

.subject-selection h3 {
  font-size: 1.25rem;
  color: #f1f5f9;
  margin-bottom: 1.5rem;
}

.subject-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.subject-card {
  background: rgba(30, 41, 59, 0.8);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.subject-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.subject-card i {
  font-size: 2rem;
  color: #3b82f6;
}

.subject-name {
  font-weight: 600;
  color: #f1f5f9;
}

.subject-count {
  font-size: 0.75rem;
  color: #94a3b8;
}

.quick-actions-bottom {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.action-btn.secondary:hover {
  background: rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}
/* Gamification Trigger */
.gamification-trigger {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  z-index: 100;
}

.gamification-trigger:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.gamification-trigger i {
  font-size: 1.5rem;
}

.level-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Analytics Modal */
.analytics-modal {
  background: white;
  border-radius: 20px;
  width: 95%;
  max-width: 1400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* Calendar Modal */
.calendar-modal {
  background: white;
  border-radius: 20px;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

.fadeInUp {
  animation-name: fadeInUp;
}

/* Card Animations */
.action-card {
  position: relative;
  overflow: hidden;
}

.icon-pulse {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
}

.card-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover .card-glow {
  opacity: 1;
}

/* Modal Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .analytics-modal,
.modal-fade-enter-active .calendar-modal {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .analytics-modal,
.modal-fade-enter-from .calendar-modal {
  transform: scale(0.9);
}

/* Slide Up Transition */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* Enhanced Calendar Button */
.calendar-btn.enhanced,
.analytics-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calendar-btn.enhanced:hover,
.analytics-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}
</style>