<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Sistema de Revisões</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #1a1a2e;
            color: #eee;
            padding: 20px;
            margin: 0;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #16213e;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        h1 {
            color: #6366f1;
            margin-bottom: 30px;
        }
        .test-section {
            background: #0f3460;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #1e3a8a;
        }
        .test-section h2 {
            color: #60a5fa;
            margin-top: 0;
        }
        .status {
            padding: 10px 20px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #065f46;
            color: #10b981;
            border: 1px solid #10b981;
        }
        .error {
            background: #7f1d1d;
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .warning {
            background: #78350f;
            color: #fbbf24;
            border: 1px solid #fbbf24;
        }
        pre {
            background: #1e293b;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #334155;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #4f46e5;
        }
        .test-results {
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 8px;
            background: #1e293b;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Debug - Sistema de Revisões Inteligente</h1>
        
        <div class="test-section">
            <h2>1. Verificação de Componentes</h2>
            <div id="component-check"></div>
        </div>

        <div class="test-section">
            <h2>2. Teste de LocalStorage</h2>
            <button onclick="testLocalStorage()">Testar LocalStorage</button>
            <div id="localstorage-result"></div>
        </div>

        <div class="test-section">
            <h2>3. Simulação de Registro de Estudo</h2>
            <button onclick="simulateStudyRegistration()">Simular Registro</button>
            <div id="study-result"></div>
        </div>

        <div class="test-section">
            <h2>4. Simulação de Questões</h2>
            <button onclick="simulateQuestions()">Simular Questões (80% acerto)</button>
            <button onclick="simulateQuestions(60)">Simular Questões (60% acerto)</button>
            <button onclick="simulateQuestions(40)">Simular Questões (40% acerto)</button>
            <div id="questions-result"></div>
        </div>

        <div class="test-section">
            <h2>5. Dados Salvos</h2>
            <button onclick="showSavedData()">Mostrar Dados Salvos</button>
            <button onclick="clearAllData()">Limpar Todos os Dados</button>
            <div id="saved-data"></div>
        </div>

        <div class="test-section">
            <h2>6. Log de Debug</h2>
            <div id="debug-log"></div>
        </div>
    </div>

    <script>
        // Logger
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.style.color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#60a5fa';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
        }

        // Component Check
        function checkComponents() {
            const result = document.getElementById('component-check');
            const components = [
                'NewRevisionModal.vue',
                'QuestionsModal.vue',
                'StudyPlanPageFinal.vue',
                'SimpleCalendar.vue'
            ];
            
            result.innerHTML = '<h3>Componentes Necessários:</h3>';
            components.forEach(comp => {
                result.innerHTML += `<div class="status success">✓ ${comp} - Integrado</div>`;
            });
            
            log('Verificação de componentes concluída', 'success');
        }

        // LocalStorage Test
        function testLocalStorage() {
            const result = document.getElementById('localstorage-result');
            try {
                localStorage.setItem('test', 'debug');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (value === 'debug') {
                    result.innerHTML = '<div class="status success">✓ LocalStorage funcionando corretamente</div>';
                    log('LocalStorage testado com sucesso', 'success');
                } else {
                    throw new Error('Valor não corresponde');
                }
            } catch (e) {
                result.innerHTML = `<div class="status error">✗ Erro no LocalStorage: ${e.message}</div>`;
                log(`Erro no LocalStorage: ${e.message}`, 'error');
            }
        }

        // Simulate Study Registration
        function simulateStudyRegistration() {
            const result = document.getElementById('study-result');
            
            const study = {
                id: Date.now(),
                title: 'Sistema Cardiovascular - Anatomia',
                subject: 'Anatomia',
                date: new Date().toISOString().split('T')[0],
                difficulty: 'Difícil',
                duration: 120,
                notes: 'Estudo completo do sistema cardiovascular',
                type: 'theoretical',
                scheduling: {
                    firstRevisionDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
                    revisionType: 'questions',
                    baseInterval: 1,
                    currentCycle: 0,
                    metadata: {
                        studyDuration: 120,
                        difficulty: 'Difícil',
                        subject: 'Anatomia',
                        createdAt: new Date(),
                        lastModified: new Date()
                    }
                }
            };
            
            try {
                const studies = JSON.parse(localStorage.getItem('studies') || '[]');
                studies.push(study);
                localStorage.setItem('studies', JSON.stringify(studies));
                
                result.innerHTML = `
                    <div class="status success">✓ Estudo registrado com sucesso!</div>
                    <pre>${JSON.stringify(study, null, 2)}</pre>
                `;
                log('Estudo teórico registrado', 'success');
            } catch (e) {
                result.innerHTML = `<div class="status error">✗ Erro ao registrar: ${e.message}</div>`;
                log(`Erro ao registrar estudo: ${e.message}`, 'error');
            }
        }

        // Simulate Questions
        function simulateQuestions(percentage = 80) {
            const result = document.getElementById('questions-result');
            const total = 30;
            const correct = Math.round(total * percentage / 100);
            
            // Calculate next revision
            let baseInterval;
            if (percentage >= 90) baseInterval = 30;
            else if (percentage >= 85) baseInterval = 21;
            else if (percentage >= 80) baseInterval = 14;
            else if (percentage >= 75) baseInterval = 10;
            else if (percentage >= 70) baseInterval = 7;
            else if (percentage >= 65) baseInterval = 5;
            else if (percentage >= 60) baseInterval = 3;
            else if (percentage >= 55) baseInterval = 2;
            else baseInterval = 1;
            
            const questionData = {
                id: Date.now(),
                title: 'Sistema Cardiovascular - Questões',
                subject: 'Anatomia',
                date: new Date().toISOString().split('T')[0],
                totalQuestions: total,
                correctAnswers: correct,
                wrongAnswers: total - correct,
                percentage: percentage,
                averageTime: 90,
                type: 'questions',
                analysis: {
                    confidence: 85,
                    nextRevisionDays: baseInterval,
                    recommendations: percentage < 60 ? ['Revisar conceitos fundamentais', 'Fazer mais exercícios'] : []
                },
                scheduling: {
                    nextRevisionDate: new Date(Date.now() + baseInterval * 24 * 60 * 60 * 1000),
                    priority: percentage < 60 ? 'high' : percentage < 75 ? 'medium' : 'low'
                }
            };
            
            try {
                const performances = JSON.parse(localStorage.getItem('performances') || '[]');
                performances.push(questionData);
                localStorage.setItem('performances', JSON.stringify(performances));
                
                result.innerHTML = `
                    <div class="status ${percentage >= 60 ? 'success' : 'warning'}">
                        ${percentage >= 60 ? '✓' : '⚠'} Questões registradas: ${correct}/${total} (${percentage}%)
                    </div>
                    <div class="status">Próxima revisão em: ${baseInterval} dias</div>
                    <pre>${JSON.stringify(questionData, null, 2)}</pre>
                `;
                log(`Questões registradas: ${percentage}% de acerto`, percentage >= 60 ? 'success' : 'info');
            } catch (e) {
                result.innerHTML = `<div class="status error">✗ Erro ao registrar: ${e.message}</div>`;
                log(`Erro ao registrar questões: ${e.message}`, 'error');
            }
        }

        // Show Saved Data
        function showSavedData() {
            const result = document.getElementById('saved-data');
            
            try {
                const studies = JSON.parse(localStorage.getItem('studies') || '[]');
                const performances = JSON.parse(localStorage.getItem('performances') || '[]');
                
                result.innerHTML = `
                    <h3>Estudos Teóricos (${studies.length})</h3>
                    <pre>${JSON.stringify(studies, null, 2)}</pre>
                    <h3>Performances em Questões (${performances.length})</h3>
                    <pre>${JSON.stringify(performances, null, 2)}</pre>
                `;
                log(`Dados carregados: ${studies.length} estudos, ${performances.length} performances`, 'info');
            } catch (e) {
                result.innerHTML = `<div class="status error">✗ Erro ao carregar dados: ${e.message}</div>`;
                log(`Erro ao carregar dados: ${e.message}`, 'error');
            }
        }

        // Clear All Data
        function clearAllData() {
            if (confirm('Tem certeza que deseja limpar todos os dados?')) {
                localStorage.removeItem('studies');
                localStorage.removeItem('performances');
                document.getElementById('saved-data').innerHTML = '<div class="status warning">⚠ Todos os dados foram limpos</div>';
                log('Todos os dados foram limpos', 'warning');
            }
        }

        // Initialize
        window.onload = function() {
            log('Sistema de debug iniciado', 'info');
            checkComponents();
        };
    </script>
</body>
</html>