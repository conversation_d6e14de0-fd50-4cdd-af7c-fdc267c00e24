# 🔍 DEBUG CHECKLIST - Sistema de Revisões

## ✅ Verificações Necessárias

### 1. **Componentes Vue**
- [x] NewRevisionModal.vue existe e está funcional
- [x] QuestionsModal.vue existe e está funcional
- [x] Componentes importados em StudyPlanPageFinal.vue
- [x] Variáveis de estado definidas (showRevisionModal, showQuestionsModal)

### 2. **Eventos e Handlers**
- [x] Botão com @click="showRevisionModal = true"
- [x] Handlers definidos (handleRevisionSave, handleStudySave, etc.)
- [x] Handlers incluídos no return do setup()

### 3. **CSS e Estilos**
- [x] Classes .revision-btn e .header-actions definidas
- [x] Estilos aplicados corretamente

### 4. **Possíveis Problemas**

#### ❌ **Problema 1: Modal não abre**
**Sintomas:**
- <PERSON>lique no botão não faz nada
- Sem erros no console

**Verificar:**
```javascript
// No console do navegador:
console.log(showRevisionModal) // Deve mostrar o valor atual
```

**Soluções:**
1. Verificar se o v-if está correto no modal
2. Verificar se showRevisionModal está no return
3. Verificar z-index do modal

#### ❌ **Problema 2: Erro de importação**
**Sintomas:**
- Erro "Failed to resolve component"

**Verificar:**
```javascript
// Caminho correto dos imports
import NewRevisionModal from './NewRevisionModal.vue'
```

**Soluções:**
1. Verificar caminhos de importação
2. Verificar nomes dos componentes
3. Reiniciar servidor de desenvolvimento

#### ❌ **Problema 3: LocalStorage não salva**
**Sintomas:**
- Dados não persistem após recarregar

**Verificar:**
```javascript
// No console:
localStorage.getItem('studies')
localStorage.getItem('performances')
```

**Soluções:**
1. Verificar permissões do navegador
2. Verificar se está em modo privado
3. Limpar cache e cookies

## 🛠️ Como Debugar

### 1. **Abrir Console do Navegador (F12)**
```javascript
// Verificar se componente está montado
console.log('Componente montado')

// Verificar estado do modal
console.log(showRevisionModal.value)

// Forçar abertura do modal
showRevisionModal.value = true
```

### 2. **Vue DevTools**
1. Instalar extensão Vue DevTools
2. Inspecionar componente StudyPlanPageFinal
3. Verificar data e props
4. Verificar eventos emitidos

### 3. **Network Tab**
1. Verificar se arquivos .vue carregam
2. Verificar erros 404
3. Verificar se há bloqueios CORS

### 4. **Teste Manual**
1. Abrir DEBUG_REVISION_SYSTEM.html no navegador
2. Testar cada funcionalidade
3. Verificar logs de debug

## 📝 Log de Resolução

### Passo 1: Identificar o problema
- [ ] Modal não abre
- [ ] Erro no console
- [ ] Dados não salvam
- [ ] Outro: ___________

### Passo 2: Coletar informações
- [ ] Screenshot do erro
- [ ] Console logs
- [ ] Network logs
- [ ] Vue DevTools info

### Passo 3: Aplicar solução
- [ ] Corrigir código
- [ ] Testar correção
- [ ] Validar funcionamento

## 🚀 Comando Rápido de Debug

```bash
# No terminal, na pasta frontend:
npm run serve -- --mode development

# Verificar erros de compilação
npm run lint

# Limpar cache
rm -rf node_modules/.cache
npm run serve
```

## 📞 Suporte

Se o problema persistir:
1. Verificar versão do Vue (deve ser 3.x)
2. Verificar dependências: `npm list vue`
3. Reinstalar dependências: `rm -rf node_modules && npm install`