# 🧠 Sistema de Revisões Espaçadas - Documentação

## Visão Geral

O Sistema de Revisões Espaçadas implementado segue rigorosamente a metodologia científica descrita em `@Resources/Lógicas/MétododeRevisõesEspaçadas.txt`, dividida em três partes principais:

## 🔹 Primeira Parte: Registro do Estudo Teórico

### Funcionamento:
1. **Classificação Subjetiva**: O aluno classifica o conteúdo estudado como:
   - **Fácil**: Primeiro contato agendado para **2 dias** após o estudo
   - **Difícil**: Primeiro contato agendado para **1 dia** após o estudo

### Lógica Implementada:
```javascript
const diasParaAdicionar = estudoTeorico.dificuldade === 'Fácil' ? 2 : 1
const dataPrimeiroContato = new Date(estudoTeorico.data)
dataPrimeiroContato.setDate(dataPrimeiroContato.getDate() + diasParaAdicionar)
```

## 🔹 Segunda Parte: Revisões Práticas com Questões

### Funcionamento:
1. **Blocos de Questões**: 20-30 questões (ideal: 30)
2. **Agendamento Baseado em Desempenho**:

| Percentual de Acerto | Próxima Revisão |
|---------------------|-----------------|
| ≤ 50%               | 2 dias         |
| 50-55%              | 7 dias         |
| 55-60%              | 14 dias        |
| 60-65%              | 18 dias        |
| 65-75%              | 24 dias        |
| 75-80%              | 30 dias        |
| > 80%               | 35 dias        |

### Lógica Implementada:
```javascript
if (percentual <= 50) dias = 2
else if (percentual <= 55) dias = 7
else if (percentual <= 60) dias = 14
else if (percentual <= 65) dias = 18
else if (percentual <= 75) dias = 24
else if (percentual <= 80) dias = 30
else dias = 35
```

## 🔹 Terceira Parte: Automatização do Calendário

### Funcionamento:
- Todas as revisões são automaticamente adicionadas ao calendário
- Priorização baseada no desempenho:
  - **Alta prioridade**: < 60% de acerto
  - **Média prioridade**: 60-75% de acerto
  - **Baixa prioridade**: > 75% de acerto

## 📋 Fluxo Completo do Sistema

```mermaid
graph TD
    A[Estudo Teórico] --> B{Classificação}
    B -->|Fácil| C[Primeiro Contato em 2 dias]
    B -->|Difícil| D[Primeiro Contato em 1 dia]
    C --> E[Realizar 20-30 Questões]
    D --> E
    E --> F{Desempenho}
    F -->|≤50%| G[Próxima em 2 dias]
    F -->|50-55%| H[Próxima em 7 dias]
    F -->|55-60%| I[Próxima em 14 dias]
    F -->|60-65%| J[Próxima em 18 dias]
    F -->|65-75%| K[Próxima em 24 dias]
    F -->|75-80%| L[Próxima em 30 dias]
    F -->|>80%| M[Próxima em 35 dias]
    G --> N[Adicionar ao Calendário]
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
```

## 🎯 Componentes Implementados

### 1. **RevisionToolWindow.vue**
- Interface principal com 3 abas:
  - **📚 Estudo Teórico**: Registro de novos estudos
  - **✍️ Revisão Prática**: Registro de desempenho em questões
  - **📊 Histórico**: Visualização completa do progresso

### 2. **RevisoesView.vue**
- Página principal do sistema
- Integração com calendário
- Dashboard de estatísticas
- Gestão de revisões pendentes

## 💾 Armazenamento de Dados

### LocalStorage Keys:
- `estudos_teoricos`: Array de estudos registrados
- `revisoes_praticas`: Array de performances em questões
- `revisions`: Array de revisões agendadas

### Estrutura dos Dados:

#### Estudo Teórico:
```javascript
{
  id: timestamp,
  titulo: string,
  materia: string,
  data: ISO date,
  tempo: number (minutos),
  dificuldade: 'Fácil' | 'Difícil',
  notas: string,
  dataPrimeiroContato: ISO date
}
```

#### Revisão Prática:
```javascript
{
  id: timestamp,
  estudoId: number,
  titulo: string,
  materia: string,
  data: ISO date,
  totalQuestoes: number,
  acertos: number,
  percentual: number,
  proximaRevisaoDias: number,
  proximaRevisaoData: ISO date
}
```

## 🚀 Como Usar

1. **Registrar Novo Estudo**:
   - Clique em "+ Nova Revisão"
   - Preencha os dados do estudo teórico
   - Classifique como Fácil ou Difícil
   - O sistema agendará automaticamente o primeiro contato

2. **Realizar Revisão Prática**:
   - Acesse a aba "Revisão Prática"
   - Selecione o conteúdo a revisar
   - Registre o desempenho nas questões
   - O sistema calculará e agendará a próxima revisão

3. **Acompanhar Progresso**:
   - Visualize o histórico completo
   - Analise estatísticas de desempenho
   - Acompanhe revisões pendentes no calendário

## 📈 Benefícios do Sistema

1. **Baseado em Evidências**: Segue a curva de esquecimento de Ebbinghaus
2. **Personalizado**: Adapta-se ao desempenho individual
3. **Automatizado**: Elimina a necessidade de cálculos manuais
4. **Integrado**: Funciona em conjunto com o calendário de estudos
5. **Rastreável**: Mantém histórico completo de progresso

## 🔧 Manutenção

Para modificar os intervalos de revisão, edite a função no arquivo `RevisionToolWindow.vue`:

```javascript
const proximaRevisaoCalculada = computed(() => {
  // Ajuste os valores conforme necessário
})
```

---

**Desenvolvido seguindo rigorosamente o método descrito em `@Resources/Lógicas/MétododeRevisõesEspaçadas.txt`**